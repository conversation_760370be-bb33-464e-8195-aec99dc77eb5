<!-- 文件上传对话框 -->
<div id="uploadModal" class="indicator-modal">
    <div class="indicator-content">
        <span class="close" onclick="closeUploadModal()">&times;</span>
        <h2>概算文件导入</h2>
        <div class="upload-area">
            <input type="file" id="fileInput" style="display: none;">
            <div class="upload-box" onclick="document.getElementById('fileInput').click()">
                <i class="fas fa-cloud-upload-alt"></i>
                <p>点击或拖拽工程概算Excel文件到此处，完成文件上传</p>
            </div>
        </div>
        <div id="uploadProgress" style="display: none;">
            <p class="success-message"><i class="fas fa-check-circle"></i> 文件上传成功！</p>
        </div>
    </div>
</div>

<!-- 指标预览对话框 -->
<div id="indicatorPreviewModal" class="indicator-modal">
    <div class="indicator-content">
        <span class="close" onclick="closeModal('indicatorPreviewModal')">&times;</span>
        <h2>指标数据预览</h2>
        <div class="preview-actions" style="margin-bottom: 15px;">
            <button id="editIndicatorsBtn" class="btn btn-primary" style="margin-right: 10px;" onclick="toggleEditMode()">编辑</button>
            <button id="exportIndicatorsBtn" class="btn btn-success" onclick="exportIndicators()">导出</button>
        </div>
        <div class="preview-tabs">
            <button class="preview-tab active" onclick="switchTab('preview', 'benti')">本体费用指标</button>
            <button class="preview-tab" onclick="switchTab('preview', 'qita')">其他费用指标</button>
        </div>
        <div id="previewBentiPanel" class="preview-panel active">
            <div class="indicator-table-wrapper">
                <table class="indicator-table preview-table">
                    <thead>
                        <tr>
                            <th style="text-align: center;">序号</th>
                            <th>本体费用指标名称</th>
                            <th>单位</th>
                            <th>指标值</th>
                        </tr>
                    </thead>
                    <tbody id="previewBentiTableBody">
                        <!-- 将通过JavaScript动态填充 -->
                    </tbody>
                </table>
            </div>
        </div>
        <div id="previewQitaPanel" class="preview-panel">
            <div class="indicator-table-wrapper">
                <table class="indicator-table preview-table">
                    <thead>
                        <tr>
                            <th style="text-align: center;">序号</th>
                            <th>其他费用指标名称</th>
                            <th>单位</th>
                            <th>指标值</th>
                        </tr>
                    </thead>
                    <tbody id="previewQitaTableBody">
                        <!-- 将通过JavaScript动态填充 -->
                    </tbody>
                </table>
            </div>
        </div>
        <div style="margin-top: 20px; text-align: right;">
            <button class="btn btn-secondary" onclick="closeModal('indicatorPreviewModal')">关闭</button>
        </div>
    </div>
</div>

<!-- 指标提取进度对话框 -->
<div id="extractProgressModal" class="indicator-modal">
    <div class="indicator-content">
        <span class="close" onclick="closeProgressModal('extractProgressModal')">&times;</span>
        <h2 id="progressTitle">指标提取进度</h2>
        <div class="progress-content">
            <div id="extractProgressMessages" class="progress-messages"></div>
        </div>
        <div class="modal-footer" style="margin-top: 20px; text-align: right;">
            <button id="extractPreviewButton" class="btn btn-primary" style="display: none; margin-right: 10px;" onclick="handleExtractPreviewClick()">指标预览</button>
            <button class="btn btn-secondary" onclick="closeProgressModal('extractProgressModal')">关闭</button>
        </div>
    </div>
</div>

<script>
// 文件上传相关函数
function showUploadModal(projectId) {
    const modal = document.getElementById('uploadModal');
    modal.dataset.projectId = projectId;
    modal.classList.add('show');
    document.getElementById('uploadProgress').style.display = 'none';
    document.getElementById('fileInput').value = '';
}

function closeUploadModal() {
    const modal = document.getElementById('uploadModal');
    modal.classList.remove('show');
}

document.getElementById('fileInput').addEventListener('change', async function(e) {
    if (e.target.files.length > 0) {
        const modal = document.getElementById('uploadModal');
        const projectId = modal.dataset.projectId;
        
        // 显示上传成功消息
        document.getElementById('uploadProgress').style.display = 'block';
        
        // 更新项目状态
        await updateProjectStatus(projectId, {
            导入状态: '已导入'
        });
        
        // 3秒后自动关闭
        setTimeout(() => {
            closeUploadModal();
        }, 3000);
    }
});

// 指标提取相关函数
async function showExtractIndicatorModal(projectId) {
    const modal = document.getElementById('extractProgressModal');
    const title = document.getElementById('progressTitle');
    const messagesContainer = document.getElementById('extractProgressMessages');
    const previewButton = document.getElementById('extractPreviewButton');
    
    // 清空之前的消息
    messagesContainer.innerHTML = '';
    
    // 隐藏预览按钮
    previewButton.style.display = 'none';
    
    // 设置标题
    title.textContent = '指标提取进度';
    
    // 显示对话框
    modal.classList.add('show');

    // 指标提取流程
    addProgressMessage(messagesContainer, '指标提取开始');
    
    // 本体费用指标提取
    addProgressMessage(messagesContainer, '本体费用指标开始提取');
    for (const indicator of bentiIndicators) {
        await new Promise(resolve => setTimeout(resolve, 100));
        addProgressMessage(messagesContainer, `提取本体费用指标：${indicator.name}，提取完成`);
    }
    addProgressMessage(messagesContainer, '本体费用指标提取完成');
    
    // 其他费用指标提取
    addProgressMessage(messagesContainer, '其他费用指标开始提取');
    for (const indicator of qitaIndicators) {
        await new Promise(resolve => setTimeout(resolve, 100));
        addProgressMessage(messagesContainer, `提取其他费用指标：${indicator.name}，提取完成`);
    }
    addProgressMessage(messagesContainer, '其他费用指标提取完成');
    
    try {
        const response = await fetch(`/api/projects/${projectId}/extract`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            previewButton.textContent = '指标预览';
            previewButton.style.display = 'inline-block';
            await loadProjectData();
            modal.dataset.projectId = projectId;
            addProgressMessage(messagesContainer, '所有指标提取完成，请点击"指标预览"查看或编辑相关数据');
        } else {
            console.error('更新指标提取状态失败');
        }
    } catch (error) {
        console.error('调用API失败:', error);
    }
}

// 添加进度消息的辅助函数
function addProgressMessage(container, message, isSuccess = true) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'progress-message' + (isSuccess ? ' success' : '');
    messageDiv.innerHTML = `<i class="fas fa-${isSuccess ? 'check-circle' : 'spinner fa-spin'}"></i>${message}`;
    container.appendChild(messageDiv);
    // 自动滚动到底部
    container.scrollTop = container.scrollHeight;
}

// 指标预览相关函数
async function showIndicatorPreview(projectId) {
    const modal = document.getElementById('indicatorPreviewModal');
    
    // 显示对话框
    modal.classList.add('show');
    
    try {
        // 从后端获取指标数据
        const [bentiData, qitaData] = await Promise.all([
            fetch('/api/indicators/benti').then(res => res.json()),
            fetch('/api/indicators/qita').then(res => res.json())
        ]);
        
        // 获取第一行数据
        const bentiValues = bentiData[0];
        const qitaValues = qitaData[0];
        
        const previewBentiPanel = document.getElementById('previewBentiPanel');
        const previewQitaPanel = document.getElementById('previewQitaPanel');
        
        // 生成本体费用指标预览表格
        previewBentiPanel.innerHTML = generatePreviewTable(bentiIndicators, '本体费用', bentiValues);
        
        // 生成其他费用指标预览表格
        previewQitaPanel.innerHTML = generatePreviewTable(qitaIndicators, '其他费用', qitaValues);
        
        // 默认显示本体费用面板
        switchTab('preview', 'benti');
        
    } catch (error) {
        console.error('加载指标预览数据失败:', error);
    }
}

// 生成预览表格的辅助函数
function generatePreviewTable(indicators, type, values) {
    let html = `
        <div class="indicator-table-wrapper">
            <table class="indicator-table preview-table">
                <thead>
                    <tr>
                        <th style="text-align: center;">序号</th>
                        <th>${type}指标名称</th>
                        <th>单位</th>
                        <th>指标值</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    indicators.forEach((indicator, index) => {
        // 构建JSON字段名
        const fieldName = `${indicator.name}(${indicator.unit})`;
        // 获取对应的值
        let value = '--';
        if (type === '本体费用') {
            value = values[`${indicator.name}(${indicator.unit})`] || values[`${indicator.name}（${indicator.unit}）`] || '--';
        } else {
            value = values[`${indicator.name}（${indicator.unit}）`] || '--';
        }
        
        html += `
            <tr>
                <td style="text-align: center;">${index + 1}</td>
                <td>${indicator.name}</td>
                <td>${indicator.unit}</td>
                <td class="indicator-value" data-id="${indicator.id}" style="text-align: right;">${value}</td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
            </table>
        </div>
    `;
    
    return html;
}

// 切换编辑模式
function toggleEditMode() {
    const editBtn = document.getElementById('editIndicatorsBtn');
    const values = document.querySelectorAll('.indicator-value');
    const isEditing = editBtn.textContent === '保存';
    
    if (isEditing) {
        // 保存编辑
        values.forEach(td => {
            const input = td.querySelector('input');
            if (input) {
                td.textContent = input.value;
                td.style.textAlign = 'right';
            }
        });
        
        showToast('指标数据已保存');
        editBtn.textContent = '编辑';
    } else {
        // 进入编辑模式
        values.forEach(td => {
            const value = td.textContent;
            td.innerHTML = `<input type="number" step="0.01" value="${value}" style="width: 100px;">`;
            td.style.textAlign = 'center';
        });
        editBtn.textContent = '保存';
    }
}

// 处理指标提取预览按钮点击事件
function handleExtractPreviewClick() {
    const modal = document.getElementById('extractProgressModal');
    const projectId = modal.dataset.projectId;
    if (projectId) {
        closeProgressModal('extractProgressModal'); // 先关闭进度框
        showIndicatorPreview(projectId); // 再显示指标预览框
    }
}

// 导出指标数据
function exportIndicators() {
    showToast('指标数据已导出');
}

// 显示提示消息
function showToast(message) {
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;
    document.body.appendChild(toast);
    
    // 2秒后自动消失
    setTimeout(() => {
        toast.remove();
    }, 2000);
}

// 关闭对话框
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.remove('show');
}

function closeProgressModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.remove('show');
}

// 本体费用指标数据
const bentiIndicators = [
    { name: '铁塔基数', unit: '基' },
    { name: '直线塔', unit: '基' },
    { name: '耐张塔', unit: '基' },
    { name: '耐张比例', unit: '%' },
    { name: '导线', unit: 't' },
    { name: '塔材', unit: 't' },
    { name: '基础钢材', unit: 't' },
    { name: '地脚螺栓和插入式角钢', unit: 't' },
    { name: '挂线金具', unit: 't' },
    { name: '导线间隔棒', unit: '套' },
    { name: '防振锤', unit: '个' },
    { name: '导线防振锤', unit: '个' },
    { name: '地线防振锤', unit: '个' },
    { name: '合成/复合绝缘子', unit: '支' },
    { name: '玻璃绝缘子/盘式绝缘子', unit: '支' },
    { name: '硬跳', unit: '套' },
    { name: '现浇混凝土', unit: 'm³' },
    { name: '灌柱桩基础混凝土', unit: 'm³' },
    { name: '基础护壁', unit: 'm³' },
    { name: '基础垫层', unit: 'm³' },
    { name: '钻孔灌注桩深度', unit: 'm' },
    { name: '护坡、挡土墙', unit: 'm³' },
    { name: '土方量', unit: 'm³' },
    { name: '基坑土方（非机械）', unit: 'm³' },
    { name: '基坑土方（机械）', unit: 'm³' },
    { name: '接地槽', unit: 'm³' },
    { name: '排水沟', unit: 'm³' },
    { name: '尖峰、基面', unit: 'm³' },
    { name: '本体工程', unit: '万元' },
    { name: '基础工程', unit: '万元' },
    { name: '杆塔工程', unit: '万元' },
    { name: '接地工程', unit: '万元' },
    { name: '架线工程', unit: '万元' },
    { name: '附件工程', unit: '万元' },
    { name: '辅助工程', unit: '万元' }
];

// 其他费用指标数据
const qitaIndicators = [
    { name: '项目建设管理费', unit: '万元' },
    { name: '项目法人管理费', unit: '万元' },
    { name: '招标费', unit: '万元' },
    { name: '工程监理费', unit: '万元' },
    { name: '施工过程造价咨询及竣工结算审核费', unit: '万元' },
    { name: '工程保险费', unit: '万元' },
    { name: '项目建设技术服务费', unit: '万元' },
    { name: '项目前期工作费', unit: '万元' },
    { name: '勘察设计费', unit: '万元' },
    { name: '勘察费', unit: '万元' },
    { name: '设计费', unit: '万元' },
    { name: '基本设计费', unit: '万元' },
    { name: '其他设计费', unit: '万元' },
    { name: '设计文件评审费', unit: '万元' },
    { name: '可行性研究文件评审费', unit: '万元' },
    { name: '初步设计文件评审费', unit: '万元' },
    { name: '施工图文件评审费', unit: '万元' },
    { name: '工程建设检测费', unit: '万元' },
    { name: '电力工程质量检测费', unit: '万元' },
    { name: '桩基检测费', unit: '万元' },
    { name: '电力工程技术经济标准编制费', unit: '万元' },
    { name: '生产准备费', unit: '万元' },
    { name: '管理车辆购置费', unit: '万元' },
    { name: '工器具及办公家具购置费', unit: '万元' },
    { name: '生产职工培训及提前进场费', unit: '万元' },
    { name: '专业爆破服务费', unit: '万元' }
];

// 切换tab页
function switchTab(type, tabName) {
    // 获取所有tab按钮和面板
    const tabs = document.querySelectorAll(`.${type}-tabs button`);
    const panels = document.querySelectorAll(`.${type}-panel`);
    
    // 移除所有active类
    tabs.forEach(tab => tab.classList.remove('active'));
    panels.forEach(panel => panel.classList.remove('active'));
    
    // 激活选中的tab
    const selectedTab = document.querySelector(`.${type}-tab[onclick*="${type}', '${tabName}"]`);
    const selectedPanel = document.getElementById(`${type}${tabName.charAt(0).toUpperCase() + tabName.slice(1)}Panel`);
    
    if (selectedTab) selectedTab.classList.add('active');
    if (selectedPanel) selectedPanel.classList.add('active');
}
</script> 