from flask import Blueprint, render_template, jsonify
import os
import json

bp = Blueprint('his_ind', __name__)

# 数据文件路径配置（与 app.py 保持一致）
DATA_DIR = 'data'
BENTI_DATA_FILE = os.path.join(DATA_DIR, 'Ind_benti.json')
QITA_DATA_FILE = os.path.join(DATA_DIR, 'Ind_qita.json')


def load_json_data(file_path):
    """通用 JSON 数据加载函数（复制自 app.py ，避免循环导入）"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return []
    except Exception as e:
        print(f"加载数据失败 {file_path}: {e}")
        return []


@bp.route('/his_ind/query')
def his_ind_query():
    """历史指标查询页面"""
    benti_data = load_json_data(BENTI_DATA_FILE)
    qita_data = load_json_data(QITA_DATA_FILE)
    return render_template('his_ind/hisIndQuery.html',
                           benti_data=benti_data,
                           qita_data=qita_data)


@bp.route('/api/indicators/benti')
def get_benti_indicators():
    """获取本体费用指标-指标数据预览（仅第一行）"""
    try:
        benti_data = load_json_data(BENTI_DATA_FILE)
        if isinstance(benti_data, list) and benti_data:
            first_row = benti_data[0]
            if isinstance(first_row, dict):
                return jsonify([first_row])
        return jsonify([])
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/api/indicators/qita')
def get_qita_indicators():
    """获取其他费用指标-指标数据预览（仅第一行）"""
    try:
        qita_data = load_json_data(QITA_DATA_FILE)
        if isinstance(qita_data, list) and qita_data:
            first_row = qita_data[0]
            if isinstance(first_row, dict):
                return jsonify([first_row])
        return jsonify([])
    except Exception as e:
        return jsonify({'error': str(e)}), 500 