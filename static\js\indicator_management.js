/**
 * 指标管理模块 JavaScript
 * 专门负责指标管理页面的所有功能
 * 包括本体指标和其他指标的增删改查、分页、搜索等功能
 */

// ==================== 模块配置和状态管理 ====================

/**
 * 指标管理模块配置
 */
const IndicatorManagement = {
    // 分页状态管理
    pagination: {
        benti: {
            currentPage: 1,
            pageSize: 10,
            totalRecords: 0,
            totalPages: 0
        },
        qita: {
            currentPage: 1,
            pageSize: 10,
            totalRecords: 0,
            totalPages: 0
        }
    },

    // API 端点配置
    api: {
        benti: {
            list: '/api/indicator_rule/benti/list',
            detail: '/api/indicator_rule/benti',
            create: '/api/indicator_rule/benti',
            update: '/api/indicator_rule/benti',
            delete: '/api/indicator_rule/benti/delete',
            maxSeq: '/api/indicator_rule/benti/max_seq'
        },
        qita: {
            list: '/api/indicator_rule/qita/list',
            detail: '/api/indicator_rule/qita',
            create: '/api/indicator_rule/qita',
            update: '/api/indicator_rule/qita',
            delete: '/api/indicator_rule/qita/delete',
            maxSeq: '/api/indicator_rule/qita/max_seq'
        }
    },

    // DOM 元素缓存
    elements: {
        benti: {},
        qita: {}
    }
};

// ==================== 初始化功能 ====================

/**
 * 初始化指标管理页面
 */
async function initIndicatorManagement() {
    console.log('初始化指标管理模块...');

    try {
        // 缓存DOM元素
        cacheElements();

        // 初始化Tab切换功能
        initIndicatorTabs();

        // 延迟一小段时间确保DOM完全准备好
        await new Promise(resolve => setTimeout(resolve, 100));

        // 加载默认数据
        console.log('开始加载本体指标默认数据...');
        await loadBentiIndicatorsWithRetry();

        console.log('指标管理模块初始化完成');
    } catch (error) {
        console.error('指标管理模块初始化失败:', error);
        showGlobalErrorMessage('页面初始化失败，请刷新页面重试');
    }
}

/**
 * 带重试机制的本体指标数据加载
 */
async function loadBentiIndicatorsWithRetry(maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            console.log(`第${attempt}次尝试加载本体指标数据...`);
            await loadBentiIndicators();
            console.log('本体指标数据加载成功');
            return;
        } catch (error) {
            console.error(`第${attempt}次加载失败:`, error);

            if (attempt === maxRetries) {
                console.error('所有重试都失败，显示错误状态');
                showErrorState('benti', '数据加载失败，请点击查询按钮重试');
                throw error;
            }

            // 等待一段时间后重试
            await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
    }
}

/**
 * 显示全局错误消息
 * @param {string} message - 错误消息
 */
function showGlobalErrorMessage(message) {
    // 尝试使用现有的错误显示机制
    if (typeof showErrorState === 'function') {
        showErrorState('benti', message);
    } else {
        // 如果没有现有机制，使用alert作为后备
        alert(message);
        console.error('全局错误:', message);
    }
}



/**
 * 缓存常用DOM元素
 */
function cacheElements() {
    console.log('开始缓存DOM元素...');

    // 本体指标相关元素
    IndicatorManagement.elements.benti = {
        nameInput: document.getElementById('benti-name-input'),
        categorySelect: document.getElementById('benti-category-select'),
        tableBody: document.getElementById('benti-table-body'),
        totalInfo: document.getElementById('benti-total-info'),
        prevBtn: document.getElementById('benti-prev-btn'),
        nextBtn: document.getElementById('benti-next-btn'),
        paginationPages: document.getElementById('benti-pagination-pages'),
        editModal: document.getElementById('benti-edit-modal')
    };

    // 其他指标相关元素
    IndicatorManagement.elements.qita = {
        nameInput: document.getElementById('qita-name-input'),
        tableBody: document.getElementById('qita-table-body'),
        totalInfo: document.getElementById('qita-total-info'),
        prevBtn: document.getElementById('qita-prev-btn'),
        nextBtn: document.getElementById('qita-next-btn'),
        paginationPages: document.getElementById('qita-pagination-pages'),
        editModal: document.getElementById('qita-edit-modal')
    };

    // 验证关键元素是否存在
    const criticalElements = [
        { name: 'benti-table-body', element: IndicatorManagement.elements.benti.tableBody },
        { name: 'benti-pagination-pages', element: IndicatorManagement.elements.benti.paginationPages },
        { name: 'benti-total-info', element: IndicatorManagement.elements.benti.totalInfo }
    ];

    let missingElements = [];
    criticalElements.forEach(({ name, element }) => {
        if (!element) {
            missingElements.push(name);
            console.error(`关键元素缺失: ${name}`);
        } else {
            console.log(`元素缓存成功: ${name}`);
        }
    });

    if (missingElements.length > 0) {
        throw new Error(`缺失关键DOM元素: ${missingElements.join(', ')}`);
    }

    console.log('DOM元素缓存完成');

}

/**
 * 初始化Tab切换功能
 */
function initIndicatorTabs() {
    const tabs = document.querySelectorAll('.indicator-tab');
    const tabContents = document.querySelectorAll('.indicator-tab-content');

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const tabName = this.getAttribute('data-tab');

            // 更新Tab状态
            tabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            // 更新内容显示
            tabContents.forEach(content => content.classList.remove('active'));
            const targetContent = document.getElementById(tabName + '-tab-content');
            if (targetContent) {
                targetContent.classList.add('active');
            }

            // 加载对应数据
            if (tabName === 'benti') {
                loadBentiIndicators();
            } else if (tabName === 'qita') {
                loadQitaIndicators();
            }
        });
    });
}

// ==================== 工具函数 ====================

/**
 * 截断文本显示
 * @param {string} text - 原始文本
 * @param {number} maxLength - 最大长度
 * @returns {string} 截断后的文本
 */
function truncateText(text, maxLength) {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

/**
 * 格式化日期时间
 * @param {string} dateTime - 日期时间字符串
 * @returns {string} 格式化后的日期时间
 */
function formatDateTime(dateTime) {
    if (!dateTime) return '';
    const date = new Date(dateTime);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}



/**
 * 显示加载状态
 * @param {string} type - 指标类型 ('benti' 或 'qita')
 * @param {boolean} isLoading - 是否显示加载状态
 */
function showLoadingState(type, isLoading) {
    const tableBody = IndicatorManagement.elements[type].tableBody;
    if (isLoading) {
        tableBody.innerHTML = '<tr><td colspan="100%" style="text-align: center; padding: 20px;">加载中...</td></tr>';
    }
}

/**
 * 显示错误信息
 * @param {string} type - 指标类型 ('benti' 或 'qita')
 * @param {string} message - 错误信息
 */
function showErrorState(type, message) {
    const tableBody = IndicatorManagement.elements[type].tableBody;
    tableBody.innerHTML = `<tr><td colspan="100%" style="text-align: center; padding: 20px; color: #f56565;">${message}</td></tr>`;
}

/**
 * 显示空数据状态
 * @param {string} type - 指标类型 ('benti' 或 'qita')
 */
function showEmptyState(type) {
    const tableBody = IndicatorManagement.elements[type].tableBody;
    tableBody.innerHTML = '<tr><td colspan="100%" style="text-align: center; padding: 20px; color: #a0aec0;">暂无数据</td></tr>';
}

// ==================== HTTP 请求封装 ====================

/**
 * 发送HTTP请求的通用方法
 * @param {string} url - 请求URL
 * @param {Object} options - 请求选项
 * @returns {Promise} 请求Promise
 */
async function httpRequest(url, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json'
        }
    };

    const finalOptions = { ...defaultOptions, ...options };
    
    try {
        const response = await fetch(url, finalOptions);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || `HTTP error! status: ${response.status}`);
        }
        
        return data;
    } catch (error) {
        console.error('HTTP请求失败:', error);
        throw error;
    }
}

// ==================== 数据加载功能 ====================

/**
 * 加载本体指标数据
 * @param {Object} searchParams - 搜索参数
 */
async function loadBentiIndicators(searchParams = {}) {
    const pagination = IndicatorManagement.pagination.benti;

    try {
        console.log('开始加载本体指标数据，参数:', searchParams);
        showLoadingState('benti', true);

        // 构建请求参数
        const params = new URLSearchParams({
            ...searchParams,
            page: pagination.currentPage,
            pageSize: pagination.pageSize
        });

        console.log('请求URL:', `${IndicatorManagement.api.benti.list}?${params}`);
        const data = await httpRequest(`${IndicatorManagement.api.benti.list}?${params}`);
        console.log('API响应数据:', data);

        if (data && data.success) {
            // 更新分页信息
            pagination.totalRecords = data.total || 0;
            pagination.totalPages = Math.ceil(pagination.totalRecords / pagination.pageSize);

            console.log('分页信息更新:', {
                totalRecords: pagination.totalRecords,
                totalPages: pagination.totalPages,
                currentPage: pagination.currentPage
            });

            // 渲染数据
            renderBentiTable(data.data || []);
            renderBentiPagination();

            console.log('本体指标数据渲染完成');
        } else {
            const errorMsg = data?.message || '加载数据失败';
            console.error('API返回错误:', errorMsg);
            showErrorState('benti', errorMsg);
            throw new Error(errorMsg);
        }
    } catch (error) {
        console.error('加载本体指标失败:', error);
        const errorMsg = error.message || '网络请求失败';
        showErrorState('benti', '加载数据出错: ' + errorMsg);
        throw error; // 重新抛出错误以便重试机制处理
    } finally {
        showLoadingState('benti', false);
    }
}

/**
 * 加载其他指标数据
 * @param {Object} searchParams - 搜索参数
 */
async function loadQitaIndicators(searchParams = {}) {
    const pagination = IndicatorManagement.pagination.qita;
    
    try {
        showLoadingState('qita', true);
        
        // 构建请求参数
        const params = new URLSearchParams({
            ...searchParams,
            page: pagination.currentPage,
            pageSize: pagination.pageSize
        });

        const data = await httpRequest(`${IndicatorManagement.api.qita.list}?${params}`);

        if (data.success) {
            // 更新分页信息
            pagination.totalRecords = data.total || 0;
            pagination.totalPages = Math.ceil(pagination.totalRecords / pagination.pageSize);

            // 渲染数据
            renderQitaTable(data.data || []);
            renderQitaPagination();
        } else {
            showErrorState('qita', data.message || '加载数据失败');
        }
    } catch (error) {
        console.error('加载其他指标失败:', error);
        showErrorState('qita', '加载数据出错: ' + error.message);
    }
}

// ==================== 表格渲染功能 ====================

/**
 * 渲染本体指标表格
 * @param {Array} data - 指标数据数组
 */
function renderBentiTable(data) {
    const tbody = IndicatorManagement.elements.benti.tableBody;

    if (!data || data.length === 0) {
        showEmptyState('benti');
        return;
    }

    tbody.innerHTML = '';
    console.log('渲染本体指标数据:', data);

    data.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="fixed-col">${item.序号 || ''}</td>
            <td class="fixed-col" title="${item.指标名称 || ''}">${item.指标名称 || ''}</td>
            <td>${item.单位 || ''}</td>
            <td>${item.分类 || ''}</td>
            <td>${item.单价 || ''}</td>
            <td>${item.概算表 || ''}</td>
            <td>${item.章节 || ''}</td>
            <td>${item.定额编号 || ''}</td>
            <td title="${item.匹配字段 || ''}">${truncateText(item.匹配字段, 30)}</td>
            <td class="long-text" title="${item.指标提取业务逻辑 || ''}">${truncateText(item.指标提取业务逻辑, 50)}</td>
            <td class="long-text" title="${item.指标提取技术逻辑 || ''}">${truncateText(item.指标提取技术逻辑, 50)}</td>
            <td>${formatDateTime(item.创建时间)}</td>
            <td>${formatDateTime(item.更新时间)}</td>
            <td class="fixed-col-right action-buttons">
                <button class="btn btn-primary btn-sm" onclick="editBentiIndicator(${item.id || ''})">编辑</button>
                <button class="btn btn-danger btn-sm" onclick="deleteBentiIndicator(${item.id || ''})">删除</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

/**
 * 渲染其他指标表格
 * @param {Array} data - 指标数据数组
 */
function renderQitaTable(data) {
    const tbody = IndicatorManagement.elements.qita.tableBody;

    if (!data || data.length === 0) {
        showEmptyState('qita');
        return;
    }

    tbody.innerHTML = '';
    console.log('渲染其他指标数据:', data);

    data.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="fixed-col">${item.序号 || ''}</td>
            <td class="fixed-col" title="${item.指标名称 || ''}">${item.指标名称 || ''}</td>
            <td>${item.单位 || ''}</td>
            <td>${item.概算表 || ''}</td>
            <td title="${item.匹配字段 || ''}">${truncateText(item.匹配字段, 30)}</td>
            <td class="long-text" title="${item.指标提取业务逻辑 || ''}">${truncateText(item.指标提取业务逻辑, 50)}</td>
            <td class="long-text" title="${item.指标提取技术逻辑 || ''}">${truncateText(item.指标提取技术逻辑, 50)}</td>
            <td>${formatDateTime(item.创建时间)}</td>
            <td>${formatDateTime(item.更新时间)}</td>
            <td class="fixed-col-right action-buttons">
                <button class="btn btn-primary btn-sm" onclick="editQitaIndicator(${item.id || ''})">编辑</button>
                <button class="btn btn-danger btn-sm" onclick="deleteQitaIndicator(${item.id || ''})">删除</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// ==================== 分页功能 ====================

/**
 * 渲染本体指标分页控件
 */
function renderBentiPagination() {
    const pagination = IndicatorManagement.pagination.benti;
    const elements = IndicatorManagement.elements.benti;

    // 更新总记录数信息
    elements.totalInfo.textContent = `共 ${pagination.totalRecords} 条记录`;

    // 更新分页按钮状态
    elements.prevBtn.disabled = pagination.currentPage <= 1;
    elements.nextBtn.disabled = pagination.currentPage >= pagination.totalPages;

    // 渲染页码
    renderBentiPaginationPages(pagination, elements.paginationPages);
}

/**
 * 渲染其他指标分页控件
 */
function renderQitaPagination() {
    const pagination = IndicatorManagement.pagination.qita;
    const elements = IndicatorManagement.elements.qita;

    // 更新总记录数信息
    elements.totalInfo.textContent = `共 ${pagination.totalRecords} 条记录`;

    // 更新分页按钮状态
    elements.prevBtn.disabled = pagination.currentPage <= 1;
    elements.nextBtn.disabled = pagination.currentPage >= pagination.totalPages;

    // 渲染页码
    renderQitaPaginationPages(pagination, elements.paginationPages);
}

/**
 * 渲染本体指标分页页码
 * @param {Object} pagination - 分页信息
 * @param {HTMLElement} container - 页码容器
 */
function renderBentiPaginationPages(pagination, container) {
    container.innerHTML = '';

    if (pagination.totalPages > 0) {
        // 计算显示的页码范围
        let startPage = Math.max(1, pagination.currentPage - 2);
        let endPage = Math.min(pagination.totalPages, pagination.currentPage + 2);

        // 如果当前页靠近开始，显示更多后面的页码
        if (pagination.currentPage <= 3) {
            endPage = Math.min(pagination.totalPages, 5);
        }

        // 如果当前页靠近结束，显示更多前面的页码
        if (pagination.currentPage >= pagination.totalPages - 2) {
            startPage = Math.max(1, pagination.totalPages - 4);
        }

        // 渲染页码按钮
        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('span');
            pageBtn.className = `pagination-page ${i === pagination.currentPage ? 'active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.onclick = () => gotoBentiPage(i);
            container.appendChild(pageBtn);
        }

        // 添加总页数显示
        const totalInfo = document.createElement('span');
        totalInfo.textContent = ` / ${pagination.totalPages}页`;
        totalInfo.style.marginLeft = '10px';
        totalInfo.style.color = 'var(--secondary-color)';
        container.appendChild(totalInfo);
    }
}

/**
 * 渲染其他指标分页页码
 * @param {Object} pagination - 分页信息
 * @param {HTMLElement} container - 页码容器
 */
function renderQitaPaginationPages(pagination, container) {
    container.innerHTML = '';

    if (pagination.totalPages > 0) {
        // 计算显示的页码范围
        let startPage = Math.max(1, pagination.currentPage - 2);
        let endPage = Math.min(pagination.totalPages, pagination.currentPage + 2);

        // 如果当前页靠近开始，显示更多后面的页码
        if (pagination.currentPage <= 3) {
            endPage = Math.min(pagination.totalPages, 5);
        }

        // 如果当前页靠近结束，显示更多前面的页码
        if (pagination.currentPage >= pagination.totalPages - 2) {
            startPage = Math.max(1, pagination.totalPages - 4);
        }

        // 渲染页码按钮
        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('span');
            pageBtn.className = `pagination-page ${i === pagination.currentPage ? 'active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.onclick = () => gotoQitaPage(i);
            container.appendChild(pageBtn);
        }

        // 添加总页数显示
        const totalInfo = document.createElement('span');
        totalInfo.textContent = ` / ${pagination.totalPages}页`;
        totalInfo.style.marginLeft = '10px';
        totalInfo.style.color = 'var(--secondary-color)';
        container.appendChild(totalInfo);
    }
}

// ==================== 搜索和分页导航功能 ====================

/**
 * 搜索本体指标
 */
function searchBentiIndicators() {
    const elements = IndicatorManagement.elements.benti;
    const name = elements.nameInput.value.trim();
    const category = elements.categorySelect.value;

    const searchParams = {};
    if (name) searchParams.name = name;
    if (category) searchParams.category = category;

    // 重置到第一页
    IndicatorManagement.pagination.benti.currentPage = 1;
    loadBentiIndicators(searchParams);
}

/**
 * 分页加载本体指标（保持当前搜索条件）
 */
function loadBentiIndicatorsWithCurrentSearch() {
    const elements = IndicatorManagement.elements.benti;
    const name = elements.nameInput.value.trim();
    const category = elements.categorySelect.value;

    const searchParams = {};
    if (name) searchParams.name = name;
    if (category) searchParams.category = category;

    // 不重置页码，使用当前页码进行加载
    loadBentiIndicators(searchParams);
}

/**
 * 重置本体指标查询表单
 */
function resetBentiForm() {
    const elements = IndicatorManagement.elements.benti;
    elements.nameInput.value = '';
    elements.categorySelect.value = '';

    // 重置到第一页
    IndicatorManagement.pagination.benti.currentPage = 1;
    loadBentiIndicators();
}

/**
 * 搜索其他指标
 */
function searchQitaIndicators() {
    const elements = IndicatorManagement.elements.qita;
    const name = elements.nameInput.value.trim();

    const searchParams = {};
    if (name) searchParams.name = name;

    // 重置到第一页
    IndicatorManagement.pagination.qita.currentPage = 1;
    loadQitaIndicators(searchParams);
}

/**
 * 分页加载其他指标（保持当前搜索条件）
 */
function loadQitaIndicatorsWithCurrentSearch() {
    const elements = IndicatorManagement.elements.qita;
    const name = elements.nameInput.value.trim();

    const searchParams = {};
    if (name) searchParams.name = name;

    // 不重置页码，使用当前页码进行加载
    loadQitaIndicators(searchParams);
}

/**
 * 重置其他指标查询表单
 */
function resetQitaForm() {
    const elements = IndicatorManagement.elements.qita;
    elements.nameInput.value = '';

    // 重置到第一页
    IndicatorManagement.pagination.qita.currentPage = 1;
    loadQitaIndicators();
}

/**
 * 分页导航 - 上一页
 * @param {string} type - 指标类型 ('benti' 或 'qita')
 */
function prevPage(type) {
    const pagination = IndicatorManagement.pagination[type];
    if (pagination.currentPage > 1) {
        pagination.currentPage--;
        if (type === 'benti') {
            loadBentiIndicatorsWithCurrentSearch();
        } else {
            loadQitaIndicatorsWithCurrentSearch();
        }
    }
}

/**
 * 分页导航 - 下一页
 * @param {string} type - 指标类型 ('benti' 或 'qita')
 */
function nextPage(type) {
    const pagination = IndicatorManagement.pagination[type];
    if (pagination.currentPage < pagination.totalPages) {
        pagination.currentPage++;
        if (type === 'benti') {
            loadBentiIndicatorsWithCurrentSearch();
        } else {
            loadQitaIndicatorsWithCurrentSearch();
        }
    }
}

/**
 * 分页导航 - 跳转到指定页
 * @param {string} type - 指标类型 ('benti' 或 'qita')
 * @param {number} page - 目标页码
 */
function gotoPage(type, page) {
    const pagination = IndicatorManagement.pagination[type];
    if (page >= 1 && page <= pagination.totalPages) {
        pagination.currentPage = page;
        if (type === 'benti') {
            loadBentiIndicatorsWithCurrentSearch();
        } else {
            loadQitaIndicatorsWithCurrentSearch();
        }
    }
}

// 为了兼容HTML中的onclick事件，保留原有的函数名
function prevBentiPage() { prevPage('benti'); }
function nextBentiPage() { nextPage('benti'); }
function gotoBentiPage(page) {
    const pagination = IndicatorManagement.pagination.benti;
    if (page >= 1 && page <= pagination.totalPages) {
        pagination.currentPage = page;
        loadBentiIndicatorsWithCurrentSearch();
    }
}
function prevQitaPage() { prevPage('qita'); }
function nextQitaPage() { nextPage('qita'); }
function gotoQitaPage(page) {
    const pagination = IndicatorManagement.pagination.qita;
    if (page >= 1 && page <= pagination.totalPages) {
        pagination.currentPage = page;
        loadQitaIndicatorsWithCurrentSearch();
    }
}

// ==================== 编辑功能 ====================

/**
 * 编辑本体指标
 * @param {number} id - 指标ID
 */
async function editBentiIndicator(id) {
    console.log('编辑本体指标:', id);

    try {
        const data = await httpRequest(`${IndicatorManagement.api.benti.detail}/${id}`);

        if (data.success) {
            const indicatorData = data.data;

            // 填充表单数据
            document.getElementById('benti-edit-id').value = indicatorData.id;
            document.getElementById('benti-edit-序号').value = indicatorData.序号 || '';
            document.getElementById('benti-edit-指标名称').value = indicatorData.指标名称 || '';
            document.getElementById('benti-edit-单位').value = indicatorData.单位 || '';
            document.getElementById('benti-edit-分类').value = indicatorData.分类 || '';
            document.getElementById('benti-edit-单价').value = indicatorData.单价 || '';
            document.getElementById('benti-edit-概算表').value = indicatorData.概算表 || '';
            document.getElementById('benti-edit-章节').value = indicatorData.章节 || '';
            document.getElementById('benti-edit-定额编号').value = indicatorData.定额编号 || '';
            document.getElementById('benti-edit-匹配字段').value = indicatorData.匹配字段 || '';
            document.getElementById('benti-edit-指标提取业务逻辑').value = indicatorData.指标提取业务逻辑 || '';
            document.getElementById('benti-edit-指标提取技术逻辑').value = indicatorData.指标提取技术逻辑 || '';

            // 编辑时设置指标名称为只读
            document.getElementById('benti-edit-指标名称').readOnly = true;
            document.getElementById('benti-edit-指标名称').className = 'readonly-input';

            // 修改模态框标题
            document.querySelector('#benti-edit-modal .modal-header h3').textContent = '编辑本体指标';

            // 显示模态框
            showModal('benti-edit-modal');
        } else {
            alert('获取指标详情失败：' + data.message);
        }
    } catch (error) {
        console.error('获取指标详情失败:', error);
        alert('获取指标详情失败，请稍后重试');
    }
}

/**
 * 编辑其他指标
 * @param {number} id - 指标ID
 */
async function editQitaIndicator(id) {
    console.log('编辑其他指标:', id);

    try {
        const data = await httpRequest(`${IndicatorManagement.api.qita.detail}/${id}`);

        if (data.success) {
            const indicatorData = data.data;

            // 填充表单数据
            document.getElementById('qita-edit-id').value = indicatorData.id;
            document.getElementById('qita-edit-序号').value = indicatorData.序号 || '';
            document.getElementById('qita-edit-指标名称').value = indicatorData.指标名称 || '';
            document.getElementById('qita-edit-单位').value = indicatorData.单位 || '';
            document.getElementById('qita-edit-概算表').value = indicatorData.概算表 || '';
            document.getElementById('qita-edit-匹配字段').value = indicatorData.匹配字段 || '';
            document.getElementById('qita-edit-指标提取业务逻辑').value = indicatorData.指标提取业务逻辑 || '';
            document.getElementById('qita-edit-指标提取技术逻辑').value = indicatorData.指标提取技术逻辑 || '';

            // 编辑时设置指标名称为只读
            document.getElementById('qita-edit-指标名称').readOnly = true;
            document.getElementById('qita-edit-指标名称').className = 'readonly-input';

            // 修改模态框标题
            document.querySelector('#qita-edit-modal .modal-header h3').textContent = '编辑其他指标';

            // 显示模态框
            showModal('qita-edit-modal');
        } else {
            alert('获取指标详情失败：' + data.message);
        }
    } catch (error) {
        console.error('获取指标详情失败:', error);
        alert('获取指标详情失败，请稍后重试');
    }
}

// ==================== 模态框管理功能 ====================

/**
 * 显示模态框
 * @param {string} modalId - 模态框ID
 */
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'flex';
        modal.style.alignItems = 'center';
        modal.style.justifyContent = 'center';
    }
}

/**
 * 隐藏模态框
 * @param {string} modalId - 模态框ID
 */
function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
    }
}

/**
 * 关闭本体指标编辑模态框
 */
function closeBentiEditModal() {
    hideModal('benti-edit-modal');
}

/**
 * 关闭其他指标编辑模态框
 */
function closeQitaEditModal() {
    hideModal('qita-edit-modal');
}

// ==================== 新增功能 ====================

/**
 * 获取最大序号
 * @param {string} type - 指标类型 ('benti' 或 'qita')
 * @returns {Promise<number>} 下一个序号
 */
async function getNextSequenceNumber(type) {
    try {
        const data = await httpRequest(IndicatorManagement.api[type].maxSeq);
        if (data.success) {
            return data.data.max_seq + 1;
        } else {
            throw new Error(data.message || '获取最大序号失败');
        }
    } catch (error) {
        console.warn('从API获取最大序号失败，使用备选方案:', error.message);

        // 备选方案：从当前表格获取最大序号
        const tableBody = IndicatorManagement.elements[type].tableBody;
        const rows = tableBody.getElementsByTagName('tr');
        let maxSeqNo = 0;

        for (let i = 0; i < rows.length; i++) {
            const firstCell = rows[i].cells[0];
            if (firstCell) {
                const seqNo = parseInt(firstCell.textContent);
                if (!isNaN(seqNo) && seqNo > maxSeqNo) {
                    maxSeqNo = seqNo;
                }
            }
        }

        return maxSeqNo + 1;
    }
}

/**
 * 新增本体指标
 */
async function addBentiIndicator() {
    try {
        const nextSeqNo = await getNextSequenceNumber('benti');
        showBentiAddModal(nextSeqNo);
    } catch (error) {
        console.error('获取序号失败:', error);
        alert('获取序号失败：' + error.message);
    }
}

/**
 * 新增其他指标
 */
async function addQitaIndicator() {
    try {
        const nextSeqNo = await getNextSequenceNumber('qita');
        showQitaAddModal(nextSeqNo);
    } catch (error) {
        console.error('获取序号失败:', error);
        alert('获取序号失败：' + error.message);
    }
}

/**
 * 显示本体指标新增模态框
 * @param {number} nextSeqNo - 下一个序号
 */
function showBentiAddModal(nextSeqNo) {
    // 清空表单并设置默认值
    document.getElementById('benti-edit-id').value = '';
    document.getElementById('benti-edit-序号').value = nextSeqNo;
    document.getElementById('benti-edit-序号').readOnly = true;
    document.getElementById('benti-edit-指标名称').value = '';
    document.getElementById('benti-edit-单位').value = '';
    document.getElementById('benti-edit-分类').value = '';
    document.getElementById('benti-edit-概算表').value = '';
    document.getElementById('benti-edit-章节').value = '';
    document.getElementById('benti-edit-定额编号').value = '';
    document.getElementById('benti-edit-匹配字段').value = '';
    document.getElementById('benti-edit-指标提取业务逻辑').value = '';
    document.getElementById('benti-edit-指标提取技术逻辑').value = '';

    // 新增时设置指标名称为可编辑
    document.getElementById('benti-edit-指标名称').readOnly = false;
    document.getElementById('benti-edit-指标名称').className = 'form-input';

    // 修改模态框标题
    document.querySelector('#benti-edit-modal .modal-header h3').textContent = '新增本体指标';

    // 显示模态框
    showModal('benti-edit-modal');
}

/**
 * 显示其他指标新增模态框
 * @param {number} nextSeqNo - 下一个序号
 */
function showQitaAddModal(nextSeqNo) {
    // 清空表单并设置默认值
    document.getElementById('qita-edit-id').value = '';
    document.getElementById('qita-edit-序号').value = nextSeqNo;
    document.getElementById('qita-edit-序号').readOnly = true;
    document.getElementById('qita-edit-指标名称').value = '';
    document.getElementById('qita-edit-单位').value = '';
    document.getElementById('qita-edit-概算表').value = '';
    document.getElementById('qita-edit-匹配字段').value = '';
    document.getElementById('qita-edit-指标提取业务逻辑').value = '';
    document.getElementById('qita-edit-指标提取技术逻辑').value = '';

    // 新增时设置指标名称为可编辑
    document.getElementById('qita-edit-指标名称').readOnly = false;
    document.getElementById('qita-edit-指标名称').className = 'form-input';

    // 修改模态框标题
    document.querySelector('#qita-edit-modal .modal-header h3').textContent = '新增其他指标';

    // 显示模态框
    showModal('qita-edit-modal');
}

// ==================== 保存功能 ====================

/**
 * 保存本体指标
 */
async function saveBentiIndicator() {
    const id = document.getElementById('benti-edit-id').value;
    const isNew = !id;

    // 收集表单数据
    const formData = {
        序号: document.getElementById('benti-edit-序号').value,
        指标名称: document.getElementById('benti-edit-指标名称').value,
        单位: document.getElementById('benti-edit-单位').value,
        分类: document.getElementById('benti-edit-分类').value,
        单价: document.getElementById('benti-edit-单价').value,
        概算表: document.getElementById('benti-edit-概算表').value,
        章节: document.getElementById('benti-edit-章节').value,
        定额编号: document.getElementById('benti-edit-定额编号').value,
        匹配字段: document.getElementById('benti-edit-匹配字段').value,
        指标提取业务逻辑: document.getElementById('benti-edit-指标提取业务逻辑').value,
        指标提取技术逻辑: document.getElementById('benti-edit-指标提取技术逻辑').value
    };

    // 验证必填字段
    if (!formData.指标名称.trim()) {
        alert('请输入指标名称');
        return;
    }

    try {
        const url = isNew ? IndicatorManagement.api.benti.create : `${IndicatorManagement.api.benti.update}/${id}`;
        const method = isNew ? 'POST' : 'PUT';

        const data = await httpRequest(url, {
            method: method,
            body: JSON.stringify(formData)
        });

        if (data.success) {
            alert(isNew ? '新增成功' : '保存成功');
            closeBentiEditModal();
            loadBentiIndicators();
        } else {
            alert((isNew ? '新增失败：' : '保存失败：') + data.message);
        }
    } catch (error) {
        console.error(isNew ? '新增失败:' : '保存失败:', error);
        alert((isNew ? '新增失败' : '保存失败') + '，请稍后重试');
    }
}

/**
 * 保存其他指标
 */
async function saveQitaIndicator() {
    const id = document.getElementById('qita-edit-id').value;
    const isNew = !id;

    // 收集表单数据
    const formData = {
        序号: document.getElementById('qita-edit-序号').value,
        指标名称: document.getElementById('qita-edit-指标名称').value,
        单位: document.getElementById('qita-edit-单位').value,
        概算表: document.getElementById('qita-edit-概算表').value,
        匹配字段: document.getElementById('qita-edit-匹配字段').value,
        指标提取业务逻辑: document.getElementById('qita-edit-指标提取业务逻辑').value,
        指标提取技术逻辑: document.getElementById('qita-edit-指标提取技术逻辑').value
    };

    // 验证必填字段
    if (!formData.指标名称.trim()) {
        alert('请输入指标名称');
        return;
    }

    try {
        const url = isNew ? IndicatorManagement.api.qita.create : `${IndicatorManagement.api.qita.update}/${id}`;
        const method = isNew ? 'POST' : 'PUT';

        const data = await httpRequest(url, {
            method: method,
            body: JSON.stringify(formData)
        });

        if (data.success) {
            alert(isNew ? '新增成功' : '保存成功');
            closeQitaEditModal();
            loadQitaIndicators();
        } else {
            alert((isNew ? '新增失败：' : '保存失败：') + data.message);
        }
    } catch (error) {
        console.error(isNew ? '新增失败:' : '保存失败:', error);
        alert((isNew ? '新增失败' : '保存失败') + '，请稍后重试');
    }
}

// ==================== 删除功能 ====================

/**
 * 删除本体指标
 * @param {number} id - 指标ID
 */
async function deleteBentiIndicator(id) {
    if (!confirm('确定要删除这个指标吗？')) {
        return;
    }

    try {
        const data = await httpRequest(`${IndicatorManagement.api.benti.delete}/${id}`, {
            method: 'DELETE'
        });

        if (data.success) {
            alert('删除成功');
            loadBentiIndicators();
        } else {
            alert('删除失败: ' + data.message);
        }
    } catch (error) {
        console.error('删除出错:', error);
        alert('删除出错: ' + error.message);
    }
}

/**
 * 删除其他指标
 * @param {number} id - 指标ID
 */
async function deleteQitaIndicator(id) {
    if (!confirm('确定要删除这个指标吗？')) {
        return;
    }

    try {
        const data = await httpRequest(`${IndicatorManagement.api.qita.delete}/${id}`, {
            method: 'DELETE'
        });

        if (data.success) {
            alert('删除成功');
            loadQitaIndicators();
        } else {
            alert('删除失败: ' + data.message);
        }
    } catch (error) {
        console.error('删除出错:', error);
        alert('删除出错: ' + error.message);
    }
}

// ==================== 导入导出演示功能 ====================

/**
 * 显示导入导出模态框
 * @param {Object} options - 模态框配置选项
 */
function showImportExportModal({ title, text, showFile = false, confirmText = '确认', cancelText = '取消', onConfirm = null }) {
    console.log('showImportExportModal方法开始执行');

    const modal = document.getElementById('importExportModal');
    const titleElement = document.getElementById('importExportModalTitle');
    const messageElement = document.getElementById('importExportModalMessage');
    const fileInput = document.getElementById('importExportModalFileInput');
    const cancelBtn = document.getElementById('importExportModalCancelBtn');
    const confirmBtn = document.getElementById('importExportModalConfirmBtn');

    console.log('模态框元素:', modal);

    if (!modal) {
        console.error('找不到模态框元素');
        return;
    }

    // 设置内容
    titleElement.textContent = title;
    messageElement.textContent = text;

    // 控制文件输入框显示
    if (showFile) {
        fileInput.classList.add('show');
        fileInput.value = '';
    } else {
        fileInput.classList.remove('show');
    }

    // 设置按钮
    confirmBtn.textContent = confirmText;
    cancelBtn.textContent = cancelText;

    // 控制取消按钮显示
    if (cancelText) {
        cancelBtn.classList.remove('hide');
    } else {
        cancelBtn.classList.add('hide');
    }

    // 显示模态框 - 使用CSS类控制
    modal.classList.add('show');

    console.log('模态框应该已显示，CSS类:', modal.className);

    // 绑定事件
    confirmBtn.onclick = function() {
        if (onConfirm) onConfirm();
    };
    cancelBtn.onclick = hideImportExportModal;
}

/**
 * 隐藏导入导出模态框
 */
function hideImportExportModal() {
    const modal = document.getElementById('importExportModal');
    const fileInput = document.getElementById('importExportModalFileInput');

    if (modal) {
        // 移除显示类
        modal.classList.remove('show');
        console.log('模态框已隐藏');
    }

    if (fileInput) {
        // 隐藏文件输入框并清空值
        fileInput.classList.remove('show');
        fileInput.value = '';
    }
}

/**
 * 导入本体指标演示
 */
function importBentiIndicators() {
    showImportExportModal({
        title: '本体指标文件上传',
        text: '请选择要导入的本体指标Excel文件。',
        showFile: true,
        confirmText: '导入',
        onConfirm: function () {
            const fileInput = document.getElementById('importExportModalFileInput');
            if (!fileInput.files.length) {
                alert('请先选择要上传的文件！');
                return;
            }
            showImportExportModal({
                title: '确认导入',
                text: '导入的指标数据将添加到系统中，请确认。',
                confirmText: '确认',
                onConfirm: function () {
                    showImportExportModal({
                        title: '提示',
                        text: '本体指标数据导入成功！',
                        confirmText: '确定',
                        cancelText: null,
                        onConfirm: function() {
                            hideImportExportModal();
                            loadBentiIndicators(); // 刷新数据
                        }
                    });
                }
            });
        }
    });
}

/**
 * 导出本体指标演示
 */
function exportBentiIndicators() {
    showImportExportModal({
        title: '导出确认',
        text: '将本体指标数据导出为Excel文件，请确认是否导出。',
        confirmText: '导出',
        onConfirm: function () {
            showImportExportModal({
                title: '提示',
                text: '本体指标数据已导出成功！',
                confirmText: '确定',
                cancelText: null,
                onConfirm: hideImportExportModal
            });
        }
    });
}

/**
 * 导入其他指标演示
 */
function importQitaIndicators() {
    console.log('导入其他指标演示');
    showImportExportModal({
        title: '其他指标文件上传',
        text: '请选择要导入的其他指标Excel文件。',
        showFile: true,
        confirmText: '导入',
        onConfirm: function () {
            const fileInput = document.getElementById('importExportModalFileInput');
            if (!fileInput.files.length) {
                alert('请先选择要上传的文件！');
                return;
            }
            showImportExportModal({
                title: '确认导入',
                text: '导入的指标数据将添加到系统中，请确认。',
                confirmText: '确认',
                onConfirm: function () {
                    showImportExportModal({
                        title: '提示',
                        text: '其他指标数据导入成功！',
                        confirmText: '确定',
                        cancelText: null,
                        onConfirm: function() {
                            hideImportExportModal();
                            loadQitaIndicators(); // 刷新数据
                        }
                    });
                }
            });
        }
    });
}

/**
 * 导出其他指标演示
 */
function exportQitaIndicators() {
    showImportExportModal({
        title: '导出确认',
        text: '将其他指标数据导出为Excel文件，请确认是否导出。',
        confirmText: '导出',
        onConfirm: function () {
            showImportExportModal({
                title: '提示',
                text: '其他指标数据已导出成功！',
                confirmText: '确定',
                cancelText: null,
                onConfirm: hideImportExportModal
            });
        }
    });
}

// ==================== 页面初始化和事件绑定 ====================

/**
 * 页面加载完成后初始化指标管理功能
 */
document.addEventListener('DOMContentLoaded', async function() {
    // 检查是否在指标管理页面
    if (document.querySelector('.indicator-management-container')) {
        console.log('检测到指标管理页面，开始初始化...');

        try {
            // 初始化指标管理模块
            await initIndicatorManagement();
        } catch (error) {
            console.error('页面初始化过程中发生错误:', error);
        }

        // 添加模态框外部点击关闭功能
        window.addEventListener('click', function(event) {
            const bentiModal = document.getElementById('benti-edit-modal');
            const qitaModal = document.getElementById('qita-edit-modal');
            const importExportModal = document.getElementById('importExportModal');

            if (event.target === bentiModal) {
                closeBentiEditModal();
            }
            if (event.target === qitaModal) {
                closeQitaEditModal();
            }
            if (event.target === importExportModal) {
                hideImportExportModal();
            }
        });

        // 添加键盘事件监听（ESC键关闭模态框）
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeBentiEditModal();
                closeQitaEditModal();
                hideImportExportModal();
            }
        });

        console.log('指标管理页面初始化完成');
    }
});

// 备用初始化机制 - 如果DOMContentLoaded已经触发
if (document.readyState === 'loading') {
    // 文档仍在加载中，DOMContentLoaded事件会处理初始化
    console.log('文档正在加载中，等待DOMContentLoaded事件...');
} else {
    // 文档已经加载完成，立即初始化
    console.log('文档已加载完成，立即初始化...');
    setTimeout(async () => {
        if (document.querySelector('.indicator-management-container')) {
            console.log('备用初始化：检测到指标管理页面');
            try {
                await initIndicatorManagement();
            } catch (error) {
                console.error('备用初始化失败:', error);
            }
        }
    }, 100);
}

// ==================== 模块导出（如果需要） ====================

// 如果在模块化环境中使用，可以导出主要功能
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        IndicatorManagement,
        initIndicatorManagement,
        loadBentiIndicators,
        loadQitaIndicators,
        searchBentiIndicators,
        searchQitaIndicators,
        addBentiIndicator,
        addQitaIndicator,
        editBentiIndicator,
        editQitaIndicator,
        deleteBentiIndicator,
        deleteQitaIndicator,
        saveBentiIndicator,
        saveQitaIndicator
    };
}
