# 数据标准化处理详解

## 概述

本文档详细解析了电力线路匹配算法中 `_normalize_data` 函数的数据标准化处理过程，该函数实现了Z-score标准化（标准分数标准化）。

数据标准化：其实就是先基于历史数据统计出每个要素的平均值，标准差（方差的平方），然后计算每个要素的范围，将数据转化到一个范围。

当进行新数据的相似度匹配时，新数据进来时，可以量化新数据和旧数据的距离。

## 标准化算法原理

### Z-score标准化公式

```
z = (x - μ) / σ
```

**参数说明：**
- `x` = 原始值
- `μ` = 该特征的均值（mean）
- `σ` = 该特征的标准差（standard deviation）
- `z` = 标准化后的值

### 算法目标

将数据转换为：
- **均值 = 0**
- **标准差 = 1**
- **无量纲化**

## 函数实现解析

### 核心代码结构

```python
def _normalize_data(self, data: List[List[float]]) -> List[List[float]]:
    """数据标准化处理（Z-score标准化）"""
    
    # 1. 获取数据维度
    n_features = len(data[0])  # 特征数量
    n_samples = len(data)      # 样本数量
    
    # 2. 计算每个特征的统计量
    means = []  # 存储各特征均值
    stds = []   # 存储各特征标准差
    
    # 3. 逐特征计算均值和标准差
    for j in range(n_features):
        feature_values = [data[i][j] for i in range(n_samples)]
        mean = sum(feature_values) / n_samples
        variance = sum((x - mean) ** 2 for x in feature_values) / n_samples
        std = math.sqrt(variance) if variance > 0 else 1.0
        
        means.append(mean)
        stds.append(std)
    
    # 4. 应用标准化公式
    normalized = []
    for i in range(n_samples):
        normalized_row = []
        for j in range(n_features):
            normalized_value = (data[i][j] - means[j]) / stds[j]
            normalized_row.append(normalized_value)
        normalized.append(normalized_row)
    
    return normalized
```

## 详细计算示例

### 原始数据

```python
original_data = [
    [30.0, 10.0, 2, 1],  # 线路1: [风速(m/s), 覆冰(mm), 回路数, 导线规格]
    [25.0, 15.0, 1, 2],  # 线路2
    [35.0, 5.0, 3, 1],   # 线路3
    [28.0, 12.0, 2, 2],  # 线路4
]
```

### 第一步：计算统计量

#### 特征0 - 风速 [30.0, 25.0, 35.0, 28.0]

```
均值计算：
μ_风速 = (30.0 + 25.0 + 35.0 + 28.0) / 4 = 118.0 / 4 = 29.5

方差计算：
σ²_风速 = [(30-29.5)² + (25-29.5)² + (35-29.5)² + (28-29.5)²] / 4
        = [0.25 + 20.25 + 30.25 + 2.25] / 4
        = 53.0 / 4 = 13.25

标准差计算：
σ_风速 = √13.25 = 3.64
```

#### 特征1 - 覆冰 [10.0, 15.0, 5.0, 12.0]

```
均值计算：
μ_覆冰 = (10.0 + 15.0 + 5.0 + 12.0) / 4 = 42.0 / 4 = 10.5

方差计算：
σ²_覆冰 = [(10-10.5)² + (15-10.5)² + (5-10.5)² + (12-10.5)²] / 4
        = [0.25 + 20.25 + 30.25 + 2.25] / 4
        = 53.0 / 4 = 13.25

标准差计算：
σ_覆冰 = √13.25 = 3.64
```

#### 特征2 - 回路数 [2, 1, 3, 2]

```
均值计算：
μ_回路数 = (2 + 1 + 3 + 2) / 4 = 8 / 4 = 2.0

方差计算：
σ²_回路数 = [(2-2)² + (1-2)² + (3-2)² + (2-2)²] / 4
          = [0 + 1 + 1 + 0] / 4 = 0.5

标准差计算：
σ_回路数 = √0.5 = 0.71
```

#### 特征3 - 导线规格 [1, 2, 1, 2]

```
均值计算：
μ_导线规格 = (1 + 2 + 1 + 2) / 4 = 6 / 4 = 1.5

方差计算：
σ²_导线规格 = [(1-1.5)² + (2-1.5)² + (1-1.5)² + (2-1.5)²] / 4
            = [0.25 + 0.25 + 0.25 + 0.25] / 4 = 0.25

标准差计算：
σ_导线规格 = √0.25 = 0.5
```

### 第二步：应用标准化公式

#### 线路1 [30.0, 10.0, 2, 1] 标准化

```
风速标准化：    z₁ = (30.0 - 29.5) / 3.64 = 0.5 / 3.64 = 0.14
覆冰标准化：    z₂ = (10.0 - 10.5) / 3.64 = -0.5 / 3.64 = -0.14
回路数标准化：  z₃ = (2 - 2.0) / 0.71 = 0 / 0.71 = 0.0
导线规格标准化：z₄ = (1 - 1.5) / 0.5 = -0.5 / 0.5 = -1.0

结果：[0.14, -0.14, 0.0, -1.0]
```

#### 线路2 [25.0, 15.0, 1, 2] 标准化

```
风速标准化：    z₁ = (25.0 - 29.5) / 3.64 = -4.5 / 3.64 = -1.24
覆冰标准化：    z₂ = (15.0 - 10.5) / 3.64 = 4.5 / 3.64 = 1.24
回路数标准化：  z₃ = (1 - 2.0) / 0.71 = -1 / 0.71 = -1.41
导线规格标准化：z₄ = (2 - 1.5) / 0.5 = 0.5 / 0.5 = 1.0

结果：[-1.24, 1.24, -1.41, 1.0]
```

#### 线路3 [35.0, 5.0, 3, 1] 标准化

```
风速标准化：    z₁ = (35.0 - 29.5) / 3.64 = 5.5 / 3.64 = 1.51
覆冰标准化：    z₂ = (5.0 - 10.5) / 3.64 = -5.5 / 3.64 = -1.51
回路数标准化：  z₃ = (3 - 2.0) / 0.71 = 1 / 0.71 = 1.41
导线规格标准化：z₄ = (1 - 1.5) / 0.5 = -0.5 / 0.5 = -1.0

结果：[1.51, -1.51, 1.41, -1.0]
```

#### 线路4 [28.0, 12.0, 2, 2] 标准化

```
风速标准化：    z₁ = (28.0 - 29.5) / 3.64 = -1.5 / 3.64 = -0.41
覆冰标准化：    z₂ = (12.0 - 10.5) / 3.64 = 1.5 / 3.64 = 0.41
回路数标准化：  z₃ = (2 - 2.0) / 0.71 = 0 / 0.71 = 0.0
导线规格标准化：z₄ = (2 - 1.5) / 0.5 = 0.5 / 0.5 = 1.0

结果：[-0.41, 0.41, 0.0, 1.0]
```

## 标准化结果对比表

| 线路 | 原始数据 | 标准化数据 |
|------|----------|------------|
| 线路1 | [30.0, 10.0, 2, 1] | [0.14, -0.14, 0.0, -1.0] |
| 线路2 | [25.0, 15.0, 1, 2] | [-1.24, 1.24, -1.41, 1.0] |
| 线路3 | [35.0, 5.0, 3, 1] | [1.51, -1.51, 1.41, -1.0] |
| 线路4 | [28.0, 12.0, 2, 2] | [-0.41, 0.41, 0.0, 1.0] |

## 标准化的作用和意义

### 1. 消除量纲影响

**标准化前：**
- 风速：25-35 m/s
- 覆冰：5-15 mm  
- 回路数：1-3 个
- 导线规格：1-2 编号

**标准化后：**
- 所有特征都变成无量纲的标准分数
- 数值范围大致在 [-2, 2] 之间

### 2. 统一特征重要性

**问题：** 如果不标准化，风速的数值范围(25-35)会比导线规格(1-2)大很多，在距离计算中风速会占主导地位。

**解决：** 标准化后，每个特征对距离计算的贡献变得相等。

### 3. 提高算法精度

- 防止数值大的特征主导相似度计算
- 确保算法能够公平地比较不同特征
- 提高匹配结果的准确性

### 4. 标准化值的含义

- **z = 0**：该特征值等于平均水平
- **z > 0**：该特征值高于平均水平
- **z < 0**：该特征值低于平均水平
- **|z|**：偏离平均水平的程度（以标准差为单位）

## 实际应用示例

### 解读标准化结果

以线路3为例：`[1.51, -1.51, 1.41, -1.0]`

- **风速 1.51**：比平均风速高1.51个标准差（较高风速）
- **覆冰 -1.51**：比平均覆冰低1.51个标准差（较低覆冰）
- **回路数 1.41**：比平均回路数高1.41个标准差（较多回路）
- **导线规格 -1.0**：比平均导线规格低1个标准差（较小规格）

### 相似度计算优势

标准化后，当计算两条线路的欧几里得距离时：
```
distance = √[(z₁₁-z₂₁)² + (z₁₂-z₂₂)² + (z₁₃-z₂₃)² + (z₁₄-z₂₄)²]
```

每个特征的贡献是平等的，不会因为原始数值范围的差异而产生偏差。

## 注意事项

1. **标准差为0的处理**：如果某个特征的所有值都相同，标准差为0，代码中设置为1.0避免除零错误。

2. **新数据标准化**：新线路数据必须使用相同的均值和标准差进行标准化，确保一致性。

3. **数据质量要求**：标准化假设数据近似正态分布，异常值会影响均值和标准差的计算。

4. **保存标准化参数**：均值和标准差需要保存，用于后续新数据的标准化处理。
