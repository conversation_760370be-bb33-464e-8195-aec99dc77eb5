#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指标管理数据库初始化脚本
创建SQLite数据库并导入MD文件数据
"""

import sqlite3
import os
import sys
import re
import random
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# ==================== 单价生成相关配置 ====================

# 需要生成单价的24个指标
PRICE_INDICATORS = [
    "直线塔", "耐张塔", "导线", "塔材", "基础钢材",
    "地脚螺栓和插入式角钢", "挂线金具", "导线间隔棒",
    "导线防振锤", "地线防振锤", "合成/复合绝缘子",
    "玻璃绝缘子/盘式绝缘子", "硬跳", "现浇混凝土",
    "灌柱桩基础混凝土", "基础护壁", "基础垫层",
    "钻孔灌注桩深度", "护坡、挡土墙、排水沟",
    "基坑土方（非机械）", "基坑土方（机械）",
    "接地槽", "排水沟", "尖峰、基面"
]

def get_price_range(unit):
    """根据单位确定单价范围"""
    price_ranges = {
        # 基础设施类 - 较高单价
        '基': (10000, 50000),

        # 材料类 - 中等单价（按吨计算）
        't': (5000, 25000),

        # 小件类 - 较低单价
        '套': (50, 500),
        '个': (20, 300),
        '支': (100, 800),

        # 工程量类 - 按立方米计价
        'm³': (100, 800),

        # 长度类 - 按米计价
        'm': (50, 200),

        # 百分比类 - 特殊计价
        '%': (1, 10)
    }

    return price_ranges.get(unit, (100, 1000))  # 默认范围

def generate_random_price(min_price, max_price):
    """生成随机单价，保留2位小数"""
    price = random.uniform(min_price, max_price)
    return round(price, 2)

def generate_indicator_price(indicator_name, unit):
    """为指标生成单价"""
    if indicator_name in PRICE_INDICATORS:
        min_price, max_price = get_price_range(unit)
        return generate_random_price(min_price, max_price)
    else:
        return "/"  # 不在单价指标列表中的返回"/"字符串

def create_database():
    """创建数据库和表结构"""
    db_path = os.path.join('data', 'indicator_management.db')
    
    # 确保data目录存在
    os.makedirs('data', exist_ok=True)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建本体指标表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS t_indicator_benti (
            id INTEGER PRIMARY KEY,
            序号 INTEGER,
            指标名称 TEXT NOT NULL,
            单位 TEXT,
            分类 TEXT,
            单价 TEXT DEFAULT '/',
            概算表 TEXT,
            章节 TEXT,
            定额编号 TEXT,
            匹配字段 TEXT,
            指标提取业务逻辑 TEXT,
            指标提取技术逻辑 TEXT,
            创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
            更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 创建其他指标表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS t_indicator_qita (
            id INTEGER PRIMARY KEY,
            序号 INTEGER,
            指标名称 TEXT NOT NULL,
            单位 TEXT,
            概算表 TEXT,
            匹配字段 TEXT,
            指标提取业务逻辑 TEXT,
            指标提取技术逻辑 TEXT,
            创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
            更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    conn.commit()
    conn.close()
    print(f"数据库创建成功: {db_path}")

def parse_md_table(file_path):
    """解析MD文件中的表格数据"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.strip().split('\n')
    if len(lines) < 3:
        return []
    
    # 解析表头
    header_line = lines[0]
    headers = [h.strip() for h in header_line.split('|')[1:-1]]  # 去掉首尾空元素
    
    # 解析数据行
    data_rows = []
    for line in lines[2:]:  # 跳过表头和分隔符行
        if line.strip() and '|' in line:
            row_data = [cell.strip() for cell in line.split('|')[1:-1]]
            if len(row_data) == len(headers):
                data_rows.append(dict(zip(headers, row_data)))
    
    return data_rows

def import_benti_data():
    """导入本体指标数据"""
    md_file = 'templatefiles/markdown/indicatorAndrule/指标信息及提取规则分气象区-本体指标.md'
    
    if not os.path.exists(md_file):
        print(f"文件不存在: {md_file}")
        return
    
    data_rows = parse_md_table(md_file)
    if not data_rows:
        print("未找到有效数据")
        return
    
    db_path = os.path.join('data', 'indicator_management.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 清空现有数据
    cursor.execute('DELETE FROM t_indicator_benti')

    # 设置随机种子以确保可重现的结果
    random.seed(42)

    # 插入数据
    for row in data_rows:
        indicator_name = row.get('指标名称', '')
        unit = row.get('单位', '')

        # 生成单价（如果指标在单价列表中）
        price = generate_indicator_price(indicator_name, unit)

        cursor.execute('''
            INSERT INTO t_indicator_benti (
                序号, 指标名称, 单位, 分类, 单价, 概算表, 章节, 定额编号, 匹配字段,
                指标提取业务逻辑, 指标提取技术逻辑
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            int(row.get('序号', 0)),
            indicator_name,
            unit,
            row.get('分类', ''),
            price,  # 单价字段放在分类后
            row.get('概算表', ''),
            row.get('章节', ''),
            row.get('定额编号', ''),
            row.get('匹配字段', ''),
            row.get('指标提取业务逻辑', ''),
            row.get('指标提取技术逻辑', '')
        ))

        # 输出插入信息
        price_display = f"{price:.2f}元" if isinstance(price, (int, float)) else price
        print(f"插入本体指标: {indicator_name:25s} | 单位: {unit:4s} | 单价: {price_display:>10s}")
    
    conn.commit()
    conn.close()
    print(f"本体指标数据导入成功，共 {len(data_rows)} 条记录")

def import_qita_data():
    """导入其他指标数据"""
    md_file = 'templatefiles/markdown/indicatorAndrule/指标信息及提取规则合并气象区-其他指标.md'
    
    if not os.path.exists(md_file):
        print(f"文件不存在: {md_file}")
        return
    
    data_rows = parse_md_table(md_file)
    if not data_rows:
        print("未找到有效数据")
        return
    
    db_path = os.path.join('data', 'indicator_management.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 清空现有数据
    cursor.execute('DELETE FROM t_indicator_qita')
    
    # 插入数据
    for row in data_rows:
        cursor.execute('''
            INSERT INTO t_indicator_qita (
                序号, 指标名称, 单位, 概算表, 匹配字段,
                指标提取业务逻辑, 指标提取技术逻辑
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            int(row.get('序号', 0)),
            row.get('指标名称', ''),
            row.get('单位', ''),
            row.get('概算表', ''),
            row.get('匹配字段', ''),
            row.get('指标提取业务逻辑', ''),
            row.get('指标提取技术逻辑', '')
        ))
    
    conn.commit()
    conn.close()
    print(f"其他指标数据导入成功，共 {len(data_rows)} 条记录")

def main():
    """主函数"""
    print("开始初始化指标管理数据库...")
    
    # 创建数据库和表结构
    create_database()
    
    # 导入数据
    import_benti_data()
    import_qita_data()
    
    print("数据库初始化完成！")

if __name__ == '__main__':
    main()
