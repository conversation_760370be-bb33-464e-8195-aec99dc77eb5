<!-- 单价管理页面内容 -->
<div class="price-management-container">
    <!-- 查询条件区域 -->
    <div class="query-section">
        <div class="query-row">
            <div class="query-form">
                <div class="query-item">
                    <label>指标名称：</label>
                    <input type="text" id="price-name-input" placeholder="请输入指标名称" class="query-input">
                </div>
                <div class="query-item">
                    <label>指标分类：</label>
                    <select id="price-category-select" class="query-select">
                        <option value="">全部</option>
                        <!-- 分类选项将通过JavaScript动态加载 -->
                    </select>
                </div>
            </div>
            <div class="query-buttons">
                <button class="btn btn-primary" onclick="searchPrices()">查询</button>
                <button class="btn btn-secondary" onclick="resetPriceForm()">重置</button>
                <button class="btn btn-success" onclick="addPrice()">新增</button>
                <button class="btn btn-info" onclick="importPrices()">导入</button>
                <button class="btn btn-warning" onclick="exportPrices()">导出</button>
            </div>
        </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
        <div class="table-container">
            <table class="data-table" id="price-table">
                <thead>
                    <tr>
                        <th class="fixed-col">序号</th>
                        <th class="fixed-col">指标名称</th>
                        <th>计量单位</th>
                        <th>指标分类</th>
                        <th>单价(元)</th>
                        <th class="fixed-col-right">操作</th>
                    </tr>
                </thead>
                <tbody id="price-table-body">
                    <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
        <!-- 分页控件 -->
        <div class="pagination-section">
            <div class="pagination-info">
                <span id="price-total-info">共 0 条记录</span>
            </div>
            <div class="pagination-controls">
                <button class="btn btn-secondary btn-sm" id="price-prev-btn" onclick="prevPricePage()">上一页</button>
                <span class="pagination-pages" id="price-pagination-pages"></span>
                <button class="btn btn-secondary btn-sm" id="price-next-btn" onclick="nextPricePage()">下一页</button>
            </div>
        </div>
    </div>
</div>

<!-- 单价编辑模态框 -->
<div id="price-edit-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>编辑单价信息</h3>
            <span class="close" onclick="closePriceEditModal()">&times;</span>
        </div>
        <div class="modal-body">
            <form id="price-edit-form">
                <input type="hidden" id="price-edit-id">

                <div class="form-group">
                    <label for="price-edit-sequence">序号：</label>
                    <input type="number" id="price-edit-sequence" class="form-input" required>
                </div>

                <div class="form-group">
                    <label for="price-edit-name">指标名称：</label>
                    <input type="text" id="price-edit-name" class="form-input" required>
                </div>

                <div class="form-group">
                    <label for="price-edit-unit">计量单位：</label>
                    <input type="text" id="price-edit-unit" class="form-input" required>
                </div>

                <div class="form-group">
                    <label for="price-edit-category">指标分类：</label>
                    <select id="price-edit-category" class="form-input" required>
                        <option value="">请选择分类</option>
                        <option value="基础工程">基础工程</option>
                        <option value="架线工程">架线工程</option>
                        <option value="杆塔工程">杆塔工程</option>
                        <option value="附件工程">附件工程</option>
                        <option value="辅助工程">辅助工程</option>
                        <option value="接地工程">接地工程</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="price-edit-price">单价(元)：</label>
                    <input type="number" id="price-edit-price" class="form-input" step="0.01" min="0" required>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closePriceEditModal()">取消</button>
            <button type="button" class="btn btn-primary" onclick="savePriceData()">保存</button>
        </div>
    </div>
</div>
