<!-- 指标管理页面内容 -->
<div class="indicator-management-container">
    <!-- Tab页导航 -->
    <div class="indicator-tabs">
        <div class="indicator-tab active" data-tab="benti">本体指标</div>
        <div class="indicator-tab" data-tab="qita">其他指标</div>
    </div>

    <!-- 本体指标Tab内容 -->
    <div id="benti-tab-content" class="indicator-tab-content active">
        <!-- 查询条件区域 -->
        <div class="query-section">
            <div class="query-row">
                <div class="query-form">
                    <div class="query-item">
                        <label>指标名称：</label>
                        <input type="text" id="benti-name-input" placeholder="请输入指标名称" class="query-input">
                    </div>
                    <div class="query-item">
                        <label>指标分类：</label>
                        <select id="benti-category-select" class="query-select">
                            <option value="">全部</option>
                            <option value="边界条件">边界条件</option>
                            <option value="地形比例">地形比例</option>
                            <option value="运距">运距</option>
                            <option value="基础工程">基础工程</option>
                            <option value="架线工程">架线工程</option>
                            <option value="杆塔工程">杆塔工程</option>
                            <option value="附件工程">附件工程</option>
                            <option value="辅助工程">辅助工程</option>
                            <option value="费用">费用</option>
                        </select>
                    </div>
                </div>
                <div class="query-buttons">
                    <button class="btn btn-primary" onclick="searchBentiIndicators()">查询</button>
                    <button class="btn btn-secondary" onclick="resetBentiForm()">重置</button>
                    <button class="btn btn-success" onclick="addBentiIndicator()">新增</button>
                    <button class="btn btn-info" onclick="importBentiIndicators()">导入</button>
                    <button class="btn btn-warning" onclick="exportBentiIndicators()">导出</button>
                </div>
            </div>
        </div>

        <!-- 数据表格区域 -->
        <div class="table-section">
            <div class="table-container">
                <table class="data-table" id="benti-table">
                    <thead>
                        <tr>
                            <th class="fixed-col">序号</th>
                            <th class="fixed-col">指标名称</th>
                            <th>单位</th>
                            <th>分类</th>
                            <th>单价(元)</th>
                            <th>概算表</th>
                            <th>章节</th>
                            <th>定额编号</th>
                            <th>匹配字段</th>
                            <th>指标提取业务逻辑</th>
                            <th>指标提取技术逻辑</th>
                            <th>创建时间</th>
                            <th>更新时间</th>
                            <th class="fixed-col-right">操作</th>
                        </tr>
                    </thead>
                    <tbody id="benti-table-body">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
            <!-- 分页控件 -->
            <div class="pagination-section">
                <div class="pagination-info">
                    <span id="benti-total-info">共 0 条记录</span>
                </div>
                <div class="pagination-controls">
                    <button class="btn btn-secondary btn-sm" id="benti-prev-btn" onclick="prevBentiPage()">上一页</button>
                    <span class="pagination-pages" id="benti-pagination-pages"></span>
                    <button class="btn btn-secondary btn-sm" id="benti-next-btn" onclick="nextBentiPage()">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 其他指标Tab内容 -->
    <div id="qita-tab-content" class="indicator-tab-content">
        <!-- 查询条件区域 -->
        <div class="query-section">
            <div class="query-row">
                <div class="query-form">
                    <div class="query-item">
                        <label>指标名称：</label>
                        <input type="text" id="qita-name-input" placeholder="请输入指标名称" class="query-input">
                    </div>
                </div>
                <div class="query-buttons">
                    <button class="btn btn-primary" onclick="searchQitaIndicators()">查询</button>
                    <button class="btn btn-secondary" onclick="resetQitaForm()">重置</button>
                    <button class="btn btn-success" onclick="addQitaIndicator()">新增</button>
                    <button class="btn btn-info" onclick="importQitaIndicators()">导入</button>
                    <button class="btn btn-warning" onclick="exportQitaIndicators()">导出</button>
                </div>
            </div>
        </div>

        <!-- 数据表格区域 -->
        <div class="table-section">
            <div class="table-container">
                <table class="data-table" id="qita-table">
                    <thead>
                        <tr>
                            <th class="fixed-col">序号</th>
                            <th class="fixed-col">指标名称</th>
                            <th>单位</th>
                            <th>概算表</th>
                            <th>匹配字段</th>
                            <th>指标提取业务逻辑</th>
                            <th>指标提取技术逻辑</th>
                            <th>创建时间</th>
                            <th>更新时间</th>
                            <th class="fixed-col-right">操作</th>
                        </tr>
                    </thead>
                    <tbody id="qita-table-body">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
            <!-- 分页控件 -->
            <div class="pagination-section">
                <div class="pagination-info">
                    <span id="qita-total-info">共 0 条记录</span>
                </div>
                <div class="pagination-controls">
                    <button class="btn btn-secondary btn-sm" id="qita-prev-btn" onclick="prevQitaPage()">上一页</button>
                    <span class="pagination-pages" id="qita-pagination-pages"></span>
                    <button class="btn btn-secondary btn-sm" id="qita-next-btn" onclick="nextQitaPage()">下一页</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 本体指标编辑模态框 -->
<div id="benti-edit-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>编辑本体指标</h3>
            <span class="close" onclick="closeBentiEditModal()">&times;</span>
        </div>
        <div class="modal-body">
            <form id="benti-edit-form">
                <input type="hidden" id="benti-edit-id" name="id">

                <div class="form-group">
                    <label>序号：</label>
                    <input type="text" id="benti-edit-序号" readonly class="readonly-input">
                </div>

                <div class="form-group">
                    <label>指标名称：</label>
                    <input type="text" id="benti-edit-指标名称" readonly class="readonly-input">
                </div>

                <div class="form-group">
                    <label>单位：</label>
                    <input type="text" id="benti-edit-单位" name="单位" class="form-input">
                </div>

                <div class="form-group">
                    <label>分类：</label>
                    <input type="text" id="benti-edit-分类" name="分类" class="form-input">
                </div>

                <div class="form-group">
                    <label>单价(元)：</label>
                    <input type="number" id="benti-edit-单价" name="单价" class="form-input" step="0.01" min="0" placeholder="留空表示无单价">
                </div>

                <div class="form-group">
                    <label>概算表：</label>
                    <input type="text" id="benti-edit-概算表" name="概算表" class="form-input">
                </div>

                <div class="form-group">
                    <label>章节：</label>
                    <input type="text" id="benti-edit-章节" name="章节" class="form-input">
                </div>

                <div class="form-group">
                    <label>定额编号：</label>
                    <input type="text" id="benti-edit-定额编号" name="定额编号" class="form-input">
                </div>

                <div class="form-group">
                    <label>匹配字段：</label>
                    <input type="text" id="benti-edit-匹配字段" name="匹配字段" class="form-input">
                </div>

                <div class="form-group">
                    <label>指标提取业务逻辑：</label>
                    <textarea id="benti-edit-指标提取业务逻辑" name="指标提取业务逻辑" class="form-textarea" rows="4"></textarea>
                </div>

                <div class="form-group">
                    <label>指标提取技术逻辑：</label>
                    <textarea id="benti-edit-指标提取技术逻辑" name="指标提取技术逻辑" class="form-textarea" rows="4"></textarea>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeBentiEditModal()">取消</button>
            <button type="button" class="btn btn-primary" onclick="saveBentiIndicator()">保存</button>
        </div>
    </div>
</div>

<!-- 其他指标编辑模态框 -->
<div id="qita-edit-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>编辑其他指标</h3>
            <span class="close" onclick="closeQitaEditModal()">&times;</span>
        </div>
        <div class="modal-body">
            <form id="qita-edit-form">
                <input type="hidden" id="qita-edit-id" name="id">

                <div class="form-group">
                    <label>序号：</label>
                    <input type="text" id="qita-edit-序号" readonly class="readonly-input">
                </div>

                <div class="form-group">
                    <label>指标名称：</label>
                    <input type="text" id="qita-edit-指标名称" readonly class="readonly-input">
                </div>

                <div class="form-group">
                    <label>单位：</label>
                    <input type="text" id="qita-edit-单位" name="单位" class="form-input">
                </div>

                <div class="form-group">
                    <label>概算表：</label>
                    <input type="text" id="qita-edit-概算表" name="概算表" class="form-input">
                </div>

                <div class="form-group">
                    <label>匹配字段：</label>
                    <input type="text" id="qita-edit-匹配字段" name="匹配字段" class="form-input">
                </div>

                <div class="form-group">
                    <label>指标提取业务逻辑：</label>
                    <textarea id="qita-edit-指标提取业务逻辑" name="指标提取业务逻辑" class="form-textarea" rows="4"></textarea>
                </div>

                <div class="form-group">
                    <label>指标提取技术逻辑：</label>
                    <textarea id="qita-edit-指标提取技术逻辑" name="指标提取技术逻辑" class="form-textarea" rows="4"></textarea>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeQitaEditModal()">取消</button>
            <button type="button" class="btn btn-primary" onclick="saveQitaIndicator()">保存</button>
        </div>
    </div>
</div>

<!-- 导入导出演示模态框 -->
<div id="importExportModal" class="import-export-modal-overlay">
    <div class="import-export-modal-content">
        <div class="import-export-modal-header">
            <h3 id="importExportModalTitle" class="import-export-modal-title">标题</h3>
            <span onclick="hideImportExportModal()" class="import-export-modal-close">&times;</span>
        </div>
        <div class="import-export-modal-body">
            <p id="importExportModalMessage" class="import-export-modal-message">消息内容</p>
            <input type="file" id="importExportModalFileInput"
                   accept=".xlsx,.xls,.csv"
                   class="import-export-modal-file-input">
        </div>
        <div class="import-export-modal-footer">
            <button type="button" id="importExportModalCancelBtn" class="import-export-modal-btn import-export-modal-btn-cancel">取消</button>
            <button type="button" id="importExportModalConfirmBtn" class="import-export-modal-btn import-export-modal-btn-confirm">确认</button>
        </div>
    </div>
</div>


