/* 指标及规则管理模块样式 */

/* 确保html和body占满全屏 */
html, body {
    height: 100%;
    margin: 0;
    padding: 0;
}

/* 覆盖全局容器样式 */
main {
    padding: 0;
    min-height: calc(100vh - 120px);
    height: calc(100vh - 120px);
}

main .container {
    max-width: none;
    padding: 5px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* 使用与主样式一致的变量 */
:root {
    --primary-color: #0071e3;
    --primary-hover: #0077ed;
    --secondary-color: #86868b;
    --text-color: #1d1d1f;
    --light-gray: #f5f5f7;
    --border-color: #d2d2d7;
    --success-color: #34c759;
    --warning-color: #ff9500;
    --error-color: #ff3b30;
    --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* 主容器样式 */
.indicator-rule-container {
    display: flex;
    height: 100%;
    min-height: calc(100vh - 140px);
    background-color: var(--light-gray);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
    flex: 1;
}

/* 左侧菜单栏 */
.indicator-rule-sidebar {
    width: 250px;
    background-color: white;
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
}

.indicator-rule-sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--light-gray);
}

.indicator-rule-sidebar-header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
}

.indicator-rule-menu {
    flex: 1;
    padding: 20px 0;
}

.indicator-rule-menu-item {
    display: block;
    padding: 12px 20px;
    color: var(--text-color);
    text-decoration: none;
    border-left: 3px solid transparent;
    transition: var(--transition);
    font-weight: 500;
}

.indicator-rule-menu-item:hover {
    background-color: var(--light-gray);
    color: var(--primary-color);
}

.indicator-rule-menu-item.active {
    background-color: rgba(0, 113, 227, 0.1);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
}

/* 右侧内容区域 */
.indicator-rule-content {
    flex: 1;
    background-color: white;
    display: flex;
    flex-direction: column;
}

.indicator-rule-content-header {
    padding: 20px 30px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--light-gray);
}

.indicator-rule-content-header h1 {
    margin: 0 0 8px 0;
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-color);
}

.indicator-rule-content-header p {
    margin: 0;
    color: var(--secondary-color);
    font-size: 0.95rem;
}

.indicator-rule-content-body {
    flex: 1;
    padding: 20px 30px 30px 30px;
    overflow-y: auto;
    min-height: 0; /* 确保flex子元素可以收缩 */
}

/* 页面内容区域 */
.indicator-rule-page {
    display: none;
    height: 100%;
}

.indicator-rule-page.active {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* 按钮样式 */
.indicator-rule-btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 980px;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    outline: none;
    text-decoration: none;
}

.indicator-rule-btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.indicator-rule-btn-primary:hover {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
}

.indicator-rule-btn-secondary {
    background-color: var(--light-gray);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.indicator-rule-btn-secondary:hover {
    background-color: #e5e5e5;
    transform: translateY(-1px);
}

/* 卡片样式 */
.indicator-rule-card {
    background-color: white;
    border-radius: 12px;
    box-shadow: var(--box-shadow);
    padding: 20px;
    margin-bottom: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.indicator-rule-card:last-child {
    margin-bottom: 0;
    flex-grow: 1;
}

.indicator-rule-card h3 {
    margin: 0 0 15px 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
}

/* 表格样式 */
.indicator-rule-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.indicator-rule-table th,
.indicator-rule-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.indicator-rule-table th {
    background-color: var(--light-gray);
    font-weight: 600;
    color: var(--text-color);
}

.indicator-rule-table tbody tr:hover {
    background-color: var(--light-gray);
}

/* 指标管理页面样式 */
.indicator-management-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Tab页导航样式 */
.indicator-tabs {
    display: flex;
    border-bottom: 2px solid var(--border-color);
    margin-bottom: 20px;
}

.indicator-tab {
    padding: 12px 24px;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    font-weight: 500;
    color: var(--secondary-color);
    transition: var(--transition);
}

.indicator-tab:hover {
    color: var(--primary-color);
    background-color: var(--light-gray);
}

.indicator-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background-color: var(--light-gray);
}

/* Tab内容区域 */
.indicator-tab-content {
    display: none;
    flex: 1;
    overflow-y: auto;
}

.indicator-tab-content.active {
    display: flex;
    flex-direction: column;
}

/* 查询条件区域样式 */
.query-section {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--box-shadow);
}

.query-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.query-form {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    flex: 1;
}

.query-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.query-item label {
    font-weight: 500;
    color: var(--text-color);
    white-space: nowrap;
}

.query-input, .query-select {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    min-width: 200px;
    transition: var(--transition);
}

.query-input:focus, .query-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 113, 227, 0.1);
}

.query-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    flex-shrink: 0;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: var(--light-gray);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: #e5e5e5;
    transform: translateY(-1px);
}

.btn-success {
    background-color: var(--success-color);
    color: white;
}

.btn-success:hover {
    background-color: #2fb344;
    transform: translateY(-1px);
}

.btn-info {
    background-color: #007bff;
    color: white;
}

.btn-info:hover {
    background-color: #0056b3;
    transform: translateY(-1px);
}

.btn-warning {
    background-color: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background-color: #e6850e;
    transform: translateY(-1px);
}

.btn-danger {
    background-color: var(--error-color);
    color: white;
}

.btn-danger:hover {
    background-color: #d32f2f;
    transform: translateY(-1px);
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

/* 表格区域样式 */
.table-section {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: var(--box-shadow);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.table-container {
    flex: 1;
    overflow: auto;
    position: relative;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    min-width: 1200px; /* 确保表格有足够宽度 */
}

.data-table th,
.data-table td {
    padding: 12px 8px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
    border-right: 1px solid var(--border-color);
}

.data-table th:last-child,
.data-table td:last-child {
    border-right: none;
}

.data-table th {
    background-color: var(--light-gray);
    font-weight: 600;
    color: var(--text-color);
    position: sticky;
    top: 0;
    z-index: 20;
}

/* 固定列样式 */
.data-table .fixed-col {
    position: sticky;
    left: 0;
    background-color: var(--light-gray);
    z-index: 21;
}

.data-table .fixed-col-right {
    position: sticky;
    right: 0;
    background-color: var(--light-gray);
    z-index: 21;
}

.data-table tbody tr .fixed-col {
    background-color: white;
    z-index: 19;
}

.data-table tbody tr .fixed-col-right {
    background-color: white;
    z-index: 19;
}

.data-table tbody tr:hover .fixed-col,
.data-table tbody tr:hover .fixed-col-right {
    background-color: var(--light-gray);
}

.data-table tbody tr:hover {
    background-color: var(--light-gray);
}

.data-table td {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 长文本字段特殊处理 */
.data-table .long-text {
    max-width: 200px;
    cursor: help;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.data-table .action-buttons {
    display: flex;
    gap: 5px;
    white-space: nowrap;
}

/* 分页控件样式 */
.pagination-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

.pagination-info {
    color: var(--secondary-color);
    font-size: 14px;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.pagination-pages {
    display: flex;
    gap: 5px;
    align-items: center;
    min-height: 32px;
}

.pagination-page {
    padding: 6px 12px;
    border: 1px solid #d2d2d7;
    background-color: white;
    color: #1d1d1f;
    cursor: pointer;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s ease;
    display: inline-block;
    text-align: center;
    min-width: 32px;
    box-sizing: border-box;
}

.pagination-page:hover {
    background-color: #f5f5f7;
    border-color: #0071e3;
}

.pagination-page.active {
    background-color: #0071e3;
    color: white;
    border-color: #0071e3;
}

.pagination-page.disabled {
    background-color: #f5f5f7;
    color: #86868b;
    cursor: not-allowed;
}

/* Tooltip样式 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 300px;
    background-color: #333;
    color: #fff;
    text-align: left;
    border-radius: 6px;
    padding: 8px;
    position: absolute;
    z-index: 1000;
    bottom: 125%;
    left: 50%;
    margin-left: -150px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
    white-space: normal;
    word-wrap: break-word;
}

.tooltip .tooltiptext::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #333 transparent transparent transparent;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .indicator-rule-container {
        flex-direction: column;
        height: auto;
    }

    .indicator-rule-sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }

    .indicator-rule-menu {
        display: flex;
        overflow-x: auto;
        padding: 10px;
    }

    .indicator-rule-menu-item {
        white-space: nowrap;
        border-left: none;
        border-bottom: 3px solid transparent;
    }

    .indicator-rule-menu-item.active {
        border-left: none;
        border-bottom-color: var(--primary-color);
    }

    .query-row {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .query-form {
        flex-direction: column;
        gap: 15px;
    }

    .query-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .query-input, .query-select {
        width: 100%;
        min-width: auto;
    }

    .query-buttons {
        justify-content: center;
    }

    .data-table {
        font-size: 12px;
        min-width: 800px;
    }

    .data-table th,
    .data-table td {
        padding: 8px 4px;
    }

    .pagination-section {
        flex-direction: column;
        gap: 10px;
        align-items: center;
    }

    .pagination-controls {
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* ==================== 编辑模态框样式 ==================== */

/* 模态框背景 - 使用更高优先级的选择器 */
#benti-edit-modal.modal,
#qita-edit-modal.modal {
    display: none;
    position: fixed !important;
    z-index: 1000 !important;
    left: 0 !important;
    top: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    max-width: none !important;
    max-height: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    transform: none !important;
    transition: none !important;
    overflow: visible !important;
}

/* 模态框显示时的样式 */
#benti-edit-modal.modal[style*="flex"],
#qita-edit-modal.modal[style*="flex"] {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* 模态框内容容器 */
#benti-edit-modal .modal-content,
#qita-edit-modal .modal-content {
    background-color: #fff !important;
    border-radius: 8px !important;
    width: 90% !important;
    max-width: 650px !important;
    max-height: 90vh !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
    animation: modalSlideIn 0.3s ease-out;
    overflow: hidden !important;
    margin: 0 !important;
    position: relative !important;
    transform: none !important;
    transition: none !important;
}

/* 模态框动画 */
@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 模态框头部 */
.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

/* 关闭按钮 */
.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
    transition: color 0.2s ease;
}

.close:hover,
.close:focus {
    color: #000;
    text-decoration: none;
}

/* 模态框主体 */
.modal-body {
    padding: 25px;
    max-height: 65vh;
    overflow-y: auto;
}

/* 表单组样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

/* 输入框样式 */
.form-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    box-sizing: border-box;
}

.form-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 文本域样式 */
.form-textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    resize: vertical;
    min-height: 80px;
    font-family: inherit;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    box-sizing: border-box;
}

.form-textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 只读输入框样式 */
.readonly-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    font-size: 14px;
    background-color: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
    box-sizing: border-box;
}

/* 模态框底部 */
.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px 25px;
    border-top: 1px solid #e9ecef;
    background-color: #f8f9fa;
    border-radius: 0 0 8px 8px;
}

/* ==================== 导入导出模态框专用样式 ==================== */

/* 模态框遮罩层 - 覆盖全屏并提供背景遮罩效果 */
.import-export-modal-overlay {
    display: none !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    z-index: 9999 !important;
    justify-content: center !important;
    align-items: center !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* 显示状态 - 通过JavaScript添加此类来显示模态框 */
.import-export-modal-overlay.show {
    display: flex !important;
}

/* 模态框内容容器 - 主要的模态框窗口样式 */
.import-export-modal-content {
    background: white !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
    max-width: 500px !important;
    width: 90% !important;
    max-height: 90vh !important;
    overflow: auto !important;
    position: relative !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* 模态框头部 - 包含标题和关闭按钮的区域 */
.import-export-modal-header {
    padding: 20px 24px 16px !important;
    border-bottom: 1px solid #e5e7eb !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
}

/* 模态框标题 - 显示操作类型（导入/导出） */
.import-export-modal-title {
    margin: 0 !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #1f2937 !important;
}

/* 关闭按钮 - 右上角的X按钮 */
.import-export-modal-close {
    cursor: pointer !important;
    font-size: 24px !important;
    color: #6b7280 !important;
    line-height: 1 !important;
    padding: 4px !important;
    user-select: none !important;
}

/* 关闭按钮悬停效果 */
.import-export-modal-close:hover {
    color: #374151 !important;
}

/* 模态框主体 - 包含消息文本和文件输入框的区域 */
.import-export-modal-body {
    padding: 20px 24px !important;
}

/* 消息文本 - 显示操作说明和提示信息 */
.import-export-modal-message {
    margin: 0 0 16px 0 !important;
    color: #374151 !important;
    line-height: 1.5 !important;
}

/* 文件输入框 - 用于选择要导入的文件 */
.import-export-modal-file-input {
    display: none !important;
    margin-bottom: 16px !important;
    width: 100% !important;
    padding: 8px 12px !important;
    border: 1px solid #d1d5db !important;
    border-radius: 4px !important;
    font-size: 14px !important;
    box-sizing: border-box !important;
}

/* 文件输入框显示状态 - 通过JavaScript控制显示 */
.import-export-modal-file-input.show {
    display: block !important;
}

/* 模态框底部 - 包含操作按钮的区域 */
.import-export-modal-footer {
    padding: 16px 24px 20px !important;
    border-top: 1px solid #e5e7eb !important;
    display: flex !important;
    justify-content: flex-end !important;
    gap: 12px !important;
}

/* 按钮基础样式 - 所有模态框按钮的通用样式 */
.import-export-modal-btn {
    padding: 8px 16px !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    font-size: 14px !important;
    transition: all 0.2s !important;
    border: none !important;
}

/* 取消按钮 - 次要操作按钮样式 */
.import-export-modal-btn-cancel {
    border: 1px solid #d1d5db !important;
    background: white !important;
    color: #374151 !important;
}

/* 取消按钮悬停效果 */
.import-export-modal-btn-cancel:hover {
    background: #f9fafb !important;
}

/* 确认按钮 - 主要操作按钮样式 */
.import-export-modal-btn-confirm {
    background: #3b82f6 !important;
    color: white !important;
}

/* 确认按钮悬停效果 */
.import-export-modal-btn-confirm:hover {
    background: #2563eb !important;
}

/* 隐藏按钮 - 通过JavaScript控制按钮显示/隐藏 */
.import-export-modal-btn.hide {
    display: none !important;
}

/* ==================== 单价管理页面专用样式 ==================== */

/* 单价管理容器 - 主要容器样式 */
.price-management-container {
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

/* 查询条件区域 - 搜索和筛选功能区域 */
.price-management-container .query-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 查询行布局 - 查询条件和按钮的布局 */
.price-management-container .query-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    gap: 20px;
    flex-wrap: wrap;
}

/* 查询表单区域 - 输入框和下拉框区域 */
.price-management-container .query-form {
    display: flex;
    gap: 20px;
    align-items: flex-end;
    flex-wrap: wrap;
    flex: 1;
}

/* 查询项样式 - 单个查询条件的样式 */
.price-management-container .query-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

/* 查询项标签 - 查询条件的标签样式 */
.price-management-container .query-item label {
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

/* 查询输入框 - 文本输入框样式 */
.price-management-container .query-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    width: 200px;
    transition: border-color 0.3s ease;
}

.price-management-container .query-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 查询下拉框 - 选择框样式 */
.price-management-container .query-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    width: 150px;
    background: white;
    transition: border-color 0.3s ease;
}

.price-management-container .query-select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 查询按钮区域 - 操作按钮区域 */
.price-management-container .query-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* 表格区域 - 数据表格容器 */
.price-management-container .table-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 表格容器 - 表格滚动容器 */
.price-management-container .table-container {
    overflow-x: auto;
    margin-bottom: 20px;
}

/* 数据表格 - 主要数据表格样式 */
.price-management-container .data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.price-management-container .data-table th,
.price-management-container .data-table td {
    padding: 12px 8px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.price-management-container .data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
    z-index: 10;
}

.price-management-container .data-table tbody tr:hover {
    background: #f8f9fa;
}

/* 固定列样式 - 重要列固定显示 */
.price-management-container .data-table .fixed-col {
    min-width: 120px;
    font-weight: 500;
}

.price-management-container .data-table .fixed-col-right {
    min-width: 120px;
    text-align: center;
}

/* 操作按钮组 - 表格中的操作按钮 */
.price-management-container .action-buttons {
    display: flex;
    gap: 5px;
    justify-content: center;
}

/* 分页区域 - 分页控件容器 */
.price-management-container .pagination-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

/* 分页信息 - 记录数信息显示 */
.price-management-container .pagination-info {
    color: #666;
    font-size: 14px;
}

/* 分页控件 - 分页按钮组 */
.price-management-container .pagination-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 分页页码 - 页码按钮样式 */
.price-management-container .pagination-pages {
    display: flex;
    gap: 5px;
    align-items: center;
}

.price-management-container .pagination-page {
    padding: 6px 12px;
    border: 1px solid #ddd;
    background: white;
    color: #333;
    cursor: pointer;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.price-management-container .pagination-page:hover {
    background: #f8f9fa;
    border-color: #007bff;
}

.price-management-container .pagination-page.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

/* 响应式设计 - 移动端适配 */
@media (max-width: 768px) {
    .price-management-container .query-row {
        flex-direction: column;
        align-items: stretch;
    }

    .price-management-container .query-form {
        flex-direction: column;
        gap: 15px;
    }

    .price-management-container .query-input,
    .price-management-container .query-select {
        width: 100%;
    }

    .price-management-container .query-buttons {
        justify-content: center;
    }

    .price-management-container .pagination-section {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
}

/* ==================== 本体费用计算逻辑管理样式 ==================== */

/* 本体费用管理容器 */
.ontology-management-container {
    padding: 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: var(--box-shadow);
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* 本体费用查询区域 */
.ontology-query-section {
    margin-bottom: 20px;
    padding: 15px;
    background-color: var(--light-gray);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.ontology-query-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.ontology-query-form {
    display: flex;
    gap: 20px;
    flex: 1;
}

.ontology-query-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.ontology-query-item label {
    font-weight: 500;
    color: var(--text-color);
    white-space: nowrap;
    min-width: 80px;
}

.ontology-query-input {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    width: 200px;
    transition: var(--transition);
}

.ontology-query-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 113, 227, 0.1);
}

.ontology-query-buttons {
    display: flex;
    gap: 10px;
}

/* 本体费用表格区域 */
.ontology-table-section {
    flex: 1;
    margin-bottom: 20px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.ontology-table-container {
    flex: 1;
    overflow: auto;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: white;
}

/* 本体费用分页区域 */
.ontology-pagination-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-top: 1px solid var(--border-color);
}

.ontology-pagination-info {
    color: var(--secondary-color);
    font-size: 14px;
}

.ontology-pagination-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 本体费用分页页码样式 */
.ontology-pagination-controls .pagination-pages {
    display: flex;
    align-items: center;
    gap: 5px;
}

.ontology-pagination-controls .pagination-page {
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: white;
    color: var(--text-color);
    cursor: pointer;
    font-size: 14px;
    transition: var(--transition);
    user-select: none;
}

.ontology-pagination-controls .pagination-page:hover {
    background-color: var(--light-gray);
    border-color: var(--primary-color);
}

.ontology-pagination-controls .pagination-page.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* 本体费用响应式设计 */
@media (max-width: 768px) {
    .ontology-query-row {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .ontology-query-form {
        flex-direction: column;
        gap: 10px;
    }

    .ontology-query-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .ontology-query-item label {
        margin-bottom: 5px;
        min-width: auto;
    }

    .ontology-query-input {
        width: 100%;
    }

    .ontology-query-buttons {
        flex-wrap: wrap;
        gap: 8px;
    }

    .ontology-pagination-section {
        flex-direction: column;
        gap: 10px;
        align-items: center;
    }
}

/* ==================== 本体费用模态框专用样式 ==================== */

/* 本体费用编辑模态框背景遮罩 */
#ontology-edit-modal.modal {
    display: none !important;
    position: fixed !important;
    z-index: 1000 !important;
    left: 0 !important;
    top: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    justify-content: center !important;
    align-items: center !important;
    max-width: none !important;
    max-height: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    transform: none !important;
    transition: none !important;
    overflow: visible !important;
}

/* 本体费用模态框显示状态 */
#ontology-edit-modal.modal.show {
    display: flex !important;
}

/* 通用模态框背景遮罩（备用） */
.ontology-modal {
    display: none !important;
    position: fixed !important;
    z-index: 1000 !important;
    left: 0 !important;
    top: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    justify-content: center !important;
    align-items: center !important;
}

.ontology-modal.show {
    display: flex !important;
}

/* 本体费用模态框内容容器 */
#ontology-edit-modal .modal-content {
    background-color: #fff !important;
    border-radius: 8px !important;
    width: 90% !important;
    max-width: 650px !important;
    max-height: 90vh !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
    animation: ontologyModalSlideIn 0.3s ease-out;
    overflow: hidden !important;
    margin: 0 !important;
    position: relative !important;
    transform: none !important;
    transition: none !important;
}

/* 通用模态框内容容器（备用） */
.ontology-modal-content {
    background-color: #fff !important;
    border-radius: 8px !important;
    width: 90% !important;
    max-width: 650px !important;
    max-height: 90vh !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
    animation: ontologyModalSlideIn 0.3s ease-out;
    overflow: hidden !important;
    margin: 0 !important;
    position: relative !important;
}

/* 模态框动画 */
@keyframes ontologyModalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 模态框头部 */
.ontology-modal-header {
    padding: 20px 25px 15px 25px !important;
    border-bottom: 1px solid var(--border-color) !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    background-color: var(--light-gray) !important;
}

.ontology-modal-header h3 {
    margin: 0 !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    color: var(--text-color) !important;
}

/* 模态框关闭按钮 */
.ontology-modal-close {
    background: none !important;
    border: none !important;
    font-size: 24px !important;
    cursor: pointer !important;
    color: var(--secondary-color) !important;
    padding: 0 !important;
    width: 30px !important;
    height: 30px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 50% !important;
    transition: var(--transition) !important;
}

.ontology-modal-close:hover {
    background-color: rgba(0, 0, 0, 0.1) !important;
    color: var(--text-color) !important;
}

/* 模态框主体 */
.ontology-modal-body {
    padding: 25px !important;
    max-height: 60vh !important;
    overflow-y: auto !important;
}

/* 模态框底部 */
.ontology-modal-footer {
    padding: 15px 25px 20px 25px !important;
    border-top: 1px solid var(--border-color) !important;
    display: flex !important;
    justify-content: flex-end !important;
    gap: 10px !important;
    background-color: var(--light-gray) !important;
}

/* 本体费用模态框响应式设计 */
@media (max-width: 768px) {
    .ontology-modal-content {
        width: 95% !important;
        max-width: none !important;
        margin: 10px !important;
    }

    .ontology-modal-header,
    .ontology-modal-body,
    .ontology-modal-footer {
        padding-left: 15px !important;
        padding-right: 15px !important;
    }
}
