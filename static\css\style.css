/* 技术经济成本平台样式表 - 苹果设计风格 */

/* 基础样式 */
:root {
    --primary-color: #0071e3;
    --primary-hover: #0077ed;
    --secondary-color: #86868b;
    --text-color: #1d1d1f;
    --light-gray: #f5f5f7;
    --border-color: #d2d2d7;
    --success-color: #34c759;
    --warning-color: #ff9500;
    --error-color: #ff3b30;
    --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
    color: var(--text-color);
    line-height: 1.5;
    background-color: var(--light-gray);
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 排版 */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 0.5em;
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.5rem;
}

p {
    margin-bottom: 1rem;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-hover);
}

/* 头部 */
header {
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: saturate(180%) blur(20px);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--box-shadow);
    padding: 15px 0;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    font-size: 1.5rem;
    margin-bottom: 0;
}

nav ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

nav ul li a {
    color: var(--text-color);
    font-weight: 500;
    padding-bottom: 5px;
    border-bottom: 2px solid transparent;
}

nav ul li a:hover,
nav ul li a.active {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
}

/* 主要内容 */
main {
    padding: 40px 0;
    min-height: calc(100vh - 180px);
}

/* 页脚 */
footer {
    background-color: white;
    padding: 20px 0;
    text-align: center;
    border-top: 1px solid var(--border-color);
}

/* 按钮 */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 980px;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    outline: none;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: var(--light-gray);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: #e5e5e5;
    transform: translateY(-1px);
}

/* 卡片 */
.feature-card {
    background-color: white;
    border-radius: 20px;
    box-shadow: var(--box-shadow);
    padding: 30px;
    margin-bottom: 20px;
    transition: var(--transition);
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.feature-card:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-4px);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--light-gray);
    border-radius: 20px;
    transition: var(--transition);
}

.feature-card:hover .feature-icon {
    background-color: var(--primary-color);
    color: white;
}

.feature-card h2 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--text-color);
}

.feature-card p {
    color: var(--secondary-color);
    margin-bottom: 25px;
    line-height: 1.6;
}

/* 子功能链接 */
.feature-sub-links {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
}

.feature-sub-links .btn {
    padding: 8px 16px;
    font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    header .container {
        flex-direction: column;
        text-align: center;
    }

    nav ul {
        flex-direction: column;
        gap: 1rem;
        margin-top: 1rem;
    }

    .feature-sub-links {
        flex-direction: column;
        width: 100%;
    }

    .feature-sub-links .btn {
        width: 100%;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fadeIn {
    animation: fadeIn 0.6s ease-out forwards;
}

/* 功能卡片网格布局 */
.features-section {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin: 0 auto;
    max-width: 1200px;
    padding: 20px 0;
}

/* 英雄区域 */
.hero-section {
    text-align: center;
    padding: 60px 0;
    background: linear-gradient(180deg, white, var(--light-gray));
    border-radius: 30px;
    margin-bottom: 60px;
}

.hero-section h1 {
    font-size: 3rem;
    margin-bottom: 20px;
    background: linear-gradient(120deg, var(--primary-color), #00a0ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.hero-section p {
    font-size: 1.25rem;
    color: var(--secondary-color);
    max-width: 800px;
    margin: 0 auto 30px;
}

/* 步骤导航 */
.step-nav {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    position: relative;
}

.step-nav::before {
    content: '';
    position: absolute;
    top: 25px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--border-color);
    z-index: -1;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 1;
}

.step-number {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--light-gray);
    border: 2px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-bottom: 10px;
    transition: var(--transition);
}

.step.active .step-number {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.step.completed .step-number {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

/* 表格 */
.table-container {
    overflow-x: auto;
    margin-bottom: 20px;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    background-color: var(--light-gray);
    font-weight: 600;
}

tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* 文件上传 */
.file-upload {
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    margin-bottom: 20px;
    cursor: pointer;
    transition: var(--transition);
}

.file-upload:hover {
    border-color: var(--primary-color);
}

.file-upload-icon {
    font-size: 48px;
    color: var(--secondary-color);
    margin-bottom: 15px;
}

/* 提示框 */
.alert {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.alert-success {
    background-color: rgba(52, 199, 89, 0.1);
    border-left: 4px solid var(--success-color);
    color: #2a9d4a;
}

.alert-warning {
    background-color: rgba(255, 149, 0, 0.1);
    border-left: 4px solid var(--warning-color);
    color: #cc7800;
}

.alert-error {
    background-color: rgba(255, 59, 48, 0.1);
    border-left: 4px solid var(--error-color);
    color: #cc2f26;
}

/* 模态框 */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.modal-backdrop.show {
    opacity: 1;
    visibility: visible;
}

.modal {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    transform: translateY(-20px);
    transition: var(--transition);
}

.modal-backdrop.show .modal {
    transform: translateY(0);
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
}

.modal-footer button {
    margin-left: 10px;
}

.close-modal {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--secondary-color);
}

/* 响应式布局 */
@media (max-width: 1200px) {
    .features-section {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .features-section {
        grid-template-columns: 1fr;
    }

    header .container {
        flex-direction: column;
        text-align: center;
    }

    nav ul {
        flex-direction: column;
        gap: 1rem;
        margin-top: 1rem;
    }

    .feature-sub-links {
        flex-direction: column;
        width: 100%;
    }

    .feature-sub-links .btn {
        width: 100%;
    }
}

/* 调整卡片内容以适应更窄的宽度 */
.feature-card {
    padding: 20px;
}

.feature-card h2 {
    font-size: 1.3rem;
}

.feature-card p {
    font-size: 0.95rem;
    margin-bottom: 20px;
}

.feature-icon {
    width: 60px;
    height: 60px;
    margin-bottom: 15px;
}

.feature-sub-links .btn {
    padding: 6px 12px;
    font-size: 0.85rem;
}

/* 详情模态框样式 */
.detail-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
}

.detail-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.detail-section h4 {
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 500;
    color: #333;
}

.detail-section p {
    margin: 0 0 10px 0;
    line-height: 1.5;
    color: #555;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 15px;
}

.detail-item {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 12px 15px;
}

.detail-label {
    font-size: 13px;
    color: #666;
    margin-bottom: 5px;
}

.detail-value {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.detail-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.detail-table th {
    background-color: #f1f3f5;
    padding: 10px;
    text-align: left;
    font-weight: 500;
    border-bottom: 2px solid #dee2e6;
}

.detail-table td {
    padding: 10px;
    border-bottom: 1px solid #e9ecef;
}

.detail-table .highlight-row {
    background-color: #e7f1ff;
}

.detail-table .total-row {
    font-weight: 600;
    background-color: #f8f9fa;
}

.detail-table .total-row td {
    border-top: 2px solid #dee2e6;
}

/* 方案展示样式 */
.schemes-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.scheme-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    background-color: #fff;
    overflow: hidden;
    transition: all 0.3s ease;
}

.scheme-card.selected {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.scheme-header {
    padding: 15px 20px;
    background-color: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e0e0e0;
}

.scheme-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

.scheme-title h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.scheme-badge {
    background-color: #28a745;
    color: white;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.scheme-summary {
    display: flex;
    gap: 20px;
}

.scheme-price, .scheme-unit-price {
    font-size: 14px;
}

.scheme-price strong, .scheme-unit-price strong {
    font-size: 16px;
    color: #d9534f;
}

.scheme-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.scheme-body {
    padding: 20px;
}

.scheme-body h5 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 500;
    color: #555;
}

.module-cost-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.module-cost-item {
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #6c757d;
}

.module-cost-item.highlight {
    border-left-color: #007bff;
    background-color: #e7f1ff;
}

.module-cost-name {
    font-size: 14px;
    color: #555;
    margin-bottom: 5px;
}

.module-cost-value {
    font-size: 15px;
    font-weight: 500;
    color: #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.module-option {
    font-size: 12px;
    color: #007bff;
    font-weight: normal;
    background-color: rgba(0, 123, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
}

@media (max-width: 992px) {
    .scheme-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .scheme-summary {
        width: 100%;
        justify-content: space-between;
    }
    
    .scheme-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .module-cost-grid {
        grid-template-columns: 1fr;
    }
}

/* 首页特定样式 */
.info-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.info-card {
    background-color: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: var(--box-shadow);
}

.info-card h3 {
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.info-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.info-list li {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.info-icon {
    margin-right: 15px;
    color: var(--primary-color);
}

.info-text {
    font-weight: 500;
}

.subtitle {
    font-size: 1.2rem;
    color: var(--secondary-color);
    margin-bottom: 30px;
}

.feature-card {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    padding: 32px 24px;
    width: 320px;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: box-shadow 0.2s;
}

.feature-card:hover {
    box-shadow: 0 4px 16px rgba(37,99,235,0.10);
}

.feature-card h2 {
    font-size: 1.25rem;
    font-weight: bold;
    margin-bottom: 16px;
    color: #2563eb;
}

.feature-card p {
    color: #555;
    font-size: 1rem;
    margin-bottom: 20px;
    text-align: left;
}

.feature-icon {
    margin-bottom: 16px;
    color: #2563eb;
}

.info-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.info-card {
    background-color: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: var(--box-shadow);
}

.info-card h3 {
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.info-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.info-list li {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.info-icon {
    margin-right: 15px;
    color: var(--primary-color);
}

.info-text {
    font-weight: 500;
}

/* 方案展示样式 */
.schemes-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.scheme-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    background-color: #fff;
    overflow: hidden;
    transition: all 0.3s ease;
}

.scheme-card.selected {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.scheme-header {
    padding: 15px 20px;
    background-color: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e0e0e0;
}

.scheme-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

.scheme-title h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.scheme-badge {
    background-color: #28a745;
    color: white;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.scheme-summary {
    display: flex;
    gap: 20px;
}

.scheme-price, .scheme-unit-price {
    font-size: 14px;
}

.scheme-price strong, .scheme-unit-price strong {
    font-size: 16px;
    color: #d9534f;
}

.scheme-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.scheme-body {
    padding: 20px;
}

.scheme-body h5 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 500;
    color: #555;
}

.module-cost-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.module-cost-item {
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #6c757d;
}

.module-cost-item.highlight {
    border-left-color: #007bff;
    background-color: #e7f1ff;
}

.module-cost-name {
    font-size: 14px;
    color: #555;
    margin-bottom: 5px;
}

.module-cost-value {
    font-size: 15px;
    font-weight: 500;
    color: #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.module-option {
    font-size: 12px;
    color: #007bff;
    font-weight: normal;
    background-color: rgba(0, 123, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
}

@media (max-width: 992px) {
    .scheme-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .scheme-summary {
        width: 100%;
        justify-content: space-between;
    }
    
    .scheme-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .module-cost-grid {
        grid-template-columns: 1fr;
    }
}

/* 详情模态框样式 */
.detail-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
}

.detail-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.detail-section h4 {
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 500;
    color: #333;
}

.detail-section p {
    margin: 0 0 10px 0;
    line-height: 1.5;
    color: #555;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 15px;
}

.detail-item {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 12px 15px;
}

.detail-label {
    font-size: 13px;
    color: #666;
    margin-bottom: 5px;
}

.detail-value {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.detail-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.detail-table th {
    background-color: #f1f3f5;
    padding: 10px;
    text-align: left;
    font-weight: 500;
    border-bottom: 2px solid #dee2e6;
}

.detail-table td {
    padding: 10px;
    border-bottom: 1px solid #e9ecef;
}

.detail-table .highlight-row {
    background-color: #e7f1ff;
}

.detail-table .total-row {
    font-weight: 600;
    background-color: #f8f9fa;
}

.detail-table .total-row td {
    border-top: 2px solid #dee2e6;
}

/* 响应式布局 */
@media (max-width: 900px) {
    .features-section {
        flex-direction: column;
        align-items: center;
    }
    .feature-card {
        width: 90%;
    }
} 

/* 工程信息面板样式 - 强制覆盖 */
.project-info-panel {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    background: white !important;
    padding: 1rem 1.5rem !important;
    margin-bottom: 1rem !important;
    border-radius: 4px !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03) !important;
    flex-wrap: wrap !important;
    gap: 1rem !important;
    align-items: center !important;
    z-index: 100 !important; /* 确保位于顶层 */
    width: 100% !important;
    box-sizing: border-box !important;
}

.project-info-item {
    display: flex !important;
    align-items: center !important;
    margin-right: 20px !important;
    min-width: 150px !important;
}

.project-info-item label {
    font-size: 0.875rem !important;
    color: rgba(0, 0, 0, 0.65) !important;
    margin: 0 !important;
    margin-right: 8px !important;
    display: inline-block !important;
    white-space: nowrap !important;
}

.project-info-item .info-value {
    font-size: 0.875rem !important;
    font-weight: 700 !important; /* 加粗 */
    color: #333 !important;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    min-width: 60px !important;
    overflow: visible !important;
    white-space: nowrap !important;
}

.project-info-actions {
    margin-left: auto !important;
    display: flex !important;
    align-items: center !important;
}

/* 确保强制可见性设置 */
#projectName, #voltageLevel, #totalLineLength {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: #333 !important;
    font-weight: bold !important;
    min-width: 60px !important;
    overflow: visible !important;
}

/* 确保按钮样式正确 */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.2rem;
} 