<!-- 校审数据库页面内容 -->
<div class="indicator-rule-card">
    <h3>校审数据库</h3>
    <p>管理校审相关的数据库配置和数据源，支持数据库连接、数据同步和备份等操作。</p>
    
    <!-- 操作按钮区域 -->
    <div style="margin-bottom: 20px;">
        <button class="indicator-rule-btn indicator-rule-btn-primary">新增数据源</button>
        <button class="indicator-rule-btn indicator-rule-btn-secondary">测试连接</button>
        <button class="indicator-rule-btn indicator-rule-btn-secondary">数据同步</button>
        <button class="indicator-rule-btn indicator-rule-btn-secondary">数据备份</button>
    </div>
    
    <!-- 数据库连接状态 -->
    <div style="margin-bottom: 20px; padding: 15px; background-color: var(--light-gray); border-radius: 8px;">
        <h4 style="margin-bottom: 10px;">数据库连接状态</h4>
        <div style="display: flex; gap: 20px; align-items: center;">
            <div style="display: flex; align-items: center; gap: 8px;">
                <div style="width: 12px; height: 12px; background-color: var(--success-color); border-radius: 50%;"></div>
                <span>主数据库：已连接</span>
            </div>
            <div style="display: flex; align-items: center; gap: 8px;">
                <div style="width: 12px; height: 12px; background-color: var(--success-color); border-radius: 50%;"></div>
                <span>备份数据库：已连接</span>
            </div>
            <div style="display: flex; align-items: center; gap: 8px;">
                <div style="width: 12px; height: 12px; background-color: var(--warning-color); border-radius: 50%;"></div>
                <span>外部数据源：连接中</span>
            </div>
        </div>
    </div>
    
    <!-- 数据源列表 -->
    <table class="indicator-rule-table">
        <thead>
            <tr>
                <th>序号</th>
                <th>数据源名称</th>
                <th>数据库类型</th>
                <th>服务器地址</th>
                <th>数据库名</th>
                <th>连接状态</th>
                <th>最后同步</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>1</td>
                <td>主校审数据库</td>
                <td>MySQL</td>
                <td>192.168.1.100</td>
                <td>audit_main</td>
                <td><span style="color: var(--success-color);">已连接</span></td>
                <td>2024-01-30 10:30</td>
                <td>
                    <button class="indicator-rule-btn indicator-rule-btn-secondary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                    <button class="indicator-rule-btn indicator-rule-btn-secondary" style="padding: 6px 12px; font-size: 12px; margin-left: 5px;">测试</button>
                </td>
            </tr>
            <tr>
                <td>2</td>
                <td>历史数据库</td>
                <td>PostgreSQL</td>
                <td>192.168.1.101</td>
                <td>audit_history</td>
                <td><span style="color: var(--success-color);">已连接</span></td>
                <td>2024-01-30 09:15</td>
                <td>
                    <button class="indicator-rule-btn indicator-rule-btn-secondary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                    <button class="indicator-rule-btn indicator-rule-btn-secondary" style="padding: 6px 12px; font-size: 12px; margin-left: 5px;">测试</button>
                </td>
            </tr>
            <tr>
                <td>3</td>
                <td>外部数据源</td>
                <td>Oracle</td>
                <td>192.168.1.102</td>
                <td>external_data</td>
                <td><span style="color: var(--warning-color);">连接中</span></td>
                <td>2024-01-29 16:45</td>
                <td>
                    <button class="indicator-rule-btn indicator-rule-btn-secondary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                    <button class="indicator-rule-btn indicator-rule-btn-secondary" style="padding: 6px 12px; font-size: 12px; margin-left: 5px;">测试</button>
                </td>
            </tr>
        </tbody>
    </table>
</div>

<!-- 数据同步配置 -->
<div class="indicator-rule-card">
    <h3>数据同步配置</h3>
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
        <div>
            <h4 style="margin-bottom: 10px;">同步设置</h4>
            <div style="margin-bottom: 10px;">
                <label style="display: block; margin-bottom: 5px;">同步频率：</label>
                <select style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                    <option value="realtime">实时同步</option>
                    <option value="hourly">每小时</option>
                    <option value="daily" selected>每日</option>
                    <option value="weekly">每周</option>
                </select>
            </div>
            <div style="margin-bottom: 10px;">
                <label style="display: block; margin-bottom: 5px;">同步时间：</label>
                <input type="time" value="02:00" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
            </div>
        </div>
        <div>
            <h4 style="margin-bottom: 10px;">备份设置</h4>
            <div style="margin-bottom: 10px;">
                <label style="display: block; margin-bottom: 5px;">备份频率：</label>
                <select style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
                    <option value="daily">每日</option>
                    <option value="weekly" selected>每周</option>
                    <option value="monthly">每月</option>
                </select>
            </div>
            <div style="margin-bottom: 10px;">
                <label style="display: block; margin-bottom: 5px;">保留天数：</label>
                <input type="number" value="30" style="width: 100%; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
            </div>
        </div>
    </div>
    <div style="margin-top: 15px;">
        <button class="indicator-rule-btn indicator-rule-btn-primary">保存配置</button>
        <button class="indicator-rule-btn indicator-rule-btn-secondary" style="margin-left: 10px;">立即同步</button>
        <button class="indicator-rule-btn indicator-rule-btn-secondary" style="margin-left: 10px;">立即备份</button>
    </div>
</div>

<!-- 数据库统计 -->
<div class="indicator-rule-card">
    <h3>数据库统计</h3>
    <div style="display: flex; gap: 20px;">
        <div style="text-align: center;">
            <div style="font-size: 24px; font-weight: bold; color: var(--primary-color);">3</div>
            <div style="color: var(--secondary-color);">数据源总数</div>
        </div>
        <div style="text-align: center;">
            <div style="font-size: 24px; font-weight: bold; color: var(--success-color);">2</div>
            <div style="color: var(--secondary-color);">正常连接</div>
        </div>
        <div style="text-align: center;">
            <div style="font-size: 24px; font-weight: bold; color: var(--warning-color);">1</div>
            <div style="color: var(--secondary-color);">连接异常</div>
        </div>
        <div style="text-align: center;">
            <div style="font-size: 24px; font-weight: bold; color: var(--error-color);">15GB</div>
            <div style="color: var(--secondary-color);">数据总量</div>
        </div>
    </div>
</div>
