/**
 * 单价管理模块 JavaScript
 * 专门负责单价管理页面的所有功能
 * 包括单价数据的增删改查、分页、搜索等功能
 */

// ==================== 模块配置和状态管理 ====================

/**
 * 单价管理模块配置
 */
const PriceManagement = {
    // 分页状态管理
    pagination: {
        currentPage: 1,
        pageSize: 10,
        totalRecords: 0,
        totalPages: 0
    },

    // API 端点配置
    api: {
        list: '/api/indicator_rule/price/list',
        detail: '/api/indicator_rule/price',
        create: '/api/indicator_rule/price',
        update: '/api/indicator_rule/price',
        delete: '/api/indicator_rule/price/delete',
        categories: '/api/indicator_rule/price/categories'
    },

    // DOM 元素缓存
    elements: {}
};

// ==================== 初始化功能 ====================

/**
 * 初始化单价管理页面
 */
function initPriceManagement() {
    console.log('初始化单价管理模块...');
    
    // 缓存DOM元素
    cachePriceElements();
    
    // 加载分类选项
    loadPriceCategories();

    // 加载默认数据
    loadPriceData();
    
    console.log('单价管理模块初始化完成');
}

/**
 * 缓存常用DOM元素
 */
function cachePriceElements() {
    PriceManagement.elements = {
        nameInput: document.getElementById('price-name-input'),
        categorySelect: document.getElementById('price-category-select'),
        tableBody: document.getElementById('price-table-body'),
        totalInfo: document.getElementById('price-total-info'),
        prevBtn: document.getElementById('price-prev-btn'),
        nextBtn: document.getElementById('price-next-btn'),
        paginationPages: document.getElementById('price-pagination-pages'),
        editModal: document.getElementById('price-edit-modal')
    };
}

// ==================== 网络请求功能 ====================

/**
 * 发送HTTP请求的通用方法
 * @param {string} url - 请求URL
 * @param {Object} options - 请求选项
 * @returns {Promise} 请求Promise
 */
async function httpRequest(url, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json'
        }
    };

    const finalOptions = { ...defaultOptions, ...options };
    
    try {
        const response = await fetch(url, finalOptions);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || `HTTP error! status: ${response.status}`);
        }
        
        return data;
    } catch (error) {
        console.error('HTTP请求失败:', error);
        throw error;
    }
}

// ==================== 数据加载功能 ====================

/**
 * 加载单价数据
 * @param {Object} searchParams - 搜索参数
 */
async function loadPriceData(searchParams = {}) {
    const pagination = PriceManagement.pagination;
    
    try {
        showLoadingState(true);
        
        // 构建请求参数
        const params = new URLSearchParams({
            ...searchParams,
            page: pagination.currentPage,
            pageSize: pagination.pageSize
        });

        const data = await httpRequest(`${PriceManagement.api.list}?${params}`);

        if (data.success) {
            // 更新分页信息
            pagination.totalRecords = data.total || 0;
            pagination.totalPages = Math.ceil(pagination.totalRecords / pagination.pageSize);

            // 渲染数据
            renderPriceTable(data.data || []);
            renderPricePagination();
        } else {
            showErrorState(data.message || '加载数据失败');
        }
    } catch (error) {
        console.error('加载单价数据失败:', error);
        showErrorState('加载数据出错: ' + error.message);
    }
}

/**
 * 加载分类选项
 */
async function loadPriceCategories() {
    try {
        const data = await httpRequest(PriceManagement.api.categories);
        
        if (data.success) {
            const categorySelect = PriceManagement.elements.categorySelect;
            const editCategorySelect = document.getElementById('price-edit-category');
            
            // 清空现有选项（保留"全部"选项）
            categorySelect.innerHTML = '<option value="">全部</option>';
            editCategorySelect.innerHTML = '<option value="">请选择分类</option>';
            
            // 添加分类选项
            data.data.forEach(category => {
                const option1 = new Option(category, category);
                const option2 = new Option(category, category);
                categorySelect.appendChild(option1);
                editCategorySelect.appendChild(option2);
            });
        }
    } catch (error) {
        console.error('加载分类选项失败:', error);
        // 使用默认分类选项
        const defaultCategories = ['基础工程', '架线工程', '杆塔工程', '附件工程', '辅助工程', '接地工程'];
        const categorySelect = PriceManagement.elements.categorySelect;
        const editCategorySelect = document.getElementById('price-edit-category');
        
        defaultCategories.forEach(category => {
            const option1 = new Option(category, category);
            const option2 = new Option(category, category);
            categorySelect.appendChild(option1);
            editCategorySelect.appendChild(option2);
        });
    }
}

/**
 * 分页加载单价数据（保持当前搜索条件）
 */
function loadPriceDataWithCurrentSearch() {
    const elements = PriceManagement.elements;
    const name = elements.nameInput.value.trim();
    const category = elements.categorySelect.value;

    const searchParams = {};
    if (name) searchParams.name = name;
    if (category) searchParams.category = category;

    // 不重置页码，使用当前页码进行加载
    loadPriceData(searchParams);
}

// ==================== 表格渲染功能 ====================

/**
 * 渲染单价数据表格
 * @param {Array} data - 单价数据数组
 */
function renderPriceTable(data) {
    const tbody = PriceManagement.elements.tableBody;
    tbody.innerHTML = '';

    if (!data || data.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="6" style="text-align: center; color: var(--secondary-color);">暂无数据</td>';
        tbody.appendChild(row);
        return;
    }

    data.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="fixed-col">${item.序号 || ''}</td>
            <td class="fixed-col">${item.指标名称 || ''}</td>
            <td>${item.指标单位 || ''}</td>
            <td>${item.指标分类 || ''}</td>
            <td>${formatPrice(item.指标单价)}</td>
            <td class="fixed-col-right">
                <div class="action-buttons">
                    <button class="btn btn-info btn-sm" onclick="editPrice(${item.id || ''})">编辑</button>
                    <button class="btn btn-danger btn-sm" onclick="deletePrice(${item.id || ''})">删除</button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

/**
 * 格式化单价显示
 * @param {number} price - 单价数值
 * @returns {string} 格式化后的单价字符串
 */
function formatPrice(price) {
    if (price === null || price === undefined) return '';
    return parseFloat(price).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

/**
 * 显示加载状态
 * @param {boolean} show - 是否显示加载状态
 */
function showLoadingState(show) {
    const tbody = PriceManagement.elements.tableBody;
    if (show) {
        tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: var(--secondary-color);">加载中...</td></tr>';
    }
}

/**
 * 显示错误状态
 * @param {string} message - 错误消息
 */
function showErrorState(message) {
    const tbody = PriceManagement.elements.tableBody;
    tbody.innerHTML = `<tr><td colspan="6" style="text-align: center; color: var(--error-color);">${message}</td></tr>`;
}

// ==================== 分页功能 ====================

/**
 * 渲染分页控件
 */
function renderPricePagination() {
    const pagination = PriceManagement.pagination;
    const elements = PriceManagement.elements;

    // 更新总记录数信息
    elements.totalInfo.textContent = `共 ${pagination.totalRecords} 条记录`;

    // 更新分页按钮状态
    elements.prevBtn.disabled = pagination.currentPage <= 1;
    elements.nextBtn.disabled = pagination.currentPage >= pagination.totalPages;

    // 渲染页码
    renderPaginationPages(pagination, elements.paginationPages);
}

/**
 * 渲染分页页码
 * @param {Object} pagination - 分页信息
 * @param {HTMLElement} container - 页码容器
 */
function renderPaginationPages(pagination, container) {
    container.innerHTML = '';

    if (pagination.totalPages > 0) {
        // 计算显示的页码范围
        let startPage = Math.max(1, pagination.currentPage - 2);
        let endPage = Math.min(pagination.totalPages, pagination.currentPage + 2);

        // 如果当前页靠近开始，显示更多后面的页码
        if (pagination.currentPage <= 3) {
            endPage = Math.min(pagination.totalPages, 5);
        }

        // 如果当前页靠近结束，显示更多前面的页码
        if (pagination.currentPage >= pagination.totalPages - 2) {
            startPage = Math.max(1, pagination.totalPages - 4);
        }

        // 渲染页码按钮
        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('span');
            pageBtn.className = `pagination-page ${i === pagination.currentPage ? 'active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.onclick = () => gotoPricePage(i);
            container.appendChild(pageBtn);
        }

        // 添加总页数显示
        const totalInfo = document.createElement('span');
        totalInfo.textContent = ` / ${pagination.totalPages}页`;
        totalInfo.style.marginLeft = '10px';
        totalInfo.style.color = 'var(--secondary-color)';
        container.appendChild(totalInfo);
    }
}

// ==================== 搜索和分页导航功能 ====================

/**
 * 搜索单价数据
 */
function searchPrices() {
    const elements = PriceManagement.elements;
    const name = elements.nameInput.value.trim();
    const category = elements.categorySelect.value;

    const searchParams = {};
    if (name) searchParams.name = name;
    if (category) searchParams.category = category;

    // 重置到第一页
    PriceManagement.pagination.currentPage = 1;
    loadPriceData(searchParams);
}

/**
 * 重置查询表单
 */
function resetPriceForm() {
    const elements = PriceManagement.elements;
    elements.nameInput.value = '';
    elements.categorySelect.value = '';

    // 重置到第一页
    PriceManagement.pagination.currentPage = 1;
    loadPriceData();
}

/**
 * 分页导航 - 上一页
 */
function prevPricePage() {
    const pagination = PriceManagement.pagination;
    if (pagination.currentPage > 1) {
        pagination.currentPage--;
        loadPriceDataWithCurrentSearch();
    }
}

/**
 * 分页导航 - 下一页
 */
function nextPricePage() {
    const pagination = PriceManagement.pagination;
    if (pagination.currentPage < pagination.totalPages) {
        pagination.currentPage++;
        loadPriceDataWithCurrentSearch();
    }
}

/**
 * 分页导航 - 跳转到指定页
 * @param {number} page - 目标页码
 */
function gotoPricePage(page) {
    const pagination = PriceManagement.pagination;
    if (page >= 1 && page <= pagination.totalPages) {
        pagination.currentPage = page;
        loadPriceDataWithCurrentSearch();
    }
}

// ==================== CRUD操作功能 ====================

/**
 * 新增单价
 */
function addPrice() {
    // 清空表单
    document.getElementById('price-edit-id').value = '';
    document.getElementById('price-edit-sequence').value = '';
    document.getElementById('price-edit-name').value = '';
    document.getElementById('price-edit-unit').value = '';
    document.getElementById('price-edit-category').value = '';
    document.getElementById('price-edit-price').value = '';

    // 显示模态框
    showPriceEditModal('新增单价信息');
}

/**
 * 编辑单价
 * @param {number} id - 单价ID
 */
async function editPrice(id) {
    console.log('编辑单价:', id);

    try {
        const data = await httpRequest(`${PriceManagement.api.detail}/${id}`);

        if (data.success) {
            const priceData = data.data;

            // 填充表单数据
            document.getElementById('price-edit-id').value = priceData.id || '';
            document.getElementById('price-edit-sequence').value = priceData.序号 || '';
            document.getElementById('price-edit-name').value = priceData.指标名称 || '';
            document.getElementById('price-edit-unit').value = priceData.指标单位 || '';
            document.getElementById('price-edit-category').value = priceData.指标分类 || '';
            document.getElementById('price-edit-price').value = priceData.指标单价 || '';

            // 显示模态框
            showPriceEditModal('编辑单价信息');
        } else {
            alert('获取单价信息失败: ' + data.message);
        }
    } catch (error) {
        console.error('获取单价信息失败:', error);
        alert('获取单价信息失败: ' + error.message);
    }
}

/**
 * 删除单价
 * @param {number} id - 单价ID
 */
async function deletePrice(id) {
    if (!confirm('确定要删除这个单价信息吗？')) {
        return;
    }

    try {
        const data = await httpRequest(`${PriceManagement.api.delete}/${id}`, {
            method: 'DELETE'
        });

        if (data.success) {
            alert('删除成功');
            loadPriceDataWithCurrentSearch(); // 重新加载当前页数据
        } else {
            alert('删除失败: ' + data.message);
        }
    } catch (error) {
        console.error('删除单价失败:', error);
        alert('删除失败: ' + error.message);
    }
}

/**
 * 保存单价数据
 */
async function savePriceData() {
    const id = document.getElementById('price-edit-id').value;
    const isNew = !id;

    // 收集表单数据
    const formData = {
        序号: parseInt(document.getElementById('price-edit-sequence').value) || 0,
        指标名称: document.getElementById('price-edit-name').value.trim(),
        指标单位: document.getElementById('price-edit-unit').value.trim(),
        指标分类: document.getElementById('price-edit-category').value,
        指标单价: parseFloat(document.getElementById('price-edit-price').value) || 0
    };

    // 验证数据
    if (!formData.指标名称) {
        alert('请输入指标名称');
        return;
    }
    if (!formData.指标单位) {
        alert('请输入计量单位');
        return;
    }
    if (!formData.指标分类) {
        alert('请选择指标分类');
        return;
    }
    if (formData.指标单价 <= 0) {
        alert('请输入有效的单价');
        return;
    }

    try {
        const url = isNew ? PriceManagement.api.create : `${PriceManagement.api.update}/${id}`;
        const method = isNew ? 'POST' : 'PUT';

        const data = await httpRequest(url, {
            method: method,
            body: JSON.stringify(formData)
        });

        if (data.success) {
            alert(isNew ? '新增成功' : '更新成功');
            closePriceEditModal();
            loadPriceDataWithCurrentSearch(); // 重新加载当前页数据
        } else {
            alert((isNew ? '新增' : '更新') + '失败: ' + data.message);
        }
    } catch (error) {
        console.error('保存单价数据失败:', error);
        alert('保存失败: ' + error.message);
    }
}

// ==================== 模态框操作功能 ====================

/**
 * 显示单价编辑模态框
 * @param {string} title - 模态框标题
 */
function showPriceEditModal(title) {
    const modal = PriceManagement.elements.editModal;
    const titleElement = modal.querySelector('.modal-header h3');

    titleElement.textContent = title;
    modal.style.display = 'flex';
}

/**
 * 关闭单价编辑模态框
 */
function closePriceEditModal() {
    const modal = PriceManagement.elements.editModal;
    modal.style.display = 'none';

    // 清空表单
    document.getElementById('price-edit-form').reset();
}

// ==================== 导入导出功能 ====================

/**
 * 导入单价数据
 */
function importPrices() {
    alert('导入功能开发中...');
}

/**
 * 导出单价数据
 */
function exportPrices() {
    alert('导出功能开发中...');
}

// ==================== 页面初始化和事件绑定 ====================

/**
 * 页面加载完成后初始化单价管理功能
 */
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否在单价管理页面
    if (document.querySelector('.price-management-container')) {
        console.log('检测到单价管理页面，开始初始化...');

        // 初始化单价管理模块
        initPriceManagement();

        // 添加模态框外部点击关闭功能
        window.addEventListener('click', function(event) {
            const priceModal = document.getElementById('price-edit-modal');

            if (event.target === priceModal) {
                closePriceEditModal();
            }
        });

        // 添加键盘事件监听（ESC键关闭模态框）
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closePriceEditModal();
            }
        });

        console.log('单价管理页面初始化完成');
    }
});

// ==================== 模块导出（如果需要） ====================

// 如果在模块化环境中使用，可以导出主要功能
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        PriceManagement,
        initPriceManagement,
        loadPriceData,
        searchPrices,
        addPrice,
        editPrice,
        deletePrice,
        savePriceData
    };
}
