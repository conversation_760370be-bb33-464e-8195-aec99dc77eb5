{% extends "base.html" %}

{% block head %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='css/pricing.css') }}">
{% endblock %}

{% block content %}
<div class="pricing-ws-container">
    <div class="pricing-ws-content pricing-ws-panels-collapsed" id="workspaceContent">
        <!-- 左侧工程管理面板 -->
        <div class="pricing-ws-project-panel">
            {% include "quick_pricing/project_management.html" %}
        </div>

        <!-- 右侧面板容器 -->
        <div class="pricing-ws-right-panels">
            <!-- 上方特征段管理面板 -->
            <div class="pricing-ws-section-panel">
                {% include "quick_pricing/section_management.html" %}
            </div>

            {# 下方指标选择面板 - 暂时屏蔽
            <div class="pricing-ws-indicator-panel">
                {% include "quick_pricing/section_pricingSum.html" %}
            </div>
            #}
        </div>
    </div>
</div>

<!-- 引入相关的JavaScript文件 -->
<script src="{{ url_for('static', filename='js/common.js') }}"></script>
<script src="{{ url_for('static', filename='js/project_management.js') }}"></script>
<script src="{{ url_for('static', filename='js/section_management.js') }}"></script>
{# 暂时屏蔽指标选择的JS文件
<script src="{{ url_for('static', filename='js/section_pricingSum.js') }}"></script>
#}

<script>
// 工作区管理对象
const workspaceManager = {
    // 初始化工作区
    init: function() {
        this.loadWorkspaceData();
        this.bindEvents();
        this.initPanelState();
    },

    // 初始化面板状态
    initPanelState: function() {
        const workspaceContent = document.querySelector('.pricing-ws-content');
        if (workspaceContent) {
            workspaceContent.classList.add('pricing-ws-panels-collapsed');
            workspaceContent.classList.remove('pricing-ws-panels-expanded');
        }
    },

    // 加载工作区数据
    loadWorkspaceData: function() {
        // 加载工程列表
        if (typeof loadProjectData === 'function') {
            loadProjectData();
        }
    },

    // 刷新工作区
    refreshWorkspace: function() {
        this.loadWorkspaceData();
    },

    // 绑定事件
    bindEvents: function() {
        // 监听工程创建成功事件
        document.addEventListener('projectCreated', () => {
            this.refreshWorkspace();
        });

        // 监听工程删除成功事件
        document.addEventListener('projectDeleted', () => {
            this.refreshWorkspace();
            // 重置面板状态
            if (typeof resetPanelState === 'function') {
                resetPanelState();
            }
        });
    }
};

// 全局方法：展开右侧特征段管理面板
window.expandWorkspacePanels = function() {
    const workspaceContent = document.getElementById('workspaceContent') || document.querySelector('.pricing-ws-content');
    if (workspaceContent) {
        workspaceContent.classList.remove('pricing-ws-panels-collapsed');
        workspaceContent.classList.add('pricing-ws-panels-expanded');
    }
};

// 全局方法：折叠右侧特征段管理面板
window.collapseWorkspacePanels = function() {
    const workspaceContent = document.getElementById('workspaceContent') || document.querySelector('.pricing-ws-content');
    if (workspaceContent) {
        workspaceContent.classList.add('pricing-ws-panels-collapsed');
        workspaceContent.classList.remove('pricing-ws-panels-expanded');
    }
};

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    workspaceManager.init();
});

// 导出全局刷新方法
window.refreshWorkspace = function() {
    workspaceManager.refreshWorkspace();
};
</script>
{% endblock %} 