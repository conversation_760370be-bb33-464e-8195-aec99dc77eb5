/* 以下内容来自hisIndQuery.html <style>标签，原样抽取 */
    /* 覆盖全局容器样式 */
    main {
        padding: 0;
        min-height: calc(100vh - 120px);
    }

    main .container {
        max-width: none;
        padding: 0;
        height: 100%;
    }

    /* ========== 历史线路项目数据查询tab 查询条件区：改为CSS Grid布局 ========== */
    .query-form {
        /* 原flex布局，已用Grid替换：
        display: flex;
        flex-wrap: wrap;
        */
        display: grid;
        grid-template-columns: repeat(10, 1fr); /* 每行6个输入框，可根据实际调整 */
        gap: 1rem 2rem;
        margin-bottom: 1rem;
        align-items: flex-end;
        padding: 1rem;
        box-sizing: border-box;
    }

    .form-group {
        /* 原flex宽度，已用Grid替换：
        flex: 0 0 180px;
        min-width: 160px;
        */
        min-width: 0;
        /* margin-bottom: 0.5rem; */
        display: flex;
        flex-direction: column;
        
    }
    
    .form-group label {
        margin-bottom: 0.5rem;
        color: var(--text-color);
        white-space: nowrap;
    }
    
    .form-group input,
    .form-group select {
        padding: 0.5rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 1rem;
        width: 100%;
    }

    .button-group {
        display: flex;
        gap: 1rem;
        margin-left: 0;
        white-space: nowrap;
        flex: 0 0 auto;
        align-items: flex-end;
    }
    
    .data-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 0.9rem;
    }
    
    .data-table th,
    .data-table td {
        padding: 0.75rem 1rem;
        text-align: left;
        border: 1px solid #ddd;
        white-space: nowrap;
    }
    
    .data-table th {
        background-color: #f5f5f5;
        font-weight: 500;
        position: sticky;
        top: 0;
        z-index: 1;
    }
    
    .data-table tbody tr:hover {
        background-color: #f5f5f5;
    }
    
    .table-container {
        overflow-x: auto;
        overflow-y: visible;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin: 0 1rem 0.5rem;
        background: #fff;
    }

    .tab-container {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .tab-buttons {
        display: flex;
        gap: 1rem;
        padding: 1rem 1rem 0;
        border-bottom: 1px solid #ddd;
    }

    .tab-button {
        padding: 0.75rem 1.5rem;
        border: none;
        background: none;
        cursor: pointer;
        font-size: 1rem;
        border-bottom: 2px solid transparent;
        transition: all 0.3s;
    }

    .tab-button.active {
        border-bottom: 2px solid #1890ff;
        color: #1890ff;
    }

    .tab-content {
        display: none;
        flex: 1;
        height: 100%;
        flex-direction: column;
    }

    .tab-content.active {
        display: flex;
        flex-direction: column;
    }

    .pagination {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
        padding: 0.5rem;
        background: white;
        border-top: 1px solid #ddd;
        margin: 0 1rem;
    }

    .pagination button {
        padding: 0.35rem 0.75rem;
        border: 1px solid #ddd;
        background: white;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;
        font-size: 0.9rem;
    }

    .pagination button:hover {
        background: #f5f5f5;
    }

    .pagination button.active {
        background: #1890ff;
        color: white;
        border-color: #1890ff;
    }

    .hidden-data {
        display: none;
    }

    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.4);
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    }

    .modal-overlay .modal {
        background: #fff;
        padding: 1.5rem;
        border-radius: 8px;
        max-width: 420px;
        width: 90%;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    .modal-overlay h3 {
        margin-top: 0;
        margin-bottom: 0.75rem;
    }

    .modal-overlay p {
        margin: 0.5rem 0 1rem;
        font-size: 0.9rem;
    }

    .modal-actions {
        display: flex;
        justify-content: flex-end;
        gap: 0.5rem;
    }
    .project-link {
        color: #1890ff;
        cursor: pointer;
        text-decoration: underline;
    }
    .modal-tabs {
        display: flex;
        border-bottom: 1px solid #ddd;
        margin-bottom: 1rem;
    }
    .modal-tab-btn {
        padding: 0.5rem 1.5rem;
        border: none;
        background: none;
        cursor: pointer;
        font-size: 1rem;
        border-bottom: 2px solid transparent;
        transition: all 0.3s;
    }
    .modal-tab-btn.active {
        border-bottom: 2px solid #1890ff;
        color: #1890ff;
    }
    .modal-tab-content {
        display: none;
    }
    .modal-tab-content.active {
        display: block;
    } 

/* 历史线路项目数据查询tab的按钮组单独一行并靠左 */
#tab1 .button-group {
    grid-column: 1 / -1;    /* 跨越所有列，单独一行 */
} 