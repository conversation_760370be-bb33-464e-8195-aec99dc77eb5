{% extends "base.html" %}

{% block title %}系统设置 - 电网线路工程造价分析平台{% endblock %}

{% block head %}
<style>
    .settings-section {
        background: white;
        border-radius: 12px;
        box-shadow: var(--box-shadow);
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .settings-form {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }
    
    .form-group {
        display: flex;
        flex-direction: column;
    }
    
    .form-group label {
        margin-bottom: 0.5rem;
        color: var(--text-color);
    }
    
    .form-group input,
    .form-group select {
        padding: 0.5rem;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        font-size: 1rem;
    }
    
    .form-group input[readonly] {
        background-color: var(--light-gray);
    }
    
    .form-group input[type="color"] {
        width: 3rem;
        height: 2.5rem;
        padding: 0;
    }
    
    .action-buttons {
        display: flex;
        justify-content: flex-start;
        align-items: flex-end;
    }
</style>
{% endblock %}

{% block content %}
<section class="content-header">
    <h1>系统设置</h1>
    <p>管理用户信息及平台系统参数，保障平台安全与高效运行。</p>
</section>

<!-- 用户信息区 -->
<section class="settings-section">
    <h2>用户信息</h2>
    <form class="settings-form">
        <div class="form-group">
            <label>用户名</label>
            <input type="text" value="admin" readonly>
        </div>
        <div class="form-group">
            <label>邮箱</label>
            <input type="email" value="<EMAIL>">
        </div>
        <div class="form-group">
            <label>角色</label>
            <input type="text" value="系统管理员" readonly>
        </div>
        <div class="action-buttons">
            <button type="button" class="btn btn-primary">保存信息</button>
        </div>
    </form>
</section>

<!-- 系统参数设置区 -->
<section class="settings-section">
    <h2>系统参数设置</h2>
    <form class="settings-form">
        <div class="form-group">
            <label>数据导出格式</label>
            <select>
                <option>Excel</option>
                <option>CSV</option>
                <option>PDF</option>
            </select>
        </div>
        <div class="form-group">
            <label>最大上传文件大小(MB)</label>
            <input type="number" value="20">
        </div>
        <div class="form-group">
            <label>平台主题色</label>
            <input type="color" value="#2563eb">
        </div>
        <div class="action-buttons">
            <button type="button" class="btn btn-secondary">保存设置</button>
        </div>
    </form>
</section>
{% endblock %}
