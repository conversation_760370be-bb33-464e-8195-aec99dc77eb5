// 工程管理相关的JavaScript代码
let currentProjectId = null;

// 渲染工程列表
window.renderProjectTable = function(projects) {
    console.log('Rendering projects:', projects); // 调试日志
    const tbody = document.getElementById('projectTableBody');
    const template = document.getElementById('projectRowTemplate');
    
    if (!tbody || !template) {
        console.error('找不到必要的DOM元素');
        return;
    }
    
    // 更新工程数量
    const projectCountElement = document.getElementById('projectCount');
    if (projectCountElement) {
        projectCountElement.textContent = projects.length;
    }
    
    // 清空现有内容
    tbody.innerHTML = '';
    
    // 处理空数据情况
    if (!Array.isArray(projects) || projects.length === 0) {
        const tr = document.createElement('tr');
        tr.innerHTML = '<td colspan="8" class="text-center">暂无工程数据</td>';
        tbody.appendChild(tr);
        return;
    }
    
    // 使用模板渲染每一行
    projects.forEach(project => {
        const clone = template.content.cloneNode(true);
        const tr = clone.querySelector('tr');
        
        // 填充基础数据
        tr.querySelectorAll('[data-field]').forEach(element => {
            const field = element.dataset.field;
            if (field === '组价状态' || field === '指标汇总状态') {
                const status = project[field] || (field === '组价状态' ? '未组价' : '未汇总');
                element.textContent = status;
                if (status === '已组价' || status === '已汇总') {
                    element.classList.add('pricing-pm-status-success');
                } else {
                    element.classList.add('pricing-pm-status-tag');
                }
            } else {
                element.textContent = project[field] || (field === '特征段数量' ? '0' : '-');
            }
        });
        
        // 设置按钮的项目ID
        tr.querySelectorAll('button').forEach(button => {
            button.dataset.projectId = project.序号;
            
            // 如果是查看组价按钮，根据组价状态显示/隐藏
            if (button.classList.contains('view-pricing-btn')) {
                button.style.display = project.组价状态 === '已组价' ? 'inline-block' : 'none';
            }
        });
        
        tbody.appendChild(tr);
    });
};

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    loadProjectData();
    
    // 绑定重置按钮事件
    const resetButton = document.querySelector('.query-form .btn-secondary');
    if (resetButton) {
        resetButton.addEventListener('click', function() {
            // 重置表单后重新加载数据
            document.getElementById('searchForm').reset();
            if (typeof window.refreshWorkspace === 'function') {
                window.refreshWorkspace();
            }
            
            // 重置面板状态
            resetPanelState();
        });
    }
});

// 重置面板状态
function resetPanelState() {
    // 重置当前选中的工程ID
    currentProjectId = null;
    
    // 移除所有行的选中状态
    const rows = document.querySelectorAll('#projectTableBody tr');
    rows.forEach(row => row.classList.remove('selected'));
    
    // 折叠面板
    const workspaceContent = document.querySelector('.workspace-content');
    if (workspaceContent) {
        workspaceContent.classList.add('panels-collapsed');
        workspaceContent.classList.remove('panels-expanded');
    }
}

// 导出重置面板状态函数，供其他模块使用
window.resetPanelState = resetPanelState;

// 加载工程列表
async function loadProjectData() {
    try {
        const response = await fetch('/api/pricing/projects');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        console.log('Loaded projects:', data); // 调试日志
        renderProjectTable(data.projects || []);
    } catch (error) {
        console.error('加载工程列表失败:', error);
        showToast('加载工程列表失败: ' + error.message, 'error');
    }
}

// 获取状态样式类
function getStatusClass(status) {
    switch (status) {
        case '已组价':
            return 'success';
        case '组价中':
            return 'warning';
        default:
            return 'default';
    }
}

// 保存新建工程
async function saveNewProject() {
    const formData = {
        工程名称: document.getElementById('projectName').value,
        电压等级: document.getElementById('voltageLevelNew').value,
        线路总长度: parseFloat(document.getElementById('lineLength').value),
        备注: document.getElementById('projectRemark').value
    };

    try {
        const response = await fetch('/api/pricing/projects', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });

        if (!response.ok) {
            throw new Error('创建工程失败');
        }

        showToast('工程创建成功', 'success');
        closeCreateProjectModal();

        // 刷新工程列表
        if (typeof loadProjectData === 'function') {
            await loadProjectData();
        }

        // 触发工程创建成功事件（供工作区其它面板监听）
        document.dispatchEvent(new CustomEvent('projectCreated'));
    } catch (error) {
        console.error('创建工程失败:', error);
        showToast('创建工程失败: ' + error.message, 'error');
    }
}

// 显示创建工程模态框
function showCreateProjectModal() {
    document.getElementById('createProjectModal').classList.add('show');
    document.getElementById('createProjectModal').style.display = 'flex';
    
    // 填充演示数据
    document.getElementById('projectName').value = '500kV东莞西南部受电通道工程';
    const voltageSelect = document.getElementById('voltageLevelNew');
    if (voltageSelect) {
        // voltageSelect.value = '500kV'; // 默认选中500kV
        // // 若未成功选中（浏览器兼容问题），回退到第一项
        // if (voltageSelect.value !== '500kV') {
        //     voltageSelect.selectedIndex = 0;
        // }
    }
    document.getElementById('lineLength').value = '50';
}

// 关闭创建工程模态框
function closeCreateProjectModal() {
    document.getElementById('createProjectModal').classList.remove('show');
    document.getElementById('createProjectModal').style.display = 'none';
    document.getElementById('createProjectForm').reset();
}

// 选择工程
function selectProject(button) {
    console.log('=============================================');
    console.log('selectProject被调用，原始按钮元素:', button);
    const projectId = button.dataset.projectId;
    console.log('从按钮data-project-id属性获取的工程ID:', projectId);
    
    // 确保projectId是有效的
    if (!projectId) {
        console.error('工程ID为空!');
        showToast('工程ID无效', 'error');
        return;
    }
    
    // 确保工程ID是数字
    const numericProjectId = parseInt(projectId);
    console.log('转换为数字后的工程ID:', numericProjectId);
    
    if (isNaN(numericProjectId)) {
        console.error('工程ID不是有效数字:', projectId);
        showToast('工程ID无效', 'error');
        return;
    }
    
    // 移除所有行的选中状态
    const rows = document.querySelectorAll('#projectTableBody tr');
    console.log(`移除${rows.length}行的选中状态`);
    rows.forEach(row => row.classList.remove('selected'));
    
    // 添加选中行的高亮样式
    const selectedRow = button.closest('tr');
    console.log('获取到的选中行:', selectedRow);
    
    if (selectedRow) {
        console.log('添加选中样式到行');
        selectedRow.classList.add('selected');
    }
    
    // 保存当前选中的工程ID
    currentProjectId = numericProjectId;
    console.log('设置currentProjectId:', currentProjectId);
    
    // 展开右侧特征段管理面板
    if (typeof window.expandWorkspacePanels === 'function') {
        window.expandWorkspacePanels();
    } else {
        console.warn('window.expandWorkspacePanels 不存在');
    }
    
    // 直接设置全局工程ID
    console.log('设置全局window.projectId前的值:', window.projectId);
    window.projectId = numericProjectId;
    console.log('设置全局window.projectId后的值:', window.projectId);
    
    // 构建特征段管理页面URL
    const featureSectionUrl = `/quick_pricing/feature_section?projectId=${numericProjectId}`;
    console.log('构建的特征段管理页面URL:', featureSectionUrl);
    
    // 检查loadProjectSections函数是否存在
    console.log('检查window.loadProjectSections函数是否存在:', typeof window.loadProjectSections === 'function');
    
    if (typeof window.loadProjectSections === 'function') {
        console.log('调用window.loadProjectSections，传递工程ID:', numericProjectId);
        
        // 先清空特征段表格
        const sectionTableBody = document.getElementById('sectionTableBody');
        if (sectionTableBody) {
            console.log('清空特征段表格');
            sectionTableBody.innerHTML = '<tr><td colspan="8" class="text-center">加载中...</td></tr>';
        } else {
            console.warn('找不到sectionTableBody元素');
        }
        
        // 调用loadProjectSections函数
        try {
            console.log('尝试调用window.loadProjectSections...');
            window.loadProjectSections(numericProjectId);
            console.log('window.loadProjectSections调用成功');
        } catch (error) {
            console.error('调用loadProjectSections失败，错误详情:', error);
            // 尝试直接调用loadSections
            console.log('检查window.loadSections函数是否存在:', typeof window.loadSections === 'function');
            if (typeof window.loadSections === 'function') {
                console.log('尝试直接调用window.loadSections');
                try {
                    window.loadSections();
                    console.log('window.loadSections调用成功');
                } catch (loadError) {
                    console.error('调用loadSections失败，错误详情:', loadError);
                    console.log('所有加载方法都失败，直接跳转到特征段管理页面');
                    window.location.href = featureSectionUrl;
                }
            } else {
                console.error('window.loadSections函数不存在，直接跳转到特征段管理页面');
                window.location.href = featureSectionUrl;
            }
        }
        // 延迟一点时间后检查window.projectId是否正确设置
        console.log('设置延迟检查...');
        setTimeout(() => {
            console.log('延迟检查开始');
            console.log('延迟检查window.projectId:', window.projectId);
            // 如果特征段没有加载，再次尝试
            console.log('再次检查window.loadSections函数是否存在:', typeof window.loadSections === 'function');
            if (typeof window.loadSections === 'function') {
                console.log('延迟调用window.loadSections');
                try {
                    window.loadSections();
                    console.log('延迟调用window.loadSections成功');
                } catch (delayedError) {
                    console.error('延迟调用loadSections失败，错误详情:', delayedError);
                    console.log('延迟调用也失败，直接跳转到特征段管理页面');
                    window.location.href = featureSectionUrl;
                }
            } else {
                console.error('window.loadSections函数在延迟检查中仍不存在，直接跳转');
                window.location.href = featureSectionUrl;
            }
        }, 500);
    } else {
        console.error('window.loadProjectSections函数不存在');
        // 尝试直接调用loadSections
        console.log('检查window.loadSections函数是否存在:', typeof window.loadSections === 'function');
        if (typeof window.loadSections === 'function') {
            console.log('尝试直接调用window.loadSections');
            try {
                window.loadSections();
                console.log('window.loadSections调用成功');
            } catch (error) {
                console.error('调用loadSections失败，错误详情:', error);
                console.log('加载特征段数据失败，直接跳转到特征段管理页面');
                window.location.href = featureSectionUrl;
            }
        } else {
            // 如果所有方法都不可用，则直接跳转到特征段管理页面
            console.log('所有方法都不可用，直接跳转到特征段管理页面');
            window.location.href = featureSectionUrl;
        }
    }
    console.log('=============================================');
    
    jumpButton.onclick = function() {
        console.log('点击直接跳转按钮');
        const url = `/quick_pricing/feature_section?projectId=${numericProjectId}`;
        console.log('即将跳转到URL:', url);
        window.location.href = url;
    };
    
    // 将按钮添加到页面中
    const buttonContainer = document.createElement('div');
    buttonContainer.id = 'jumpButtonContainer';
    buttonContainer.style.textAlign = 'center';
    buttonContainer.appendChild(jumpButton);
    
    // 移除可能已存在的按钮
    const existingContainer = document.getElementById('jumpButtonContainer');
    if (existingContainer) {
        existingContainer.remove();
    }
    
    // 添加到工作区内容区域
    const workspaceContentEl = document.querySelector('.workspace-content');
    if (workspaceContentEl) {
        console.log('添加直接跳转按钮到页面');
        workspaceContentEl.appendChild(buttonContainer);
    } else {
        console.warn('找不到工作区内容区域，尝试添加到body');
        document.body.appendChild(buttonContainer);
    }
}

// 删除工程
async function deleteProject(button) {
    const projectId = button.dataset.projectId;
    if (!confirm('确定要删除这个工程吗？')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/pricing/projects/${projectId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            // 检查响应的Content-Type
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                const errorData = await response.json();
                throw new Error(errorData.message || '删除失败');
            } else {
                // 如果不是JSON响应，获取状态文本
                throw new Error(`删除失败 (${response.status}: ${response.statusText})`);
            }
        }
        
        showToast('工程删除成功', 'success');
        
        // 重新加载工程列表
        await loadProjectData();
        
        // 触发工程删除成功事件
        document.dispatchEvent(new CustomEvent('projectDeleted'));
    } catch (error) {
        console.error('删除工程失败:', error);
        showToast(error.message || '删除工程失败', 'error');
    }
}

// 搜索工程
async function searchProjects() {
    try {
        const searchParams = new URLSearchParams({
            工程名称: document.getElementById('searchProjectName').value,
            电压等级: document.getElementById('searchVoltageLevel').value,
            线路总长度: document.getElementById('searchLineLength').value || ''
        });

        const response = await fetch(`/api/pricing/projects/search?${searchParams.toString()}`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        console.log('Search results:', data);
        renderProjectTable(data);
    } catch (error) {
        console.error('搜索工程失败:', error);
        showToast('搜索工程失败: ' + error.message, 'error');
    }
} 