// 特征段管理相关的JavaScript代码
let currentSectionId = null;
// 声明工程ID变量，并导出到window对象
window.projectId = null;

// 存储原始数据用于筛选
let originalSections = [];

// 添加演示数据
window.demoProjectInfo = {
    工程名称: '广州-海南500kV输电线路工程',
    电压等级: '500kV',
    线路总长度: '72.3'
};

// 直接初始化工程信息面板（用于API不可用时的备用方案）
function initProjectInfoWithDemo() {
    console.log('使用演示数据初始化工程信息面板');
    
    setTimeout(() => {
        const projectNameEl = document.getElementById('projectName');
        const voltageLevelEl = document.getElementById('voltageLevel');
        const totalLineLengthEl = document.getElementById('totalLineLength');
        
        if (projectNameEl) {
            projectNameEl.textContent = window.demoProjectInfo.工程名称;
            projectNameEl.style.display = 'inline-block';
            console.log('已初始化工程名称:', projectNameEl.textContent);
        }
        
        if (voltageLevelEl) {
            voltageLevelEl.textContent = window.demoProjectInfo.电压等级;
            voltageLevelEl.style.display = 'inline-block';
            console.log('已初始化电压等级:', voltageLevelEl.textContent);
        }
        
        if (totalLineLengthEl) {
            totalLineLengthEl.textContent = window.demoProjectInfo.线路总长度;
            totalLineLengthEl.style.display = 'inline-block';
            console.log('已初始化线路总长度:', totalLineLengthEl.textContent);
        }
        
        const projectInfoPanel = document.querySelector('.project-info-panel');
        if (projectInfoPanel) {
            projectInfoPanel.style.display = 'flex';
            projectInfoPanel.style.visibility = 'visible';
            projectInfoPanel.style.opacity = '1';
        }
    }, 500); // 延迟500ms确保DOM已加载
}

// 不再自动加载数据
document.addEventListener('DOMContentLoaded', function() {
    console.log('=====================================');
    console.log('section_management.js 页面加载完成');
    console.log('初始window.projectId:', window.projectId);
    console.log('当前页面URL:', window.location.href);
    
    // 导出loadSections函数到全局，供HTML文件使用
    window.loadSections = loadSections;
    console.log('loadSections函数已导出到全局');
    
    // 初始化工程信息面板（先显示演示数据）
    initProjectInfoWithDemo();
    
    // 从URL参数获取工程ID
    const urlProjectId = getProjectIdFromUrl();
    console.log('从getProjectIdFromUrl获取到的工程ID:', urlProjectId);
    
    if (urlProjectId) {
        console.log('从URL获取到工程ID，设置window.projectId =', urlProjectId);
        window.projectId = urlProjectId;
        // 自动加载特征段数据
        console.log('准备调用loadSections()加载特征段数据');
        loadSections();
    } else {
        console.warn('未从URL获取到工程ID，尝试使用已有的window.projectId:', window.projectId);
        if (window.projectId) {
            console.log('使用已有的window.projectId加载特征段数据');
            loadSections();
        } else {
            console.error('无法获取工程ID，无法加载特征段数据');
        }
    }
    console.log('=====================================');
});

// 从URL参数获取工程ID
function getProjectIdFromUrl() {
    console.log('调用getProjectIdFromUrl函数获取URL参数');
    const urlParams = new URLSearchParams(window.location.search);
    console.log('完整URL查询字符串:', window.location.search);
    console.log('解析的URL参数:', Object.fromEntries(urlParams.entries()));
    
    // 检查多种可能的参数名
    const projectIdFromProjectId = urlParams.get('projectId');
    const projectIdFromProject_id = urlParams.get('project_id');
    console.log('从projectId参数获取的值:', projectIdFromProjectId);
    console.log('从project_id参数获取的值:', projectIdFromProject_id);
    
    const projectId = projectIdFromProjectId || projectIdFromProject_id;
    console.log('最终使用的工程ID:', projectId);
    
    // 由于需要可能存在HTML中已经直接使用了window.projectId
    if (projectId) {
        console.log('将工程ID设置到全局window.projectId变量:', projectId);
        window.projectId = projectId;
    }
    
    return projectId;
}

// 加载特征段数据
async function loadSectionData() {
    try {
        console.log('开始加载特征段数据，当前工程ID:', window.projectId);
        if (!window.projectId) {
            console.log('未选择工程，不加载特征段数据');
            return [];
        }
        
        console.log('发起请求前，再次检查工程ID:', window.projectId);
        const url = `/api/pricing/projects/${window.projectId}/feature_sections`;
        console.log(`请求URL: ${url}`);
        
        const response = await fetch(url);
        console.log('请求响应状态:', response.status, response.statusText);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        let data = await response.json();
        console.log('从API加载的特征段数据:', data); 
        
        // 处理可能的数据包装
        if (data.feature_sections && Array.isArray(data.feature_sections)) {
            console.log('检测到数据包装在feature_sections字段中');
            data = data.feature_sections;
        }
        
        if (!Array.isArray(data)) {
            console.warn('API返回的数据不是数组:', data);
            return [];
        }
        
        return data; // 返回加载的数据
    } catch (error) {
        console.error('加载特征段数据失败:', error);
        window.showToast('加载特征段数据失败: ' + error.message, 'error');
        throw error; // 重新抛出错误，让调用者知道加载失败
    }
}

// 加载特征段数据并更新UI
async function loadSections() {
    console.log('loadSections被调用，当前projectId:', window.projectId);
    
    if (!window.projectId) {
        console.error('工程ID不存在，无法加载特征段数据');
        window.showToast('请先选择工程', 'error');
        return;
    }
    
    toggleLoading(true);
    
    try {
        // 首先更新工程信息面板
        await updateProjectInfoPanel();
        
        // 调用loadSectionData获取特征段数据
        const data = await loadSectionData();
        
        // 保存原始数据用于筛选
        originalSections = data || [];

// 渲染特征段表格
        renderSections(originalSections);
        
        // 更新特征段数量
        document.getElementById('sectionCount').textContent = originalSections.length;
        
    } catch (error) {
        console.error('加载特征段数据失败:', error);
        console.log('使用演示数据替代');
        
        // 使用演示数据
        const demoSections = [
            {
                "序号": "1",
                "特征段名称": "海南段 (25m跨区)",
                "线路长度": "12.5",
                "边界条件": {
                    "风速": "30",
                    "覆冰": "0",
                    "回路数": "双回路",
                    "导线规格": "JL/LB20A-630/45"
                },
                "组价计算状态": "已组价"
            },
            {
                "序号": "2",
                "特征段名称": "东莞段 (30m跨区)",
                "线路长度": "9.8",
                "边界条件": {
                    "风速": "28",
                    "覆冰": "0",
                    "回路数": "双回路",
                    "导线规格": "JL/LB20A-630/45"
                },
                "组价计算状态": "已组价"
            },
            {
                "序号": "3",
                "特征段名称": "广州段 (27m跨区)",
                "线路长度": "30",
                "边界条件": {
                    "风速": "27",
                    "覆冰": "0",
                    "回路数": "双回路",
                    "导线规格": "JL/LB20A-630/45"
                },
                "组价计算状态": "未组价"
            },
            {
                "序号": "4",
                "特征段名称": "广州段",
                "线路长度": "20",
                "边界条件": {
                    "风速": "25",
                    "覆冰": "0",
                    "回路数": "双回路",
                    "导线规格": "JL/LB20A-630/45"
                },
                "组价计算状态": "未组价"
            }
        ];
        
        // 保存演示数据用于筛选
        originalSections = demoSections;

        // 渲染特征段表格
        renderSections(originalSections);
        
        // 更新特征段数量
        document.getElementById('sectionCount').textContent = originalSections.length;
    } finally {
        toggleLoading(false);
    }
}

// 更新工程信息面板
async function updateProjectInfoPanel() {
    console.log('调用updateProjectInfoPanel函数，准备获取工程信息');
    
    // 如果工程ID不存在，则不更新
    if (!window.projectId) {
        console.error('工程ID不存在，无法更新工程信息面板');
        return;
    }

    try {
        let projectInfo = null;
        
        // 第一种方法：尝试直接使用detail API
        try {
            console.log('方法1: 使用detail API获取工程信息，工程ID:', window.projectId);
            const url = `/api/pricing/projects/${window.projectId}/detail`;
            console.log(`请求URL: ${url}`);
            
            const response = await fetch(url);
            console.log('API响应状态:', response.status, response.statusText);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            console.log('从API获取的工程信息完整数据:', data);
            console.log('数据类型:', typeof data, Array.isArray(data) ? 'Array' : 'Not Array');
            
            // 情况1: data直接是工程对象
            if (data && typeof data === 'object' && !Array.isArray(data) && data.工程名称) {
                console.log('情况1: data直接是工程对象');
                projectInfo = data;
            }
            // 情况2: data.project是工程对象
            else if (data && typeof data === 'object' && data.project && typeof data.project === 'object' && !Array.isArray(data.project) && data.project.工程名称) {
                console.log('情况2: data.project是工程对象');
                projectInfo = data.project;
            }
            // 情况3: data.project是工程数组，取第一个
            else if (data && typeof data === 'object' && data.project && Array.isArray(data.project) && data.project.length > 0) {
                console.log('情况3: data.project是数组，取第一个元素');
                projectInfo = data.project[0];
            }
            // 情况4: data是工程数组，取第一个
            else if (data && Array.isArray(data) && data.length > 0) {
                console.log('情况4: data是数组，取第一个元素');
                projectInfo = data[0];
            }
        } catch (detailError) {
            console.error('使用detail API获取工程信息失败:', detailError);
        }
        
        // 第二种方法：如果第一种方法失败，尝试从工程列表中获取
        if (!projectInfo) {
            console.log('方法2: 使用getProjectById从工程列表获取');
            projectInfo = await getProjectById(window.projectId);
        }
        
        // 第三种方法：如果前两种方法都失败，使用演示数据
        if (!projectInfo) {
            console.log('方法3: 使用演示数据');
            projectInfo = {
                工程名称: '广州-海南500kV输电线路工程',
                电压等级: '500kV',
                线路总长度: '72.3'
            };
            window.demoProjectInfo = projectInfo; // 存储到全局变量
            console.warn('使用演示数据作为后备方案');
        }
        
        // 记录工程信息的关键字段
        console.log('最终使用的工程信息:', projectInfo);
        console.log('工程名称:', projectInfo.工程名称);
        console.log('电压等级:', projectInfo.电压等级);
        console.log('线路总长度:', projectInfo.线路总长度);
        
        // 更新DOM元素
        updateProjectInfoDOM(projectInfo);
        
    } catch (error) {
        console.error('获取工程信息流程发生错误:', error);
        
        // 使用演示数据作为最终后备方案
        const demoProjectInfo = {
            工程名称: '广州-海南500kV输电线路工程',
            电压等级: '500kV',
            线路总长度: '72.3'
        };
        window.demoProjectInfo = demoProjectInfo;
        
        // 更新DOM元素
        updateProjectInfoDOM(demoProjectInfo);
    }
}

// 辅助函数：更新工程信息DOM元素
function updateProjectInfoDOM(projectInfo) {
    console.log('更新工程信息DOM元素');
    
    // 定位工程信息面板
    const projectInfoPanel = document.querySelector('.project-info-panel');
    if (!projectInfoPanel) {
        console.error('找不到工程信息面板元素');
        return;
    }

    try {
        // 尝试方式1：更新现有元素
        const projectNameEl = document.getElementById('projectName');
        const voltageLevelEl = document.getElementById('voltageLevel');
        const totalLineLengthEl = document.getElementById('totalLineLength');
        
        console.log('找到的DOM元素:', {
            projectNameEl,
            voltageLevelEl,
            totalLineLengthEl
        });
        
        // 如果DOM元素不存在或更新失败，尝试方式2
        let updateFailed = false;
        
        if (projectNameEl) {
            projectNameEl.textContent = projectInfo.工程名称 !== undefined ? projectInfo.工程名称 : '-';
            projectNameEl.style.display = 'inline-block';
            console.log('已更新工程名称显示为:', projectNameEl.textContent);
        } else {
            console.error('找不到projectName元素');
            updateFailed = true;
        }
        
        if (voltageLevelEl) {
            voltageLevelEl.textContent = projectInfo.电压等级 !== undefined ? projectInfo.电压等级 : '-';
            voltageLevelEl.style.display = 'inline-block';
            console.log('已更新电压等级显示为:', voltageLevelEl.textContent);
        } else {
            console.error('找不到voltageLevel元素');
            updateFailed = true;
        }
        
        if (totalLineLengthEl) {
            totalLineLengthEl.textContent = projectInfo.线路总长度 !== undefined ? projectInfo.线路总长度 : '-';
            totalLineLengthEl.style.display = 'inline-block';
            console.log('已更新线路总长度显示为:', totalLineLengthEl.textContent);
        } else {
            console.error('找不到totalLineLength元素');
            updateFailed = true;
        }
        
        // 方式2：如果方式1失败，直接设置innerHTML
        if (updateFailed) {
            console.log('方式1更新失败，尝试方式2: 直接设置innerHTML');
            projectInfoPanel.innerHTML = `
                <div class="project-info-item" style="margin-right: 20px !important;">
                    <label style="display: inline-block; margin-right: 8px !important;">工程名称:</label>
                    <span id="projectName" class="info-value" style="display: inline-block !important; visibility: visible !important; opacity: 1 !important; min-width: 100px !important; color: #333 !important; font-weight: bold !important;">${projectInfo.工程名称}</span>
                </div>
                <div class="project-info-item" style="margin-right: 20px !important;">
                    <label style="display: inline-block; margin-right: 8px !important;">电压等级:</label>
                    <span id="voltageLevel" class="info-value" style="display: inline-block !important; visibility: visible !important; opacity: 1 !important; min-width: 60px !important; color: #333 !important; font-weight: bold !important;">${projectInfo.电压等级}</span>
                </div>
                <div class="project-info-item" style="margin-right: 20px !important;">
                    <label style="display: inline-block; margin-right: 8px !important;">线路总长度(km):</label>
                    <span id="totalLineLength" class="info-value" style="display: inline-block !important; visibility: visible !important; opacity: 1 !important; min-width: 60px !important; color: #333 !important; font-weight: bold !important;">${projectInfo.线路总长度}</span>
                </div>
                <div class="project-info-actions">
                    <button id="refreshProjectInfo" class="btn btn-sm btn-primary" onclick="manualRefreshProjectInfo()">刷新工程信息</button>
                </div>
            `;
            console.log('已直接设置工程信息面板的innerHTML');
        }
        
        // 确保面板可见
        projectInfoPanel.style.display = 'flex';
        projectInfoPanel.style.visibility = 'visible';
        projectInfoPanel.style.opacity = '1';
        
        console.log('工程信息面板更新完成');
    } catch (domError) {
        console.error('更新DOM元素时出错:', domError);
        
        // 最后的备选方案：完全重建面板
        try {
            console.log('尝试最后的备选方案：完全重建面板');
            projectInfoPanel.innerHTML = `
                <div class="project-info-item" style="margin-right: 20px !important;">
                    <label style="display: inline-block; margin-right: 8px !important;">工程名称:</label>
                    <span id="projectName" class="info-value" style="display: inline-block !important; visibility: visible !important; opacity: 1 !important; min-width: 100px !important; color: #333 !important; font-weight: bold !important;">${projectInfo.工程名称}</span>
                </div>
                <div class="project-info-item" style="margin-right: 20px !important;">
                    <label style="display: inline-block; margin-right: 8px !important;">电压等级:</label>
                    <span id="voltageLevel" class="info-value" style="display: inline-block !important; visibility: visible !important; opacity: 1 !important; min-width: 60px !important; color: #333 !important; font-weight: bold !important;">${projectInfo.电压等级}</span>
                </div>
                <div class="project-info-item" style="margin-right: 20px !important;">
                    <label style="display: inline-block; margin-right: 8px !important;">线路总长度(km):</label>
                    <span id="totalLineLength" class="info-value" style="display: inline-block !important; visibility: visible !important; opacity: 1 !important; min-width: 60px !important; color: #333 !important; font-weight: bold !important;">${projectInfo.线路总长度}</span>
                </div>
                <div class="project-info-actions">
                    <button id="refreshProjectInfo" class="btn btn-sm btn-primary" onclick="manualRefreshProjectInfo()">刷新工程信息</button>
                </div>
            `;
            
            // 强制样式
            projectInfoPanel.style.cssText = "display: flex !important; visibility: visible !important; opacity: 1 !important; background: white !important; padding: 1rem 1.5rem !important; margin-bottom: 1rem !important; border-radius: 4px !important; box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03) !important; width: 100% !important;";
            
            console.log('已重建工程信息面板');
        } catch (finalError) {
            console.error('最终备选方案也失败:', finalError);
        }
    }
}

// 渲染特征段数据
function renderSections(sections) {
    const tbody = document.getElementById('sectionTableBody');
    if (!tbody) {
        console.error('找不到sectionTableBody元素');
        return;
    }
    
    // 清空表格
    tbody.innerHTML = '';
    
    if (!Array.isArray(sections) || sections.length === 0) {
        console.log('没有特征段数据或数据不是数组');
        const tr = document.createElement('tr');
        tr.innerHTML = '<td colspan="8" class="text-center">暂无特征段数据</td>';
        tbody.appendChild(tr);
        return;
    }
    
    // 获取模板元素，如果不存在则使用备选方案
    const templateEl = document.getElementById('sectionRowTemplate');
    
    sections.forEach(section => {
        let row;
        
        // 处理边界条件数据
        // 修改获取边界条件的逻辑，确保0值也能正确显示
        const 风速 = section.边界条件?.风速 !== undefined ? section.边界条件.风速 : (section.风速 !== undefined ? section.风速 : '-');
        const 覆冰 = section.边界条件?.覆冰 !== undefined ? section.边界条件.覆冰 : (section.覆冰 !== undefined ? section.覆冰 : '-');
        const 回路数 = section.边界条件?.回路数 || section.回路数 || '-';
        const 导线规格 = section.边界条件?.导线规格 || section.导线规格 || '-';
        
        console.log('处理特征段数据:', section);
        console.log('处理后的边界条件:', { 风速, 覆冰, 回路数, 导线规格 });
        
        // 如果模板存在并且支持content属性，则使用模板
        if (templateEl && templateEl.content) {
            // 克隆模板
            const clone = document.importNode(templateEl.content, true);
            
            // 填充数据
            const fields = clone.querySelectorAll('[data-field]');
            fields.forEach(field => {
                const fieldName = field.getAttribute('data-field');
                
                // 根据字段名称设置值
                switch(fieldName) {
                    case '特征段名称':
                        field.textContent = section.特征段名称 || '-';
                        break;
                    case '线路长度':
                        field.textContent = section.线路长度 || '-';
                        break;
                    case '风速':
                        field.textContent = 风速;
                        break;
                    case '覆冰':
                        field.textContent = 覆冰;
                        break;
                    case '回路数':
                        field.textContent = 回路数;
                        break;
                    case '导线规格':
                        field.textContent = 导线规格;
                        break;
                    case '组价计算状态':
                        const status = section.组价计算状态 || '未组价';
                        field.textContent = status;
                        
                        // 为状态标签添加样式
                        if (status === '已组价') {
                            field.classList.add('status-success');
                        } else {
                            field.classList.add('status-default');
                        }
                        break;
                    default:
                        field.textContent = section[fieldName] || '-';
                }
            });
            
            // 设置操作按钮的data-section-id属性
            const buttons = clone.querySelectorAll('[data-section-id]');
            buttons.forEach(button => {
                button.setAttribute('data-section-id', section.序号);
                
                // 根据组价计算状态设置按钮显示
                if (button.getAttribute('onclick').includes('selectIndicators')) {
                    if (section.组价计算状态 === '已组价') {
                        button.textContent = '重新组价';
                    } else {
                        button.textContent = '组价计算';
                    }
                }
                
                // 显示/隐藏组价结果按钮
                if (button.getAttribute('onclick').includes('viewPricingResult')) {
                    button.style.display = section.组价计算状态 === '已组价' ? 'inline-block' : 'none';
                }
            });
            
            // 添加到表格
            tbody.appendChild(clone);
        } else {
            // 备选方案：手动创建行
            console.warn('模板元素不存在或不支持content属性，使用备选方案');
            row = document.createElement('tr');
            
            // 创建单元格并填充数据
            row.innerHTML = `
                <td>${section.特征段名称 || '-'}</td>
                <td>${section.线路长度 || '-'}</td>
                <td>${风速}</td>
                <td>${覆冰}</td>
                <td>${回路数}</td>
                <td>${导线规格}</td>
                <td>${section.气象区 || '分'}</td>
                <td><span class="pricing-sm-status-tag ${section.组价状态 === '已组价' ? 'pricing-sm-status-success' : 'pricing-sm-status-default'}">${section.组价状态 || '未组价'}</span></td>
                <td>
                    <button class="pricing-shared-btn ${section.组价状态 === '已组价' ? 'pricing-shared-btn-success pricing-share-result-btn' : 'pricing-shared-btn-primary'}" onclick="selectIndicators(this)" data-section-id="${section.序号}">${section.组价状态 === '已组价' ? '重新组价' : '组价计算'}</button>
                    <button class="pricing-shared-btn pricing-shared-btn-success pricing-share-result-btn" onclick="viewPricingResult(this)" data-section-id="${section.序号}" style="display:${section.组价状态 === '已组价' ? 'inline-block' : 'none'}">组价结果</button>
                    <button class="pricing-shared-btn pricing-shared-btn-delete" onclick="deleteSection(this)" data-section-id="${section.序号}">删除</button>
                </td>
            `;
            
            tbody.appendChild(row);
        }
    });
}

// 显示/隐藏加载状态
function toggleLoading(show) {
    const loadingOverlay = document.createElement('div');
    loadingOverlay.className = 'loading-overlay';
    loadingOverlay.innerHTML = '<div class="loading-spinner"></div>';
    
    if (show) {
        document.body.appendChild(loadingOverlay);
    } else {
        const existingOverlay = document.querySelector('.loading-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }
    }
}

// 获取特定工程信息的辅助函数
async function getProjectById(projectId) {
    console.log('调用getProjectById获取工程信息，工程ID:', projectId);
    
    if (!projectId) {
        console.error('工程ID不能为空');
        return null;
    }
    
    try {
        // 尝试从工程列表API获取所有工程
        const response = await fetch('/api/pricing/projects');
        console.log('工程列表API响应状态:', response.status);
        
        if (!response.ok) {
            throw new Error(`获取工程列表失败: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('工程列表API返回数据:', data);
        
        if (!data || !data.projects || !Array.isArray(data.projects)) {
            throw new Error('工程列表API返回的数据格式不正确');
        }
        
        // 在工程列表中查找指定ID的工程
        const project = data.projects.find(p => p.序号 == projectId);
        
        if (project) {
            console.log('从工程列表中找到匹配的工程:', project);
            return project;
        } else {
            console.error('在工程列表中未找到指定ID的工程:', projectId);
            return null;
        }
    } catch (error) {
        console.error('获取工程信息失败:', error);
        return null;
    }
}

// 导出函数
window.loadSections = loadSections;
window.toggleLoading = toggleLoading;
window.updateProjectInfoPanel = updateProjectInfoPanel; // 导出updateProjectInfoPanel函数
window.getProjectById = getProjectById; // 导出getProjectById函数

// 导出saveNewSection函数到全局，供HTML文件使用
window.saveNewSection = function() {
    console.log('saveNewSection被调用，当前工程ID:', window.projectId);
    
    // 获取表单数据
    const sectionData = {
        特征段名称: document.getElementById('section-name').value,
        线路长度: parseFloat(document.getElementById('section-line-length').value),
        边界条件: {
            风速: parseFloat(document.getElementById('section-wind-speed').value),
            覆冰: parseFloat(document.getElementById('section-ice-thickness').value),
            回路数: document.getElementById('section-circuit-count').value,
            导线规格: document.getElementById('section-wire-spec').value
        },
        气象区: document.getElementById('section-weather-zone').value,
        备注: document.getElementById('section-remark').value
    };

    // 发送POST请求到后端
    fetch('/api/pricing/projects/' + window.projectId + '/feature_sections', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(sectionData)
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(err => Promise.reject(err));
        }
        return response.json();
    })
    .then(data => {
        // 关闭模态框
        document.getElementById('createSectionModal').classList.remove('show');
        // 刷新特征段列表
        loadSections();
        // 显示成功提示
        window.showToast('特征段创建成功', 'success');
    })
    .catch(error => {
        console.error('Error:', error);
        window.showToast(error.error || '特征段创建失败，请检查网络连接', 'error');
    });
};

// 渲染特征段表格
function renderSectionTable(sections) {
    console.log('开始渲染特征段表格，数据:', sections);
    const tbody = document.getElementById('sectionTableBody');
    
    if (!tbody) {
        console.error('找不到sectionTableBody元素');
        return;
    }

    // 清空表格
    tbody.innerHTML = '';
    
    // 检查数据有效性
    if (!Array.isArray(sections) || sections.length === 0) {
        console.log('没有特征段数据或数据不是数组');
        const tr = document.createElement('tr');
        tr.innerHTML = '<td colspan="10" class="text-center">暂无特征段数据</td>';
        tbody.appendChild(tr);
        
        // 更新特征段数量
        const countElement = document.getElementById('sectionCount');
        if (countElement) {
            countElement.textContent = '0';
        }
        return;
    }
    
    console.log(`渲染 ${sections.length} 个特征段`);
    
    // 遍历特征段数据并创建表格行
    sections.forEach(section => {
        console.log('处理特征段:', section);
        const row = document.createElement('tr');
        
        // 安全地获取嵌套属性
        const getNestedProperty = (obj, path, defaultValue = '') => {
            if (!obj) return defaultValue;
            const value = path.split('.').reduce((o, p) => (o && o[p] !== undefined) ? o[p] : undefined, obj);
            return value !== undefined ? value : defaultValue;
        };
        
        // 安全地获取特征段属性
        const 特征段名称 = section.特征段名称 || '';
        const 线路长度 = section.线路长度 || '';

        // 修改获取边界条件的逻辑，确保0值也能正确显示
        const 风速 = getNestedProperty(section, '边界条件.风速') !== '' ? getNestedProperty(section, '边界条件.风速') :
                 (section.风速 !== undefined ? section.风速 : '');

        const 覆冰 = getNestedProperty(section, '边界条件.覆冰') !== '' ? getNestedProperty(section, '边界条件.覆冰') :
                 (section.覆冰 !== undefined ? section.覆冰 : '');

        const 回路数 = getNestedProperty(section, '边界条件.回路数') || section.回路数 || '';
        const 导线规格 = getNestedProperty(section, '边界条件.导线规格') || section.导线规格 || '';
        const 气象区 = section.气象区 || '分';
        const 组价状态 = section.组价状态 || '未组价';
        const 序号 = section.序号 || '';
        
        console.log('特征段属性:', {
            特征段名称,
            线路长度,
            风速,
            覆冰,
            回路数,
            导线规格,
            气象区,
            组价状态,
            序号
        });

        // 创建表格行HTML
        row.innerHTML = `
            <td>${特征段名称}</td>
            <td>${线路长度}</td>
            <td>${风速}</td>
            <td>${覆冰}</td>
            <td>${回路数}</td>
            <td>${导线规格}</td>
            <td>${气象区}</td>
            <td><span class="pricing-sm-status-tag ${组价状态 === '已组价' ? 'pricing-sm-status-success' : 'pricing-sm-status-default'}">${组价状态}</span></td>
            <td>
                <button class="pricing-shared-btn ${组价状态 === '已组价' ? 'pricing-shared-btn-success pricing-share-result-btn' : 'pricing-shared-btn-primary'}" onclick="selectIndicators(this)" data-section-id="${序号}">${组价状态 === '已组价' ? '重新组价' : '组价计算'}</button>
                <button class="pricing-shared-btn pricing-shared-btn-success pricing-share-result-btn" onclick="viewPricingResult(this)" data-section-id="${序号}" style="display:${组价状态 === '已组价' ? 'inline-block' : 'none'}">组价结果</button>
                <button class="pricing-shared-btn pricing-shared-btn-delete" onclick="deleteSection(this)" data-section-id="${序号}">删除</button>
            </td>
        `;
        
        tbody.appendChild(row);
    });
    
    // 更新特征段数量
    const countElement = document.getElementById('sectionCount');
    if (countElement) {
        countElement.textContent = sections.length.toString();
    }
}

// 获取状态样式类
function getStatusClass(status) {
    switch (status) {
        case '已计算':
            return 'success';
        case '选择中':
            return 'warning';
        default:
            return 'default';
    }
}

// 显示创建特征段模态框
function showCreateSectionModal() {
    const modal = document.getElementById('createSectionModal');
    if (modal) {
        modal.classList.add('show');
    }
}

// 关闭创建特征段模态框
function closeCreateSectionModal() {
    const modal = document.getElementById('createSectionModal');
    if (modal) {
        modal.classList.remove('show');
        document.getElementById('createSectionForm').reset();
    }
}

// 显示指标计算模态框
function selectIndicators(button) {
    const sectionId = button.getAttribute('data-section-id');
    if (!sectionId || !window.projectId) {
        window.showToast('缺少必要参数', 'error');
        return;
    }
    
    console.log('打开指标选择模态框，工程ID:', window.projectId, '特征段ID:', sectionId);
    const modal = document.getElementById('indicatorSelectModal');
    const frame = document.getElementById('indicatorSelectFrame');
    
    // 获取当前行的特征段数据
    const row = button.closest('tr');
    
    // 获取单元格内容
    const cells = row.cells;
    const sectionData = {
        工程名称: document.getElementById('projectName').textContent,
        特征段名称: cells[0].textContent,
        线路长度: cells[1].textContent,
        风速: cells[2].textContent,
        覆冰: cells[3].textContent,
        回路数: cells[4].textContent,
        导线规格: cells[5].textContent,
        气象区: cells[6].textContent
    };
    
    console.log('特征段数据:', sectionData);
    
    // 将数据存储到 sessionStorage
    sessionStorage.setItem('selectedSectionData', JSON.stringify(sectionData));
    
    // 设置iframe的src，使用新路由 section_pricingSum_content 替代 indicator_select_content
    frame.src = `/quick_pricing/section_pricingSum_content/${window.projectId}/${sectionId}`;
    
    // 显示模态框
    modal.classList.add('show');
    
    // 调整模态框大小和样式
    modal.style.width = '100%';
    modal.style.height = '100%';
    modal.querySelector('.pricing-shared-modal-content').style.width = '95%';
    modal.querySelector('.pricing-shared-modal-content').style.height = '95%';
    modal.querySelector('.pricing-shared-modal-content').style.maxWidth = '1500px';
    
    // 添加iframe加载事件跟踪
    frame.onload = function() {
        console.log('iframe加载完成');
        if (frame.contentWindow.initIndicatorSelect) {
            console.log('调用iframe内的initIndicatorSelect函数');
            frame.contentWindow.initIndicatorSelect(window.projectId, sectionId);
        }
        
        // 确保iframe内的标题可见
        try {
            const pageTitle = frame.contentDocument.querySelector('.pricing-sp-page-title');
            if (pageTitle) {
                pageTitle.style.display = 'block';
                pageTitle.style.visibility = 'visible';
                pageTitle.style.opacity = '1';
                pageTitle.style.fontWeight = 'bold';
                console.log('已设置iframe内标题样式');
            }
        } catch (e) {
            console.error('设置iframe标题样式失败:', e);
        }
    };
}

// 关闭指标计算模态框
function closeIndicatorSelectModal() {
    const modal = document.getElementById('indicatorSelectModal');
    modal.classList.remove('show');
    // 刷新特征段列表
    loadSections();
}

// 查看组价结果
function viewPricingResult(button) {
    const sectionId = button.getAttribute('data-section-id');
    if (!sectionId || !window.projectId) {
        window.showToast('缺少必要参数', 'error');
        return;
    }

    // 跳转到组价结果页面
    window.location.href = `/quick_pricing/pricing_result/${window.projectId}/${sectionId}`;
}

// 删除特征段
async function deleteSection(button) {
    const sectionId = button.dataset.sectionId;
    if (!confirm('确定要删除该特征段吗？')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/pricing/projects/${window.projectId}/feature_sections/${sectionId}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            window.showToast('特征段删除成功');
            await loadSectionData();
        } else {
            throw new Error('删除特征段失败');
        }
    } catch (error) {
        console.error('删除特征段失败:', error);
        window.showToast('删除特征段失败: ' + error.message, 'error');
    }
}

// 搜索特征段
function searchSections() {
    const lineLength = parseFloat(document.getElementById('lineLength').value);
    const windSpeed = parseFloat(document.getElementById('windSpeed').value);
    const iceThickness = parseFloat(document.getElementById('iceThickness').value);
    const circuitCount = document.getElementById('circuitCountSearch').value;
    const wireSpec = document.getElementById('wireSpecSearch').value.trim().toLowerCase();

    const filteredSections = originalSections.filter(section => {
        const sectionLength = parseFloat(section.线路长度) || 0;
        
        // 修改获取风速和覆冰的逻辑，确保0值也能正确处理
        const sectionWindSpeed = section.边界条件?.风速 !== undefined ? parseFloat(section.边界条件.风速) : 
                              (section.风速 !== undefined ? parseFloat(section.风速) : 0);
        
        const sectionIceThickness = section.边界条件?.覆冰 !== undefined ? parseFloat(section.边界条件.覆冰) : 
                                 (section.覆冰 !== undefined ? parseFloat(section.覆冰) : 0);
        
        const sectionCircuitCount = section.边界条件?.回路数 || section.回路数 || '';
        const sectionWireSpec = (section.边界条件?.导线规格 || section.导线规格 || '').toLowerCase();

        return (
            (!lineLength || Math.abs(sectionLength - lineLength) < 0.01) &&
            (!windSpeed && windSpeed !== 0 || Math.abs(sectionWindSpeed - windSpeed) < 0.01) &&
            (!iceThickness && iceThickness !== 0 || Math.abs(sectionIceThickness - iceThickness) < 0.01) &&
            (circuitCount === '' || sectionCircuitCount === circuitCount) &&
            (wireSpec === '' || sectionWireSpec.includes(wireSpec))
        );
    });

    renderSectionTable(filteredSections);
}

// 重置搜索条件
function resetSearch() {
    // 重置所有搜索输入框
    document.getElementById('lineLength').value = '';
    document.getElementById('windSpeed').value = '';
    document.getElementById('iceThickness').value = '';
    document.getElementById('circuitCountSearch').value = '';
    document.getElementById('wireSpecSearch').value = '';

    // 显示原始数据
    renderSectionTable(originalSections);
}

// 返回工程列表
function backToProjects() {
    // 调用 project_management.js 中的重置面板状态函数
    if (typeof resetPanelState === 'function') {
        resetPanelState();
    } else {
        // 如果函数不可用，直接操作 DOM
        const workspaceContent = document.querySelector('.workspace-content');
        if (workspaceContent) {
            workspaceContent.classList.add('panels-collapsed');
            workspaceContent.classList.remove('panels-expanded');
        }
    }
} 

// 更新特征段状态
function updateSectionStatus(sectionData) {
    console.log('updateSectionStatus被调用，当前projectId:', window.projectId);   
    
    try {
        // 查找对应的特征段行
        const tbody = document.getElementById('sectionTableBody');
        const rows = tbody.getElementsByTagName('tr');
        
        for (let row of rows) {
            const nameCell = row.cells[0];
            if (nameCell.textContent.trim() === (sectionData.特征段名称 || '').trim()) {
                // 获取特征段ID
                const buttons = row.querySelectorAll('[data-section-id]');
                const sectionId = buttons.length > 0 ? buttons[0].getAttribute('data-section-id') : null;
                console.log('要更新的特征段ID:', sectionId);
                
                // 更新状态标签
                const statusCell = row.cells[6];
                const statusTag = statusCell.querySelector('.pricing-sm-status-tag');
                statusTag.textContent = sectionData.组价计算状态 || '已组价';
                statusTag.className = 'pricing-sm-status-tag pricing-sm-status-success';
                
                // 更新按钮状态
                const pricingBtn = row.querySelector('[onclick*="selectIndicators"]');
                if (pricingBtn) pricingBtn.textContent = '重新组价';
                
                const resultBtn = row.querySelector('[onclick*="viewPricingResult"]');
                if (resultBtn) resultBtn.style.display = 'inline-block';
                
                // 演示模式：前端页面直接更新，不调用后台接口
                window.showToast('特征段状态更新成功', 'success');
                
                // 如果不是演示模式并且有特征段ID，则调用API更新状态
                if (!window.demoMode && sectionId) {
                    fetch(`/api/pricing/projects/${window.projectId}/feature_sections/${sectionId}`, {
                        method: 'PATCH',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            ...sectionData,
                            序号: parseInt(sectionId),
                            工程序号: parseInt(window.projectId),
                            组价计算状态: sectionData.组价计算状态 || '已组价'
                        })
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('更新特征段状态失败');
                        }
                        window.showToast('特征段状态已更新', 'success');
                    })
                    .catch(error => {
                        console.error('更新特征段状态失败:', error);
                        window.showToast('更新特征段状态失败: ' + error.message, 'error');
                    });
                }
                
                break;
            }
        }
    } catch (error) {
        console.error('更新特征段状态失败:', error);
        window.showToast('更新特征段状态失败', 'error');
    }
}

// 导出函数到全局
window.updateSectionStatus = updateSectionStatus; 