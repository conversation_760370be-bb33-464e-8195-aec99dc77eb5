// 以下内容来自hisIndQuery.html <script>标签，原样抽取
// 从页面中的隐藏元素获取数据
const bentiData = JSON.parse(document.getElementById('benti-data').textContent);
const qitaData = JSON.parse(document.getElementById('qita-data').textContent);

function renderTable(data, tableId, startIndex = 0, pageSize = 10) {
    const tbody = document.querySelector(`#${tableId} tbody`);
    tbody.innerHTML = '';
    
    const endIndex = Math.min(startIndex + pageSize, data.length);
    const pageData = data.slice(startIndex, endIndex);
    
    pageData.forEach(item => {
        const tr = document.createElement('tr');
        
        // 根据表格类型选择对应的列
        const columns = tableId === 'bentiTable' ? [
            "序号", "工程名称", "线路工程", "线路总长度(km)", "回路数", "风速(m/s)", "覆冰(mm)",
            "导线规格", "平地(%)", "丘陵(%)", "山地(%)", "高山(%)", "峻岭(%)", "泥沼(%)", 
            "河网(%)", "人力运距(km)", "汽车运距（含拖拉机）(km)", "铁塔基数(基)", "直线塔(基)",
            "耐张塔(基)", "耐张比例(%)", "导线(t)", "塔材(t)", "基础钢材(t)", 
            "地脚螺栓和插入式角钢(t)", "挂线金具(t)", "导线间隔棒(套)", "防振锤(个)", 
            "导线防振锤(个)", "地线防振锤(个)", "合成/复合绝缘子(支)", 
            "玻璃绝缘子/盘式绝缘子(支)", "硬跳(套)", "现浇混凝土(m³)", "灌柱桩基础混凝土(m³)",
            "基础护壁(m³)", "基础垫层(m³)", "钻孔灌注桩深度(m)", "护坡、挡土墙(m³)", 
            "土方量(m³)", "基坑土方（非机械）(m³)", "基坑土方（机械）(m³)", "接地槽(m³)",
            "排水沟(m³)", "尖峰、基面(m³)", "本体工程(万元)", "基础工程(万元)", 
            "杆塔工程(万元)", "接地工程(万元)", "架线工程(万元)", "附件工程(万元)", 
            "辅助工程(万元)"
        ] : [
            "序号", "工程名称", "线路工程", "合并气象区总长度", "项目建设管理费（万元）",
            "项目法人管理费（万元）", "招标费（万元）", "工程监理费（万元）",
            "施工过程造价咨询及竣工结算审核费（万元）", "工程保险费（万元）",
            "项目建设技术服务费（万元）", "项目前期工作费（万元）", "勘察设计费（万元）",
            "勘察费（万元）", "设计费（万元）", "基本设计费（万元）", "其他设计费（万元）",
            "设计文件评审费（万元）", "可行性研究文件评审费（万元）", "初步设计文件评审费（万元）",
            "施工图文件评审费（万元）", "工程建设检测费（万元）", "电力工程质量检测费（万元）",
            "桩基检测费（万元）", "电力工程技术经济标准编制费（万元）", "生产准备费（万元）",
            "管理车辆购置费（万元）", "工器具及办公家具购置费（万元）",
            "生产职工培训及提前进场费（万元）", "专业爆破服务费（万元）"
        ];
        
        // 按照定义的列顺序添加数据
        columns.forEach(column => {
            const td = document.createElement('td');
            const value = item[column];
            td.textContent = (value === 0 || value) ? value : '';
            tr.appendChild(td);
        });
        
        tbody.appendChild(tr);
    });
}

document.addEventListener('DOMContentLoaded', function() {
    // 初始加载数据
    renderTable(bentiData, 'bentiTable');
    renderTable(qitaData, 'qitaTable');
    
    // 分页按钮点击事件
    document.querySelectorAll('.pagination').forEach(pagination => {
        pagination.addEventListener('click', function(e) {
            if (e.target.tagName === 'BUTTON') {
                const pageNum = parseInt(e.target.textContent) - 1;
                const tableId = this.closest('.tab-content').querySelector('.data-table').id;
                const data = tableId === 'bentiTable' ? bentiData : qitaData;
                renderTable(data, tableId, pageNum * 10);
                
                // 更新活动页码样式
                this.querySelectorAll('button').forEach(btn => btn.classList.remove('active'));
                e.target.classList.add('active');
            }
        });
    });
});

function switchTab(tabId, event) {
    document.querySelectorAll('.tab-content').forEach(tab => tab.classList.remove('active'));
    document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
    document.getElementById(tabId).classList.add('active');
    if(event) event.target.classList.add('active');
}

function switchModalTab(tabId, event) {
    document.querySelectorAll('.modal-tab-content').forEach(tab => tab.classList.remove('active'));
    document.querySelectorAll('.modal-tab-btn').forEach(btn => btn.classList.remove('active'));
    document.getElementById(tabId).classList.add('active');
    if(event) event.target.classList.add('active');
}

function showProjectModal(project) {
    // 并行异步读取三个json
    Promise.all([
        fetch('/data/Ind_info.json').then(res => res.json()),
        fetch('/data/Ind_benti.json').then(res => res.json()),
        fetch('/data/Ind_qita.json').then(res => res.json())
    ]).then(function([indInfo, bentiArr, qitaArr]) {
        const bentiRow = bentiArr && bentiArr.length ? bentiArr[0] : {};
        const qitaRow = qitaArr && qitaArr.length ? qitaArr[0] : {};
        // 本体费用tab
        const modalBentiTable = document.getElementById('modalBentiTable');
        modalBentiTable.innerHTML = '';
        if (bentiRow && indInfo) {
            const thead = document.createElement('thead');
            const tr = document.createElement('tr');
            ['指标名称', '单位', '指标值'].forEach(t => {
                const th = document.createElement('th');
                th.textContent = t;
                tr.appendChild(th);
            });
            thead.appendChild(tr);
            modalBentiTable.appendChild(thead);
            const tbody = document.createElement('tbody');
            Object.keys(indInfo['本体费用指标']).sort((a,b)=>Number(a)-Number(b)).forEach(idx => {
                const info = indInfo['本体费用指标'][idx];
                const tr = document.createElement('tr');
                const td1 = document.createElement('td');
                td1.textContent = info['指标名称'];
                tr.appendChild(td1);
                const td2 = document.createElement('td');
                td2.textContent = info['指标单位'];
                tr.appendChild(td2);
                const td3 = document.createElement('td');
                const keyEn = info['指标名称'] + '(' + info['指标单位'] + ')';
                const keyCn = info['指标名称'] + '（' + info['指标单位'] + '）';
                td3.textContent = bentiRow[keyEn] !== undefined ? bentiRow[keyEn] : (bentiRow[keyCn] !== undefined ? bentiRow[keyCn] : '');
                tr.appendChild(td3);
                tbody.appendChild(tr);
            });
            modalBentiTable.appendChild(tbody);
        }
        // 其他费用tab
        const modalQitaTable = document.getElementById('modalQitaTable');
        modalQitaTable.innerHTML = '';
        if (qitaRow && indInfo) {
            const thead = document.createElement('thead');
            const tr = document.createElement('tr');
            ['指标名称', '单位', '指标值'].forEach(t => {
                const th = document.createElement('th');
                th.textContent = t;
                tr.appendChild(th);
            });
            thead.appendChild(tr);
            modalQitaTable.appendChild(thead);
            const tbody = document.createElement('tbody');
            Object.keys(indInfo['其他费用指标']).sort((a,b)=>Number(a)-Number(b)).forEach(idx => {
                const info = indInfo['其他费用指标'][idx];
                const tr = document.createElement('tr');
                const td1 = document.createElement('td');
                td1.textContent = info['指标名称'];
                tr.appendChild(td1);
                const td2 = document.createElement('td');
                td2.textContent = info['指标单位'];
                tr.appendChild(td2);
                const td3 = document.createElement('td');
                const keyEn = info['指标名称'] + '(' + info['指标单位'] + ')';
                const keyCn = info['指标名称'] + '（' + info['指标单位'] + '）';
                td3.textContent = qitaRow[keyEn] !== undefined ? qitaRow[keyEn] : (qitaRow[keyCn] !== undefined ? qitaRow[keyCn] : '');
                tr.appendChild(td3);
                tbody.appendChild(tr);
            });
            modalQitaTable.appendChild(tbody);
        }
        document.getElementById('modalTitle').textContent = project['工程名称'] + '-' + project['线路段名称'];
        document.getElementById('customModal').style.display = 'flex';
    });
}
document.getElementById('modalCancelBtn').onclick = function() {
    document.getElementById('customModal').style.display = 'none';
};

// 读取项目数据
let projectData = [];
fetch('/data/ind_projects.json')
    .then(res => res.json())
    .then(data => {
        projectData = data;
        renderProjectTable(projectData);
    });

function renderProjectTable(data) {
    const tbody = document.querySelector('#projectTable tbody');
    tbody.innerHTML = '';
    data.forEach(item => {
        const tr = document.createElement('tr');
        // 工程名称
        const nameTd = document.createElement('td');
        nameTd.textContent = item['工程名称'];
        nameTd.className = '';
        tr.appendChild(nameTd);
        // 线路段名称加可点击弹窗
        const sectionTd = document.createElement('td');
        sectionTd.textContent = item['线路段名称'];
        sectionTd.className = 'project-link';
        sectionTd.onclick = () => showProjectModal(item);
        tr.appendChild(sectionTd);
        [
            '工程编号','时间段','所属省份','跨越城市','电压等级','线路总长度','回路数','导线规格','风速','覆冰',
            '平地%','丘陵%','山地%','高山%','峻岭%','泥沼%','河网%'
        ].forEach(key => {
            const td = document.createElement('td');
            td.textContent = item[key] !== undefined ? item[key] : '';
            tr.appendChild(td);
        });
        tbody.appendChild(tr);
    });
}

// 查询按钮逻辑
const pjQueryBtn = document.getElementById('pjQueryBtn');
const pjResetBtn = document.getElementById('pjResetBtn');
pjQueryBtn && pjQueryBtn.addEventListener('click', function () {
    let filtered = projectData.filter(item => {
        if (document.getElementById('pjNameInput').value && !item['工程名称'].includes(document.getElementById('pjNameInput').value)) return false;
        if (document.getElementById('pjSectionInput').value && !item['线路段名称'].includes(document.getElementById('pjSectionInput').value)) return false;
        if (document.getElementById('pjCodeInput').value && !item['工程编号'].includes(document.getElementById('pjCodeInput').value)) return false;
        if (document.getElementById('pjPeriodInput').value && !item['时间段'].includes(document.getElementById('pjPeriodInput').value)) return false;
        if (document.getElementById('pjProvinceInput').value && !item['所属省份'].includes(document.getElementById('pjProvinceInput').value)) return false;
        if (document.getElementById('pjCityInput').value && !item['跨越城市'].includes(document.getElementById('pjCityInput').value)) return false;
        if (document.getElementById('pjVoltageInput').value && !item['电压等级'].includes(document.getElementById('pjVoltageInput').value)) return false;
        if (document.getElementById('pjLengthInput').value && Number(item['线路总长度']) !== Number(document.getElementById('pjLengthInput').value)) return false;
        if (document.getElementById('pjLoopInput').value && !item['回路数'].includes(document.getElementById('pjLoopInput').value)) return false;
        if (document.getElementById('pjSpecInput').value && !item['导线规格'].includes(document.getElementById('pjSpecInput').value)) return false;
        if (document.getElementById('pjWindInput').value && Number(item['风速']) !== Number(document.getElementById('pjWindInput').value)) return false;
        if (document.getElementById('pjIceInput').value && Number(item['覆冰']) !== Number(document.getElementById('pjIceInput').value)) return false;
        if (document.getElementById('pjPlainInput').value && Number(item['平地%']) !== Number(document.getElementById('pjPlainInput').value)) return false;
        if (document.getElementById('pjHillInput').value && Number(item['丘陵%']) !== Number(document.getElementById('pjHillInput').value)) return false;
        if (document.getElementById('pjMountainInput').value && Number(item['山地%']) !== Number(document.getElementById('pjMountainInput').value)) return false;
        if (document.getElementById('pjHighMountainInput').value && Number(item['高山%']) !== Number(document.getElementById('pjHighMountainInput').value)) return false;
        if (document.getElementById('pjRidgeInput').value && Number(item['峻岭%']) !== Number(document.getElementById('pjRidgeInput').value)) return false;
        if (document.getElementById('pjSwampInput').value && Number(item['泥沼%']) !== Number(document.getElementById('pjSwampInput').value)) return false;
        if (document.getElementById('pjRiverInput').value && Number(item['河网%']) !== Number(document.getElementById('pjRiverInput').value)) return false;
        return true;
    });
    renderProjectTable(filtered);
});
pjResetBtn && pjResetBtn.addEventListener('click', function () {
    document.getElementById('projectQueryForm').reset();
    renderProjectTable(projectData);
}); 

// ========== 本体费用指标数据查询tab 按钮演示逻辑 ===========

document.addEventListener('DOMContentLoaded', function () {
    // 1. 补充弹窗内容区域（如无则插入）
    const modal = document.querySelector('#customModal .modal');
    if (modal && !document.getElementById('modalMessage')) {
        const msg = document.createElement('p');
        msg.id = 'modalMessage';
        msg.style.margin = '0.5rem 0 1rem';
        modal.insertBefore(msg, modal.querySelector('.modal-tabs'));
    }
    if (modal && !document.getElementById('modalFileInput')) {
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.id = 'modalFileInput';
        fileInput.style.display = 'none';
        fileInput.style.marginBottom = '1rem';
        modal.insertBefore(fileInput, modal.querySelector('.modal-tabs'));
    }
    if (modal && !document.getElementById('modalConfirmBtn')) {
        const btn = document.createElement('button');
        btn.type = 'button';
        btn.className = 'btn btn-primary';
        btn.id = 'modalConfirmBtn';
        btn.textContent = '确认';
        btn.style.marginRight = '0.5rem';
        // 插入到关闭按钮前
        const actions = modal.querySelector('.modal-actions');
        actions.insertBefore(btn, actions.firstChild);
    }
    // 获取弹窗相关元素
    const modalOverlay = document.getElementById('customModal');
    const modalTitle = document.getElementById('modalTitle');
    const modalMessage = document.getElementById('modalMessage');
    const modalFileInput = document.getElementById('modalFileInput');
    const modalCancelBtn = document.getElementById('modalCancelBtn');
    const modalConfirmBtn = document.getElementById('modalConfirmBtn');

    // 2. showModal/hideModal
    function showModal({ title, text, showFile = false, confirmText = '确认', cancelText = '关闭', onConfirm = null, hideTabs = false }) {
        modalTitle.textContent = title;
        modalMessage.textContent = text || '';
        modalFileInput.style.display = showFile ? 'block' : 'none';
        if (showFile) modalFileInput.value = '';
        modalConfirmBtn.style.display = 'inline-block';
        modalConfirmBtn.textContent = confirmText;
        modalCancelBtn.textContent = cancelText || '关闭';
        modalOverlay.style.display = 'flex';
        // 隐藏/显示tab
        const modalTabs = modal.querySelector('.modal-tabs');
        const modalTabContents = modal.querySelectorAll('.modal-tab-content');
        if (modalTabs) modalTabs.style.display = hideTabs ? 'none' : '';
        modalTabContents.forEach(tab => tab.style.display = hideTabs ? 'none' : '');
        // 解绑旧事件
        modalConfirmBtn.onclick = null;
        modalCancelBtn.onclick = null;
        // 绑定新事件
        modalConfirmBtn.onclick = function () {
            if (onConfirm) onConfirm();
        };
        modalCancelBtn.onclick = hideModal;
    }
    function hideModal() {
        modalOverlay.style.display = 'none';
        // 恢复tab显示，避免后续详情弹窗异常
        const modalTabs = modal.querySelector('.modal-tabs');
        const modalTabContents = modal.querySelectorAll('.modal-tab-content');
        if (modalTabs) modalTabs.style.display = '';
        modalTabContents.forEach(tab => tab.style.display = '');
    }

    // 3. 按钮事件绑定
    // 历史线路项目数据查询tab
    const pjQueryBtn = document.getElementById('pjQueryBtn');
    const pjResetBtn = document.getElementById('pjResetBtn');
    const pjExportBtn = document.getElementById('pjExportBtn');
    const pjImportBtn = document.getElementById('pjImportBtn');

    // 本体费用指标数据查询tab
    const bentiQueryBtn = document.getElementById('bentiQueryBtn');
    const bentiExportBtn = document.getElementById('bentiExportBtn');
    const bentiImportBtn = document.getElementById('bentiImportBtn');

    // 其他费用指标数据查询tab
    const qitaQueryBtn = document.getElementById('qitaQueryBtn');
    const qitaExportBtn = document.getElementById('qitaExportBtn');
    const qitaImportBtn = document.getElementById('qitaImportBtn');

    // ========== 历史线路项目数据查询tab 按钮演示逻辑 ==========
    pjQueryBtn && pjQueryBtn.addEventListener('click', function () {
        let filtered = projectData.filter(item => {
            if (document.getElementById('pjNameInput').value && !item['工程名称'].includes(document.getElementById('pjNameInput').value)) return false;
            if (document.getElementById('pjSectionInput').value && !item['线路段名称'].includes(document.getElementById('pjSectionInput').value)) return false;
            if (document.getElementById('pjCodeInput').value && !item['工程编号'].includes(document.getElementById('pjCodeInput').value)) return false;
            if (document.getElementById('pjPeriodInput').value && !item['时间段'].includes(document.getElementById('pjPeriodInput').value)) return false;
            if (document.getElementById('pjProvinceInput').value && !item['所属省份'].includes(document.getElementById('pjProvinceInput').value)) return false;
            if (document.getElementById('pjCityInput').value && !item['跨越城市'].includes(document.getElementById('pjCityInput').value)) return false;
            if (document.getElementById('pjVoltageInput').value && !item['电压等级'].includes(document.getElementById('pjVoltageInput').value)) return false;
            if (document.getElementById('pjLengthInput').value && Number(item['线路总长度']) !== Number(document.getElementById('pjLengthInput').value)) return false;
            if (document.getElementById('pjLoopInput').value && !item['回路数'].includes(document.getElementById('pjLoopInput').value)) return false;
            if (document.getElementById('pjSpecInput').value && !item['导线规格'].includes(document.getElementById('pjSpecInput').value)) return false;
            if (document.getElementById('pjWindInput').value && Number(item['风速']) !== Number(document.getElementById('pjWindInput').value)) return false;
            if (document.getElementById('pjIceInput').value && Number(item['覆冰']) !== Number(document.getElementById('pjIceInput').value)) return false;
            if (document.getElementById('pjPlainInput').value && Number(item['平地%']) !== Number(document.getElementById('pjPlainInput').value)) return false;
            if (document.getElementById('pjHillInput').value && Number(item['丘陵%']) !== Number(document.getElementById('pjHillInput').value)) return false;
            if (document.getElementById('pjMountainInput').value && Number(item['山地%']) !== Number(document.getElementById('pjMountainInput').value)) return false;
            if (document.getElementById('pjHighMountainInput').value && Number(item['高山%']) !== Number(document.getElementById('pjHighMountainInput').value)) return false;
            if (document.getElementById('pjRidgeInput').value && Number(item['峻岭%']) !== Number(document.getElementById('pjRidgeInput').value)) return false;
            if (document.getElementById('pjSwampInput').value && Number(item['泥沼%']) !== Number(document.getElementById('pjSwampInput').value)) return false;
            if (document.getElementById('pjRiverInput').value && Number(item['河网%']) !== Number(document.getElementById('pjRiverInput').value)) return false;
            return true;
        });
        renderProjectTable(filtered);
    });
    pjResetBtn && pjResetBtn.addEventListener('click', function () {
        document.getElementById('projectQueryForm').reset();
        renderProjectTable(projectData);
    });
    pjExportBtn && pjExportBtn.addEventListener('click', function () {
        showModal({
            title: '导出确认',
            text: '将所选择项目导出为excel文件，请确认是否导出。',
            confirmText: '导出',
            hideTabs: true,
            onConfirm: function () {
                showModal({
                    title: '提示',
                    text: '已导出成功！',
                    confirmText: '确定',
                    cancelText: null,
                    hideTabs: true,
                    onConfirm: hideModal
                });
            }
        });
    });
    pjImportBtn && pjImportBtn.addEventListener('click', function () {
        showModal({
            title: '线路项目文件上传',
            text: '上传已确认的历史线路项目文件，导入将发布到历史指标库作为工程参考依据。',
            showFile: true,
            confirmText: '导入',
            hideTabs: true,
            onConfirm: function () {
                if (!modalFileInput.files.length) {
                    alert('请先选择要上传的文件！');
                    return;
                }
                showModal({
                    title: '确认导入',
                    text: '上传文件项目数据将作为发布历史指标库作为工程参考依据，请确认。',
                    confirmText: '确认',
                    hideTabs: true,
                    onConfirm: function () {
                        projectData.push({
                            "工程名称": "演示线路工程",
                            "线路段名称": "演示段",
                            "工程编号": "DEMO001",
                            "时间段": "2023-2024",
                            "所属省份": "演示省",
                            "跨越城市": "演示市",
                            "电压等级": "500kV",
                            "线路总长度": 10,
                            "回路数": "单回路",
                            "导线规格": "DEMO-720/50",
                            "风速": 25,
                            "覆冰": 3,
                            "平地%": 50,
                            "丘陵%": 20,
                            "山地%": 10,
                            "高山%": 5,
                            "峻岭%": 5,
                            "泥沼%": 5,
                            "河网%": 5
                        });
                        renderProjectTable(projectData);
                        showModal({
                            title: '提示',
                            text: '导入成功！',
                            confirmText: '确定',
                            cancelText: null,
                            hideTabs: true,
                            onConfirm: hideModal
                        });
                    }
                });
            }
        });
    });

    // ========== 本体费用指标数据查询tab 按钮演示逻辑 ==========
    bentiQueryBtn && bentiQueryBtn.addEventListener('click', function () {
        const name = document.getElementById('projectNameInput').value.trim();
        const loopNum = document.getElementById('loopSelect').value;
        const wind = document.getElementById('windInput').value.trim();
        const ice = document.getElementById('iceInput').value.trim();
        const spec = document.getElementById('specInput').value.trim();
        const filtered = bentiData.filter(item => {
            if (name && (!item['工程名称'] || !item['工程名称'].includes(name))) return false;
            if (loopNum && loopNum !== '全部' && item['回路数'] !== loopNum) return false;
            if (wind && Number(item['风速(m/s)']) !== Number(wind)) return false;
            if (ice && Number(item['覆冰(mm)']) !== Number(ice)) return false;
            if (spec && (!item['导线规格'] || !item['导线规格'].includes(spec))) return false;
            return true;
        });
        renderTable(filtered, 'bentiTable');
    });
    bentiExportBtn && bentiExportBtn.addEventListener('click', function () {
        showModal({
            title: '导出确认',
            text: '将所选择项目导出为excel文件，请确认是否导出。',
            confirmText: '导出',
            hideTabs: true,
            onConfirm: function () {
                showModal({
                    title: '提示',
                    text: '已导出成功！',
                    confirmText: '确定',
                    cancelText: null,
                    hideTabs: true,
                    onConfirm: hideModal
                });
            }
        });
    });
    bentiImportBtn && bentiImportBtn.addEventListener('click', function () {
        showModal({
            title: '线路指标文件上传',
            text: '上传已确认的历史线路工程指标文件，导入将发布到历史指标库作为工程参考依据。',
            showFile: true,
            confirmText: '导入',
            hideTabs: true,
            onConfirm: function () {
                if (!modalFileInput.files.length) {
                    alert('请先选择要上传的文件！');
                    return;
                }
                showModal({
                    title: '确认导入',
                    text: '上传文件指标数据将作为发布历史指标库作为工程参考依据，请确认。',
                    confirmText: '确认',
                    hideTabs: true,
                    onConfirm: function () {
                        bentiData.push({
                            "序号": bentiData.length + 1,
                            "工程名称": "500kV楚庭第二通道线路工程",
                            "线路工程": "500kV凤城至楚庭线路工程线路部分-新增",
                            "线路总长度(km)": 20,
                            "回路数": "双回路",
                            "风速(m/s)": 27,
                            "覆冰(mm)": 5,
                            "导线规格": "JL/LB20A 720/50"
                        });
                        renderTable(bentiData, 'bentiTable');
                        showModal({
                            title: '提示',
                            text: '导入成功！',
                            confirmText: '确定',
                            cancelText: null,
                            hideTabs: true,
                            onConfirm: hideModal
                        });
                    }
                });
            }
        });
    });

    // ========== 其他费用指标数据查询tab 按钮演示逻辑 ==========
    qitaQueryBtn && qitaQueryBtn.addEventListener('click', function () {
        const name = document.querySelector('#tab3 input[placeholder="请输入工程名称"]').value.trim();
        const section = document.querySelector('#tab3 input[placeholder="请输入线路工程"]').value.trim();
        const filtered = qitaData.filter(item => {
            if (name && (!item['工程名称'] || !item['工程名称'].includes(name))) return false;
            if (section && (!item['线路工程'] || !item['线路工程'].includes(section))) return false;
            return true;
        });
        renderTable(filtered, 'qitaTable');
    });
    qitaExportBtn && qitaExportBtn.addEventListener('click', function () {
        showModal({
            title: '导出确认',
            text: '将所选择项目导出为excel文件，请确认是否导出。',
            confirmText: '导出',
            hideTabs: true,
            onConfirm: function () {
                showModal({
                    title: '提示',
                    text: '已导出成功！',
                    confirmText: '确定',
                    cancelText: null,
                    hideTabs: true,
                    onConfirm: hideModal
                });
            }
        });
    });
    qitaImportBtn && qitaImportBtn.addEventListener('click', function () {
        showModal({
            title: '其他费用指标文件上传',
            text: '上传已确认的其他费用指标文件，导入将发布到历史指标库作为工程参考依据。',
            showFile: true,
            confirmText: '导入',
            hideTabs: true,
            onConfirm: function () {
                if (!modalFileInput.files.length) {
                    alert('请先选择要上传的文件！');
                    return;
                }
                showModal({
                    title: '确认导入',
                    text: '上传文件指标数据将作为发布历史指标库作为工程参考依据，请确认。',
                    confirmText: '确认',
                    hideTabs: true,
                    onConfirm: function () {
                        qitaData.push({
                            "序号": qitaData.length + 1,
                            "工程名称": "演示工程",
                            "线路工程": "演示线路",
                            "合并气象区总长度": 10,
                            "项目建设管理费（万元）": 1,
                            "项目法人管理费（万元）": 2,
                            "招标费（万元）": 3
                        });
                        renderTable(qitaData, 'qitaTable');
                        showModal({
                            title: '提示',
                            text: '导入成功！',
                            confirmText: '确定',
                            cancelText: null,
                            hideTabs: true,
                            onConfirm: hideModal
                        });
                    }
                });
            }
        });
    });
}); 