from flask import Blueprint, render_template, jsonify, request, send_file
from datetime import datetime
from io import BytesIO
import os
import json
import xlsxwriter
import io
from docx import Document
from docx.shared import Pt, RGBColor
from docx.oxml.ns import qn
from docxtpl import DocxTemplate
from jinja2 import Environment, BaseLoader, Undefined
from openpyxl import load_workbook  # 若后续需要动态写入，可使用

# 导入快速组价模块的业务服务
from .models import project_service, feature_service, pricing_result_service

bp = Blueprint('quick_pricing', __name__)

# --- 常量与工具函数（与 app.py 保持一致） ---
DATA_DIR = 'data'
PRICING_PROJECTS_FILE = os.path.join(DATA_DIR, 'pricing_projects.json')
FEATURE_SECTIONS_FILE = os.path.join(DATA_DIR, 'feature_sections.json')
PRICING_RESULTS_FILE = os.path.join(DATA_DIR, 'pricing_results.json')
BENTI_DATA_FILE = os.path.join(DATA_DIR, 'Ind_benti.json')
QITA_DATA_FILE = os.path.join(DATA_DIR, 'Ind_qita.json')
PROJECT_DATA_FILE = os.path.join(DATA_DIR, 'project_data.json')


def load_json_data(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        if file_path in (PRICING_PROJECTS_FILE, FEATURE_SECTIONS_FILE, PRICING_RESULTS_FILE):
            return {}
        return []
    except Exception as e:
        print(f"加载数据失败 {file_path}: {e}")
        return []


def save_json_data(file_path, data):
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        print(f"保存数据失败 {file_path}: {e}")
        return False

# --- 页面路由 ---

@bp.route('/quick_pricing/workspace')
def quick_pricing_workspace():
    return render_template('quick_pricing/quickPricingWorkspace.html')


@bp.route('/quick_pricing/feature_sections/<int:project_id>')
def feature_sections(project_id):
    return render_template('quick_pricing/old/featureSection.html', project_id=project_id)


@bp.route('/quick_pricing/indicator_select/<int:project_id>/<int:section_id>')
def indicator_select(project_id, section_id):
    return render_template('quick_pricing/section_pricingSum.html', project_id=project_id, section_id=section_id)


@bp.route('/quick_pricing/section_management/<int:project_id>')
def section_management(project_id):
    return render_template('quick_pricing/section_management.html', project_id=project_id)


@bp.route('/quick_pricing/indicator_select_content/<int:project_id>/<int:section_id>')
def indicator_select_content(project_id, section_id):
    return render_template('quick_pricing/section_pricingSum.html')


@bp.route('/quick_pricing/section_pricingSum_content/<int:project_id>/<int:section_id>')
def section_pricingSum_content(project_id, section_id):
    return render_template('quick_pricing/section_pricingSum.html')

# --- API: 组价工程 CRUD ---

@bp.route('/api/pricing/projects', methods=['GET'])
def get_pricing_projects():
    try:
        projects = project_service.get_all_projects()
        # 按创建时间排序
        projects.sort(key=lambda x: x.get('创建时间', ''), reverse=True)
        return jsonify({"projects": projects})
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@bp.route('/api/pricing/projects/old', methods=['GET'])
def get_pricing_projects_old():
    try:
        data = load_json_data(PRICING_PROJECTS_FILE)
        projects = data.get('projects', [])
        projects.sort(key=lambda x: x.get('创建时间', ''), reverse=True)
        return jsonify(projects)
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@bp.route('/api/pricing/projects/search', methods=['GET'])
def search_pricing_projects():
    try:
        data = load_json_data(PRICING_PROJECTS_FILE)
        projects = data.get('projects', [])
        project_name = request.args.get('工程名称', '')
        voltage_level = request.args.get('电压等级', '')
        line_length = request.args.get('线路总长度', '')
        filtered = []
        for proj in projects:
            if project_name and project_name not in proj.get('工程名称', ''):
                continue
            if voltage_level and voltage_level != proj.get('电压等级', ''):
                continue
            if line_length and float(line_length) != float(proj.get('线路总长度', 0)):
                continue
            filtered.append(proj)
        filtered.sort(key=lambda x: x.get('创建时间', ''), reverse=True)
        return jsonify(filtered)
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@bp.route('/api/pricing/projects', methods=['POST'])
def create_pricing_project():
    try:
        data = load_json_data(PRICING_PROJECTS_FILE)
        projects = data.get('projects', [])
        new_project = request.json
        max_seq = max((p.get('序号', 0) for p in projects), default=0)
        new_project['序号'] = max_seq + 1
        new_project.update({
            '特征段数量': 0,
            '组价状态': '未组价',
            '创建时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            '组价时间': ''
        })
        projects.append(new_project)
        data['projects'] = projects
        if save_json_data(PRICING_PROJECTS_FILE, data):
            return jsonify({"message": "工程创建成功", "project": new_project}), 201
        return jsonify({"error": "保存数据失败"}), 500
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@bp.route('/api/pricing/projects/<int:project_id>', methods=['PATCH'])
def update_pricing_project(project_id):
    try:
        data = load_json_data(PRICING_PROJECTS_FILE)
        projects = data.get('projects', [])
        updates = request.json
        for proj in projects:
            if proj['序号'] == project_id:
                allowed = ['组价状态', '特征段数量', '组价时间']
                for field in allowed:
                    if field in updates:
                        proj[field] = updates[field]
                if updates.get('组价状态') == '已组价':
                    proj['组价时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                data['projects'] = projects
                if save_json_data(PRICING_PROJECTS_FILE, data):
                    return jsonify({"message": "工程更新成功", "project": proj})
                return jsonify({"error": "保存数据失败"}), 500
        return jsonify({"error": "工程不存在"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@bp.route('/api/pricing/projects/<int:project_id>', methods=['DELETE'])
def delete_pricing_project(project_id):
    try:
        data = load_json_data(PRICING_PROJECTS_FILE)
        projects = data.get('projects', [])
        index = next((i for i, p in enumerate(projects) if p.get('序号') == project_id), None)
        if index is None:
            return jsonify({'message': '工程不存在'}), 404
        deleted = projects.pop(index)
        if save_json_data(PRICING_PROJECTS_FILE, {'projects': projects}):
            # 同时删除特征段
            sections_data = load_json_data(FEATURE_SECTIONS_FILE)
            sections_data['feature_sections'] = [
                s for s in sections_data.get('feature_sections', []) if s.get('project_id') != project_id
            ]
            save_json_data(FEATURE_SECTIONS_FILE, sections_data)
            return jsonify({'message': '工程删除成功', 'deleted': deleted})
        return jsonify({'message': '保存数据失败'}), 500
    except Exception as e:
        return jsonify({'message': f'删除工程失败: {str(e)}'}), 500

# --- API: 特征段 CRUD ---

@bp.route('/api/pricing/projects/<int:project_id>/feature_sections', methods=['GET'])
def get_project_sections(project_id):
    try:
        # 使用数据库操作替代JSON文件操作
        features = feature_service.get_features_by_project(project_id)
        return jsonify(features)
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@bp.route('/api/pricing/projects/<int:project_id>/feature_sections', methods=['POST'])
def create_feature_section(project_id):
    try:
        # 使用数据库操作替代JSON文件操作
        feature_data = request.json.copy()
        feature_data['project_id'] = project_id

        # 处理边界条件数据结构
        if '边界条件' in feature_data:
            boundary_conditions = feature_data.pop('边界条件')
            feature_data.update(boundary_conditions)

        # 创建特征段
        new_feature = feature_service.create_feature(feature_data)
        return jsonify({"message": "特征段创建成功", "section": new_feature}), 201
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@bp.route('/api/pricing/projects/<int:project_id>/feature_sections/<int:section_id>', methods=['PATCH'])
def update_feature_section(project_id, section_id):
    try:
        # 使用数据库操作替代JSON文件操作
        updates = request.json

        # 处理边界条件数据结构
        if '边界条件' in updates:
            boundary_conditions = updates.pop('边界条件')
            updates.update(boundary_conditions)

        # 更新特征段
        success = feature_service.update_feature(section_id, updates)
        if success:
            updated_feature = feature_service.get_feature_detail(section_id)
            return jsonify({"message": "特征段更新成功", "section": updated_feature})
        return jsonify({"error": "特征段不存在"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@bp.route('/api/pricing/projects/<int:project_id>/feature_sections/<int:section_id>', methods=['DELETE'])
def delete_feature_section(project_id, section_id):
    try:
        # 使用数据库操作替代JSON文件操作
        success = feature_service.delete_feature(section_id)
        if success:
            return jsonify({"message": "特征段删除成功"})
        return jsonify({"error": "特征段不存在"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@bp.route('/api/pricing/sections/<int:section_id>', methods=['GET'])
def get_section_detail(section_id):
    try:
        data = load_json_data(FEATURE_SECTIONS_FILE)
        sections = data.get('sections', [])
        section = next((s for s in sections if s.get('序号') == section_id), None)
        if not section:
            return jsonify({"error": "特征段不存在"}), 404
        return jsonify(section)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# --- API: 指标匹配 ---

@bp.route('/api/pricing/indicators/benti', methods=['GET'])
def query_benti_indicators():
    try:
        wind_speed = float(request.args.get('wind_speed', 0))
        ice_thickness = float(request.args.get('ice_thickness', 0))
        circuit_count = request.args.get('circuit_count', '')
        wire_type = request.args.get('wire_type', '')
        data = load_json_data(BENTI_DATA_FILE)
        indicators = data.get('indicators', [])
        matched = [i for i in indicators if (
            abs(i.get('风速', 0) - wind_speed) <= 2 and
            abs(i.get('覆冰', 0) - ice_thickness) <= 2 and
            i.get('回路数', '') == circuit_count and
            i.get('导线型号', '').lower() == wire_type.lower())]
        matched.sort(key=lambda x: abs(x.get('风速', 0) - wind_speed) + abs(x.get('覆冰', 0) - ice_thickness))
        return jsonify(matched)
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@bp.route('/api/pricing/indicators/qita', methods=['GET'])
def query_qita_indicators():
    try:
        wind_speed = float(request.args.get('wind_speed', 0))
        ice_thickness = float(request.args.get('ice_thickness', 0))
        circuit_count = request.args.get('circuit_count')
        qita_data = load_json_data(QITA_DATA_FILE)
        matched = sorted(
            [i for i in qita_data if str(i.get('回路数')) == str(circuit_count)],
            key=lambda x: abs(x.get('风速', 0) - wind_speed) + abs(x.get('覆冰', 0) - ice_thickness))
        return jsonify(matched)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# --- API: 工程详情、导出、指标量汇总等 ---

@bp.route('/api/pricing/projects/<int:project_id>/detail', methods=['GET'])
def get_project_detail(project_id):
    try:
        projects_data = load_json_data(PRICING_PROJECTS_FILE)
        projects = projects_data.get('projects', [])
        project = next((p for p in projects if p.get('序号') == project_id), None)
        if not project:
            return jsonify({"error": "工程不存在"}), 404
        sections_data = load_json_data(FEATURE_SECTIONS_FILE)
        sections = sections_data.get('sections', [])
        project_sections = [s for s in sections if s.get('工程序号') == project_id]
        benti_indicators = load_json_data(BENTI_DATA_FILE).get('indicators', [])
        qita_indicators = load_json_data(QITA_DATA_FILE).get('indicators', [])
        for s in project_sections:
            benti_indicator = next((i for i in benti_indicators if i.get('序号') == s.get('本体指标序号')), None)
            qita_indicator = next((i for i in qita_indicators if i.get('序号') == s.get('其他指标序号')), None)
            section_length = s.get('结束里程', 0) - s.get('起始里程', 0)
            if benti_indicator:
                s['本体费用'] = {
                    '单公里造价': benti_indicator.get('单公里造价', 0),
                    '总造价': round(benti_indicator.get('单公里造价', 0) * section_length, 2)
                }
            if qita_indicator:
                s['其他费用'] = {
                    '单公里造价': qita_indicator.get('单公里造价', 0),
                    '总造价': round(qita_indicator.get('单公里造价', 0) * section_length, 2)
                }
            s['特征段总造价'] = round(s.get('本体费用', {}).get('总造价', 0) + s.get('其他费用', {}).get('总造价', 0), 2)
        project['工程总造价'] = round(sum(s.get('特征段总造价', 0) for s in project_sections), 2)
        pricing_results_data = load_json_data(PRICING_RESULTS_FILE)
        pricing_results = pricing_results_data.get('results', [])
        result = next((r for r in pricing_results if r.get('工程序号') == project_id), None)
        record = {
            '工程序号': project_id,
            '特征段结果': project_sections,
            '工程总造价': project['工程总造价'],
            '组价时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        if result:
            result.update(record)
        else:
            pricing_results.append(record)
        pricing_results_data['results'] = pricing_results
        save_json_data(PRICING_RESULTS_FILE, pricing_results_data)
        return jsonify({'project': project, 'sections': project_sections})
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@bp.route('/api/pricing/projects/<int:project_id>/export', methods=['GET'])
def export_pricing_result(project_id):
    try:
        resp = get_project_detail(project_id)
        if resp.status_code != 200:
            return resp
        data = resp.get_json()
        project = data.get('project')
        sections = data.get('sections')
        output = BytesIO()
        workbook = xlsxwriter.Workbook(output)
        
        # 添加总算表工作表
        zongjs_sheet = workbook.add_worksheet('总算表(表一丙)')
        # 设置列宽
        zongjs_sheet.set_column(0, 0, 8)  # 序号列
        zongjs_sheet.set_column(1, 1, 30)  # 工程或费用名称列
        zongjs_sheet.set_column(2, 5, 15)  # 其他列
        
        # 定义格式
        title_format = workbook.add_format({
            'bold': True,
            'align': 'center',
            'valign': 'vcenter',
            'font_size': 12
        })
        header_format = workbook.add_format({
            'bold': True,
            'align': 'center',
            'valign': 'vcenter',
            'border': 1
        })
        cell_format = workbook.add_format({
            'align': 'center',
            'valign': 'vcenter',
            'border': 1
        })
        left_align = workbook.add_format({
            'align': 'left',
            'valign': 'vcenter',
            'border': 1
        })
        
        # 写入标题
        zongjs_sheet.merge_range('A1:F1', '架空线路工程总概算表', title_format)
        zongjs_sheet.merge_range('A2:C2', '表一丙', left_align)
        zongjs_sheet.merge_range('D2:F2', '金额单位：万元', workbook.add_format({
            'align': 'right',
            'valign': 'vcenter',
            'border': 1
        }))
        
        # 写入表头
        headers = ['序号', '工程或费用名称', '费用金额', '各项占\n静态投资%', '单位投资\n万元/km', '费用编码']
        for col, header in enumerate(headers):
            zongjs_sheet.write(2, col, header, header_format)
        
        # 写入数据
        data_rows = [
            ['一', '架空输电线路本体工程', 7062, 85.2, 630.54, ''],
            ['（一）', '一般线路本体工程', 7062, 85.2, 630.54, ''],
            ['（二）', '大跨越本体工程', '', '', '', ''],
            ['二', '辅助设施工程', '', '', '', ''],
            ['', '小计', 7062, 85.2, 630.54, ''],
            ['三', '编制基准期价差', 1215, 14.66, 108.48, '31840000000'],
            ['四', '设备购置费', 12, 0.14, 1.07, '318A0000000'],
            ['五', '其他费用', '', '', '', '31830000000'],
            ['', '其中：建设场地征用及清理费', 2, 0.02, 0.18, ''],
            ['六', '基本预备费', '', '', '', '318D0000000'],
            ['七', '特殊项目', '', '', '', '31850000000'],
            ['', '工程静态投资（一～七项合计）', 8289, 100, 740.09, '31860000000'],
            ['八', '动态费用', '', '', '', '31870000000'],
            ['（一）', '价差预备费', '', '', '', '31871000000'],
            ['（二）', '建设期贷款利息', '', '', '', '31872000000'],
            ['', '工程动态投资（一～八项合计）', 8289, '', 740.09, '318C0000000'],
            ['', '其中：可抵扣增值税额', 887, '', 79.2, '318G0000000']
        ]
        
        for row_idx, row_data in enumerate(data_rows, start=3):
            for col_idx, cell_value in enumerate(row_data):
                if col_idx == 1:  # 工程或费用名称列左对齐
                    zongjs_sheet.write(row_idx, col_idx, cell_value, left_align)
                else:
                    zongjs_sheet.write(row_idx, col_idx, cell_value, cell_format)
        
        # 添加原有的工程概况工作表
        overview = workbook.add_worksheet('工程概况')
        labels = ['工程名称', '电压等级', '线路总长度(km)', '特征段数量', '工程总造价(万元)', '组价时间']
        for i, lbl in enumerate(labels):
            overview.write(i, 0, lbl)
        overview.write(0, 1, project.get('工程名称'))
        overview.write(1, 1, project.get('电压等级'))
        overview.write(2, 1, project.get('线路总长度'))
        overview.write(3, 1, project.get('特征段数量'))
        overview.write(4, 1, project.get('工程总造价'))
        overview.write(5, 1, project.get('组价时间'))
        
        # 添加原有的特征段明细工作表
        sheet = workbook.add_worksheet('特征段明细')
        headers = ['特征段名称', '线路长度(km)', '风速(m/s)', '覆冰(mm)', '回路数', '导线规格', '本体费用单价', '本体费用合计', '其他费用单价', '其他费用合计', '特征段总造价']
        for col, h in enumerate(headers):
            sheet.write(0, col, h)
        for row, s in enumerate(sections, start=1):
            sheet.write(row, 0, s.get('特征段名称'))
            sheet.write(row, 1, s.get('线路长度'))
            sheet.write(row, 2, s.get('边界条件', {}).get('风速'))
            sheet.write(row, 3, s.get('边界条件', {}).get('覆冰'))
            sheet.write(row, 4, s.get('边界条件', {}).get('回路数'))
            sheet.write(row, 5, s.get('边界条件', {}).get('导线规格'))
            sheet.write(row, 6, s.get('本体费用', {}).get('单公里造价'))
            sheet.write(row, 7, s.get('本体费用', {}).get('总造价'))
            sheet.write(row, 8, s.get('其他费用', {}).get('单公里造价'))
            sheet.write(row, 9, s.get('其他费用', {}).get('总造价'))
            sheet.write(row, 10, s.get('特征段总造价'))
        
        workbook.close()
        output.seek(0)
        return send_file(output, mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', as_attachment=True, download_name=f"{project.get('工程名称')}_组价结果.xlsx")
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@bp.route('/api/pricing/projects/<int:project_id>/feature_sections/<int:section_id>')
def get_feature_section(project_id, section_id):
    try:
        # 使用数据库操作替代JSON文件操作
        feature = feature_service.get_feature_detail(section_id)
        if feature and feature.get('project_id') == project_id:
            return jsonify(feature)
        return jsonify({'error': '特征段不存在'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route('/api/pricing/projects/<int:project_id>/feature_sections/<int:section_id>/indicators', methods=['POST'])
def save_section_indicators_v2(project_id, section_id):
    try:
        data = request.json
        feature_sections = load_json_data(FEATURE_SECTIONS_FILE)
        for section in feature_sections.get('feature_sections', []):
            if section.get('序号') == section_id and section.get('工程序号') == project_id:
                section['本体指标序号'] = data.get('本体指标序号')
                section['其他指标序号'] = data.get('其他指标序号')
                section['指标选择状态'] = '已选择'
                break
        if save_json_data(FEATURE_SECTIONS_FILE, feature_sections):
            return jsonify({'message': '指标选择保存成功'})
        return jsonify({'error': '保存数据失败'}), 500
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# --- API: 工程指标量汇总 ---

@bp.route('/api/pricing/projects/<int:project_id>/indicators/summary', methods=['POST'])
def summarize_project_indicators(project_id):
    try:
        projects_data = load_json_data(PRICING_PROJECTS_FILE)
        projects = projects_data.get('projects', [])
        sections_data = load_json_data(FEATURE_SECTIONS_FILE)
        sections = sections_data.get('feature_sections', [])
        project = next((p for p in projects if p.get('序号') == project_id), None)
        if not project:
            return jsonify({'error': '工程不存在'}), 404
        project_sections = [s for s in sections if s.get('工程序号') == project_id]
        uncalculated_sections = [s for s in project_sections if s.get('指标计算状态') != '已计算']
        if uncalculated_sections:
            return jsonify({'error': '存在未计算的特征段，请先完成所有特征段的指标计算'}), 400
        project['指标汇总状态'] = '已汇总'
        project['指标汇总时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        if save_json_data(PRICING_PROJECTS_FILE, projects_data):
            return jsonify({'message': '指标量汇总成功'})
        return jsonify({'error': '保存数据失败'}), 500
    except Exception as e:
        return jsonify({'error': str(e)}), 500 

# ---- 旧实现保留 ----

def export_zongjs_old(project_id):
    try:
        # 获取工程详情
        projects_data = load_json_data(PRICING_PROJECTS_FILE)
        projects = projects_data.get('projects', [])
        project = next((p for p in projects if p.get('序号') == project_id), None)
        if not project:
            return jsonify({"error": "工程不存在"}), 404
            
        # 创建Excel文件
        output = BytesIO()
        workbook = xlsxwriter.Workbook(output)
        
        # 添加总算表工作表
        zongjs_sheet = workbook.add_worksheet('总算表(表一丙)')
        # 设置列宽
        zongjs_sheet.set_column(0, 0, 8)  # 序号列
        zongjs_sheet.set_column(1, 1, 30)  # 工程或费用名称列
        zongjs_sheet.set_column(2, 5, 15)  # 其他列
        
        # 定义格式
        title_format = workbook.add_format({
            'bold': True,
            'align': 'center',
            'valign': 'vcenter',
            'font_size': 12
        })
        header_format = workbook.add_format({
            'bold': True,
            'align': 'center',
            'valign': 'vcenter',
            'border': 1
        })
        cell_format = workbook.add_format({
            'align': 'center',
            'valign': 'vcenter',
            'border': 1
        })
        left_align = workbook.add_format({
            'align': 'left',
            'valign': 'vcenter',
            'border': 1
        })
        
        # 写入标题
        zongjs_sheet.merge_range(0, 0, 0, 5, '架空线路工程总概算表', title_format)
        zongjs_sheet.merge_range(1, 0, 1, 2, '表一丙', left_align)
        zongjs_sheet.merge_range(1, 3, 1, 5, '金额单位：万元', workbook.add_format({
            'align': 'right',
            'valign': 'vcenter',
            'border': 1
        }))
        
        # 写入表头
        headers = ['序号', '工程或费用名称', '费用金额', '各项占\n静态投资%', '单位投资\n万元/km', '费用编码']
        for col, header in enumerate(headers):
            zongjs_sheet.write(2, col, header, header_format)
        
        # 写入数据
        data_rows = [
            ['一', '架空输电线路本体工程', 7062, 85.2, 630.54, ''],
            ['（一）', '一般线路本体工程', 7062, 85.2, 630.54, ''],
            ['（二）', '大跨越本体工程', '', '', '', ''],
            ['二', '辅助设施工程', '', '', '', ''],
            ['', '小计', 7062, 85.2, 630.54, ''],
            ['三', '编制基准期价差', 1215, 14.66, 108.48, '31840000000'],
            ['四', '设备购置费', 12, 0.14, 1.07, '318A0000000'],
            ['五', '其他费用', '', '', '', '31830000000'],
            ['', '其中：建设场地征用及清理费', 2, 0.02, 0.18, ''],
            ['六', '基本预备费', '', '', '', '318D0000000'],
            ['七', '特殊项目', '', '', '', '31850000000'],
            ['', '工程静态投资（一～七项合计）', 8289, 100, 740.09, '31860000000'],
            ['八', '动态费用', '', '', '', '31870000000'],
            ['（一）', '价差预备费', '', '', '', '31871000000'],
            ['（二）', '建设期贷款利息', '', '', '', '31872000000'],
            ['', '工程动态投资（一～八项合计）', 8289, '', 740.09, '318C0000000'],
            ['', '其中：可抵扣增值税额', 887, '', 79.2, '318G0000000']
        ]
        
        for row_idx, row_data in enumerate(data_rows, start=3):
            for col_idx, cell_value in enumerate(row_data):
                if col_idx == 1:  # 工程或费用名称列左对齐
                    zongjs_sheet.write(row_idx, col_idx, cell_value, left_align)
                else:
                    zongjs_sheet.write(row_idx, col_idx, cell_value, cell_format)
        
        workbook.close()
        output.seek(0)
        
        # 设置文件名
        filename = f"{project.get('工程名称', '未命名')}_架空线路工程总算表(表一丙).xlsx"
        
        return send_file(output, mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', as_attachment=True, download_name=filename)
    except Exception as e:
        return jsonify({"error": str(e)}), 500 

# ---- 新实现：使用模板 xlsx + 占位符方式（演示静态） ----

@bp.route('/api/pricing/projects/<int:project_id>/export_zongjs', methods=['GET'])
def export_zongjs(project_id):
    """基于固定 Excel 模板生成总算表（演示版）。"""
    try:
        projects_data = load_json_data(PRICING_PROJECTS_FILE)
        project = next((p for p in projects_data.get('projects', []) if p.get('序号') == project_id), None)
        if not project:
            return jsonify({"error": "工程不存在"}), 404

        template_xlsx = os.path.join('templatefiles', 'pricing', 'xxxxx-架空线路工程总算表（表一丙）.xlsx')
        if not os.path.exists(template_xlsx):
            return jsonify({"error": "模板文件缺失"}), 500

        # 当前演示：不做占位符替换，直接返回模板
        with open(template_xlsx, 'rb') as f:
            data = f.read()

        output = BytesIO(data)
        filename = f"{project.get('工程名称', '工程')}_架空线路工程总算表(表一丙).xlsx"
        return send_file(output, mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', as_attachment=True, download_name=filename)
    except Exception as e:
        return jsonify({"error": str(e)}), 500 

# --- 旧实现：Markdown 解析生成 Word ---
def export_pricing_report_old(project_id):
    """根据 Markdown 模板生成演示版组价报告 Word 文件（完整结构与表格）。"""
    try:
        import re
        # 校验工程
        projects_data = load_json_data(PRICING_PROJECTS_FILE)
        project = next((p for p in projects_data.get('projects', []) if p.get('序号') == project_id), None)
        if not project:
            return jsonify({"error": "工程不存在"}), 404
 
        template_path = os.path.join('templatefiles', 'markdown', 'xxxxx-工程组价报告.md')
        if not os.path.exists(template_path):
            return jsonify({"error": "模板文件缺失"}), 500
 
        with open(template_path, 'r', encoding='utf-8') as f:
            md_lines = f.readlines()
 
        doc = Document()
        normal_style = doc.styles['Normal']
        normal_style.font.name = u'宋体'
        normal_style._element.rPr.rFonts.set(qn('w:eastAsia'), u'宋体')
        normal_style.font.size = Pt(10.5)
        normal_style.font.color.rgb = RGBColor(0, 0, 0)

        # 设置标题样式（黑色、宋体）
        def _set_heading_style(paragraph):
            for run in paragraph.runs:
                run.font.color.rgb = RGBColor(0, 0, 0)
                run.font.name = u'宋体'
                r = run._element
                r.rPr.rFonts.set(qn('w:eastAsia'), u'宋体')
 
        i = 0
        while i < len(md_lines):
            line = md_lines[i].rstrip('\n')
 
            # 处理标题
            if line.startswith('#'):
                level = len(line) - len(line.lstrip('#'))
                heading_text = line.lstrip('#').strip()
                para = doc.add_heading(heading_text, level=min(level, 4))
                _set_heading_style(para)
                i += 1
                continue
 
            # 处理编号列表
            if re.match(r'^\d+\.\s', line):
                doc.add_paragraph(line.strip(), style='List Number')
                i += 1
                continue
 
            # 处理空行
            if line.strip() == '':
                doc.add_paragraph('')
                i += 1
                continue
 
            # 处理表格（html 格式）
            if '<table' in line:
                table_lines = []
                while i < len(md_lines):
                    table_lines.append(md_lines[i])
                    if '</table>' in md_lines[i]:
                        break
                    i += 1
                # 包含结束行
                i += 1
                rows = _parse_html_table(table_lines)
                if rows:
                    cols = max(len(r) for r in rows)
                    tbl = doc.add_table(rows=len(rows), cols=cols)
                    tbl.style = 'Table Grid'
                    for r_idx, row in enumerate(rows):
                        for c_idx in range(cols):
                            txt = row[c_idx] if c_idx < len(row) else ''
                            tbl.cell(r_idx, c_idx).text = txt
                continue
 
            # 其他普通段落
            doc.add_paragraph(line)
            i += 1
 
        # 保存
        file_stream = io.BytesIO()
        doc.save(file_stream)
        file_stream.seek(0)
        filename = f"{project.get('工程名称', '工程')}_组价报告.docx"
        return send_file(file_stream, as_attachment=True, mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document', download_name=filename)
    except Exception as e:
        return jsonify({"error": str(e)}), 500
 

def _parse_html_table(tbl_lines):
     """简易解析 <table> ... </table> html 片段为二维列表"""
     import re
     html = ''.join(tbl_lines)
     # 提取 tr
     tr_blocks = re.findall(r'<tr[^>]*>(.*?)</tr>', html, flags=re.S | re.I)
     rows = []
     cell_pattern = re.compile(r'<t[dh][^>]*>(.*?)</t[dh]>', flags=re.S | re.I)
     tag_pattern = re.compile(r'<[^>]+>')
     for block in tr_blocks:
         cells_raw = cell_pattern.findall(block)
         cells = [tag_pattern.sub('', c).replace('\n', '').strip() for c in cells_raw]
         rows.append(cells)
     return rows 

# --- 新实现：docxtpl 渲染 Word 模板 ---
# 保持同一路由
@bp.route('/api/pricing/projects/<int:project_id>/export_report', methods=['GET'])
def export_pricing_report(project_id):
    """使用 docxtpl 依据固定 Word 模板生成组价报告（演示数据）。"""
    try:
        # 校验工程存在
        projects_data = load_json_data(PRICING_PROJECTS_FILE)
        project = next((p for p in projects_data.get('projects', []) if p.get('序号') == project_id), None)
        if not project:
            return jsonify({"error": "工程不存在"}), 404

        template_docx = os.path.join('templatefiles', 'pricing', 'xxxxx-工程组价报告.docx')
        if not os.path.exists(template_docx):
            return jsonify({"error": "模板文件缺失"}), 500

        # 准备演示上下文（模板里如有未声明变量，保持空白）
        env = Environment(loader=BaseLoader(), autoescape=True)
        env.undefined = Undefined  # 未声明变量不报错

        doc = DocxTemplate(template_docx)
        doc.render({
            'project_name': project.get('工程名称', '演示工程'),
            'voltage': project.get('电压等级', '500kV'),
            'line_length': project.get('线路总长度', '50'),
            'section_count': project.get('特征段数量', '3'),
            # 其他占位符按需添加
        }, env)

        file_stream = io.BytesIO()
        doc.save(file_stream)
        file_stream.seek(0)
        filename = f"{project.get('工程名称', '工程')}_组价报告.docx"
        return send_file(file_stream, as_attachment=True,
                         mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                         download_name=filename)
    except Exception as e:
        return jsonify({"error": str(e)}), 500