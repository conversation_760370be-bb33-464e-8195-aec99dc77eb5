from flask import Blueprint, render_template, jsonify, request
import os
import json
import sqlite3
from datetime import datetime

bp = Blueprint('indicator_rule', __name__)

# 数据文件路径
DATA_DIR = 'data'
DB_PATH = os.path.join(DATA_DIR, 'indicator_management.db')

def get_db_connection():
    """获取数据库连接"""
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row  # 使结果可以像字典一样访问
    return conn
@bp.route('/indicatorAndrule')
def indicator_rule_index():
    """指标及规则管理主页面"""
    return render_template('IndicatorAndrule/indicatorAndrule.html')

@bp.route('/indicatorAndrule/indicator')
def indicator_management():
    """指标管理页面"""
    return render_template('IndicatorAndrule/indicatorAndrule.html', active_tab='indicator')

@bp.route('/indicatorAndrule/price')
def price_management():
    """单价管理页面"""
    return render_template('IndicatorAndrule/indicatorAndrule.html', active_tab='price')

@bp.route('/indicatorAndrule/ontology')
def ontology_calculation():
    """本体计算逻辑页面"""
    return render_template('IndicatorAndrule/indicatorAndrule.html', active_tab='ontology')

@bp.route('/indicatorAndrule/audit')
def audit_database():
    """校审数据库页面"""
    return render_template('IndicatorAndrule/indicatorAndrule.html', active_tab='audit')

# ==================== 本体指标API接口 ====================

@bp.route('/api/indicator_rule/benti/list')
def api_get_benti_indicators():
    """获取本体指标列表API"""
    try:
        # 获取查询参数
        name = request.args.get('name', '').strip()
        category = request.args.get('category', '').strip()
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))

        conn = get_db_connection()
        cursor = conn.cursor()

        # 构建查询条件
        where_conditions = []
        params = []

        if name:
            where_conditions.append("指标名称 LIKE ?")
            params.append(f"%{name}%")

        if category:
            where_conditions.append("分类 = ?")
            params.append(category)

        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # 查询总记录数
        count_sql = f"SELECT COUNT(*) FROM t_indicator_benti WHERE {where_clause}"
        cursor.execute(count_sql, params)
        total_records = cursor.fetchone()[0]

        # 查询分页数据 - 按更新时间、创建时间倒序排列
        offset = (page - 1) * page_size
        data_sql = f"SELECT * FROM t_indicator_benti WHERE {where_clause} ORDER BY 更新时间 DESC, 创建时间 DESC, 序号 ASC LIMIT ? OFFSET ?"
        cursor.execute(data_sql, params + [page_size, offset])
        rows = cursor.fetchall()

        # 转换为字典列表
        data = [dict(row) for row in rows]

        conn.close()

        return jsonify({
            'success': True,
            'data': data,
            'total': total_records,
            'page': page,
            'pageSize': page_size,
            'message': '获取本体指标列表成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'data': [],
            'total': 0,
            'message': f'获取本体指标列表失败: {str(e)}'
        })

@bp.route('/api/indicator_rule/benti/delete/<int:indicator_id>', methods=['DELETE'])
def api_delete_benti_indicator(indicator_id):
    """删除本体指标API"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查指标是否存在
        cursor.execute("SELECT id FROM t_indicator_benti WHERE id = ?", (indicator_id,))
        if not cursor.fetchone():
            conn.close()
            return jsonify({
                'success': False,
                'message': '指标不存在'
            })

        # 删除指标
        cursor.execute("DELETE FROM t_indicator_benti WHERE id = ?", (indicator_id,))
        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': '删除本体指标成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'删除本体指标失败: {str(e)}'
        })

# ==================== 其他指标API接口 ====================

@bp.route('/api/indicator_rule/qita/list')
def api_get_qita_indicators():
    """获取其他指标列表API"""
    try:
        # 获取查询参数
        name = request.args.get('name', '').strip()
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))

        conn = get_db_connection()
        cursor = conn.cursor()

        # 构建查询条件
        where_conditions = []
        params = []

        if name:
            where_conditions.append("指标名称 LIKE ?")
            params.append(f"%{name}%")

        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # 查询总记录数
        count_sql = f"SELECT COUNT(*) FROM t_indicator_qita WHERE {where_clause}"
        cursor.execute(count_sql, params)
        total_records = cursor.fetchone()[0]

        # 查询分页数据 - 按更新时间、创建时间倒序排列
        offset = (page - 1) * page_size
        data_sql = f"SELECT * FROM t_indicator_qita WHERE {where_clause} ORDER BY 更新时间 DESC, 创建时间 DESC, 序号 ASC LIMIT ? OFFSET ?"
        cursor.execute(data_sql, params + [page_size, offset])
        rows = cursor.fetchall()

        # 转换为字典列表
        data = [dict(row) for row in rows]

        conn.close()

        return jsonify({
            'success': True,
            'data': data,
            'total': total_records,
            'page': page,
            'pageSize': page_size,
            'message': '获取其他指标列表成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'data': [],
            'total': 0,
            'message': f'获取其他指标列表失败: {str(e)}'
        })

@bp.route('/api/indicator_rule/qita/delete/<int:indicator_id>', methods=['DELETE'])
def api_delete_qita_indicator(indicator_id):
    """删除其他指标API"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查指标是否存在
        cursor.execute("SELECT id FROM t_indicator_qita WHERE id = ?", (indicator_id,))
        if not cursor.fetchone():
            conn.close()
            return jsonify({
                'success': False,
                'message': '指标不存在'
            })

        # 删除指标
        cursor.execute("DELETE FROM t_indicator_qita WHERE id = ?", (indicator_id,))
        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': '删除其他指标成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'删除其他指标失败: {str(e)}'
        })

# API接口示例（后续可扩展）
@bp.route('/api/indicator_rule/indicators')
def api_get_indicators():
    """获取指标列表API"""
    # 这里可以添加具体的数据获取逻辑
    return jsonify({
        'success': True,
        'data': [],
        'message': '获取指标列表成功'
    })

# ==================== 单价管理API接口 ====================

@bp.route('/api/indicator_rule/price/list')
def api_get_price_list():
    """获取单价列表API"""
    try:
        # 获取查询参数
        name = request.args.get('name', '').strip()
        category = request.args.get('category', '').strip()
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))

        conn = get_db_connection()
        cursor = conn.cursor()

        # 构建查询条件
        where_conditions = []
        params = []

        if name:
            where_conditions.append("指标名称 LIKE ?")
            params.append(f"%{name}%")

        if category:
            where_conditions.append("指标分类 = ?")
            params.append(category)

        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # 查询总记录数
        count_sql = f"SELECT COUNT(*) FROM t_benti_danjia WHERE {where_clause}"
        cursor.execute(count_sql, params)
        total_records = cursor.fetchone()[0]

        # 查询分页数据 - 按序号排序
        offset = (page - 1) * page_size
        data_sql = f"SELECT * FROM t_benti_danjia WHERE {where_clause} ORDER BY 序号 ASC LIMIT ? OFFSET ?"
        cursor.execute(data_sql, params + [page_size, offset])
        rows = cursor.fetchall()

        # 转换为字典列表
        data = [dict(row) for row in rows]

        conn.close()

        return jsonify({
            'success': True,
            'data': data,
            'total': total_records,
            'page': page,
            'pageSize': page_size,
            'message': '获取单价列表成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'data': [],
            'total': 0,
            'message': f'获取单价列表失败: {str(e)}'
        })

@bp.route('/api/indicator_rule/price/categories')
def api_get_price_categories():
    """获取单价分类列表API"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT DISTINCT 指标分类 FROM t_benti_danjia WHERE 指标分类 IS NOT NULL AND 指标分类 != '' ORDER BY 指标分类")
        rows = cursor.fetchall()

        categories = [row[0] for row in rows]
        conn.close()

        return jsonify({
            'success': True,
            'data': categories,
            'message': '获取分类列表成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'data': [],
            'message': f'获取分类列表失败: {str(e)}'
        })

@bp.route('/api/indicator_rule/price/<int:price_id>')
def api_get_price_detail(price_id):
    """获取单个单价详情API"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM t_benti_danjia WHERE id = ?", (price_id,))
        row = cursor.fetchone()

        if row:
            data = dict(row)
            conn.close()
            return jsonify({
                'success': True,
                'data': data,
                'message': '获取单价详情成功'
            })
        else:
            conn.close()
            return jsonify({
                'success': False,
                'message': '单价信息不存在'
            }), 404

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取单价详情失败: {str(e)}'
        }), 500

@bp.route('/api/indicator_rule/price', methods=['POST'])
def api_create_price():
    """创建新单价API"""
    try:
        data = request.get_json()

        # 验证必填字段
        required_fields = ['序号', '指标名称', '指标单位', '指标分类', '指标单价']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({
                    'success': False,
                    'message': f'缺少必填字段: {field}'
                }), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查序号是否已存在
        cursor.execute("SELECT COUNT(*) FROM t_benti_danjia WHERE 序号 = ?", (data['序号'],))
        if cursor.fetchone()[0] > 0:
            conn.close()
            return jsonify({
                'success': False,
                'message': '序号已存在，请使用其他序号'
            }), 400

        # 插入新记录
        cursor.execute("""
            INSERT INTO t_benti_danjia (序号, 指标名称, 指标单位, 指标分类, 指标单价, 更新时间)
            VALUES (?, ?, ?, ?, ?, datetime('now', 'localtime'))
        """, (data['序号'], data['指标名称'], data['指标单位'], data['指标分类'], data['指标单价']))

        conn.commit()
        new_id = cursor.lastrowid
        conn.close()

        return jsonify({
            'success': True,
            'data': {'id': new_id},
            'message': '创建单价成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'创建单价失败: {str(e)}'
        }), 500

@bp.route('/api/indicator_rule/price/<int:price_id>', methods=['PUT'])
def api_update_price(price_id):
    """更新单价API"""
    try:
        data = request.get_json()

        # 验证必填字段
        required_fields = ['序号', '指标名称', '指标单位', '指标分类', '指标单价']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({
                    'success': False,
                    'message': f'缺少必填字段: {field}'
                }), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查记录是否存在
        cursor.execute("SELECT COUNT(*) FROM t_benti_danjia WHERE id = ?", (price_id,))
        if cursor.fetchone()[0] == 0:
            conn.close()
            return jsonify({
                'success': False,
                'message': '单价信息不存在'
            }), 404

        # 检查序号是否被其他记录使用
        cursor.execute("SELECT COUNT(*) FROM t_benti_danjia WHERE 序号 = ? AND id != ?", (data['序号'], price_id))
        if cursor.fetchone()[0] > 0:
            conn.close()
            return jsonify({
                'success': False,
                'message': '序号已被其他记录使用，请使用其他序号'
            }), 400

        # 更新记录
        cursor.execute("""
            UPDATE t_benti_danjia
            SET 序号 = ?, 指标名称 = ?, 指标单位 = ?, 指标分类 = ?, 指标单价 = ?, 更新时间 = datetime('now', 'localtime')
            WHERE id = ?
        """, (data['序号'], data['指标名称'], data['指标单位'], data['指标分类'], data['指标单价'], price_id))

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': '更新单价成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'更新单价失败: {str(e)}'
        }), 500

@bp.route('/api/indicator_rule/price/delete/<int:price_id>', methods=['DELETE'])
def api_delete_price(price_id):
    """删除单价API"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查记录是否存在
        cursor.execute("SELECT COUNT(*) FROM t_benti_danjia WHERE id = ?", (price_id,))
        if cursor.fetchone()[0] == 0:
            conn.close()
            return jsonify({
                'success': False,
                'message': '单价信息不存在'
            }), 404

        # 删除记录
        cursor.execute("DELETE FROM t_benti_danjia WHERE id = ?", (price_id,))
        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': '删除单价成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'删除单价失败: {str(e)}'
        }), 500


# ==================== 指标详情和更新API ====================

@bp.route('/api/indicator_rule/benti/<int:indicator_id>')
def api_get_benti_indicator_detail(indicator_id):
    """获取单个本体指标详情API"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM t_indicator_benti WHERE id = ?", (indicator_id,))
        row = cursor.fetchone()

        if row:
            data = dict(row)
            conn.close()
            return jsonify({
                'success': True,
                'data': data,
                'message': '获取本体指标详情成功'
            })
        else:
            conn.close()
            return jsonify({
                'success': False,
                'message': '指标不存在'
            }), 404

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取本体指标详情失败: {str(e)}'
        }), 500


@bp.route('/api/indicator_rule/qita/<int:indicator_id>')
def api_get_qita_indicator_detail(indicator_id):
    """获取单个其他指标详情API"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM t_indicator_qita WHERE id = ?", (indicator_id,))
        row = cursor.fetchone()

        if row:
            data = dict(row)
            conn.close()
            return jsonify({
                'success': True,
                'data': data,
                'message': '获取其他指标详情成功'
            })
        else:
            conn.close()
            return jsonify({
                'success': False,
                'message': '指标不存在'
            }), 404

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取其他指标详情失败: {str(e)}'
        }), 500


@bp.route('/api/indicator_rule/benti/<int:indicator_id>', methods=['PUT'])
def api_update_benti_indicator(indicator_id):
    """更新本体指标API"""
    try:
        data = request.get_json()

        # 验证数据
        if not data:
            return jsonify({
                'success': False,
                'message': '请提供更新数据'
            }), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查指标是否存在
        cursor.execute("SELECT id FROM t_indicator_benti WHERE id = ?", (indicator_id,))
        if not cursor.fetchone():
            conn.close()
            return jsonify({
                'success': False,
                'message': '指标不存在'
            }), 404

        # 构建更新SQL
        update_fields = []
        params = []

        # 可更新的字段
        updatable_fields = ['序号', '指标名称', '单位', '分类', '概算表', '章节', '定额编号', '匹配字段', '指标提取业务逻辑', '指标提取技术逻辑']

        for field in updatable_fields:
            if field in data:
                update_fields.append(f"{field} = ?")
                params.append(data[field])

        # 处理单价字段 - 特殊处理
        if '单价' in data:
            price = data.get('单价')
            if price is not None and price != '':
                if price == '/':
                    price = '/'  # 保持"/"字符串
                else:
                    try:
                        price_num = float(price)
                        if price_num < 0:
                            price = '/'
                        else:
                            price = price_num  # 保存为数字
                    except (ValueError, TypeError):
                        price = '/'
            else:
                price = '/'

            update_fields.append("单价 = ?")
            params.append(price)

        if not update_fields:
            conn.close()
            return jsonify({
                'success': False,
                'message': '没有可更新的字段'
            }), 400

        # 添加更新时间
        update_fields.append("更新时间 = CURRENT_TIMESTAMP")
        params.append(indicator_id)

        update_sql = f"UPDATE t_indicator_benti SET {', '.join(update_fields)} WHERE id = ?"
        cursor.execute(update_sql, params)

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': '本体指标更新成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'更新本体指标失败: {str(e)}'
        }), 500


@bp.route('/api/indicator_rule/benti/max_seq')
def api_get_benti_max_seq():
    """获取本体指标最大序号API"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT MAX(CAST(序号 AS INTEGER)) as max_seq FROM t_indicator_benti")
        row = cursor.fetchone()

        max_seq = row['max_seq'] if row['max_seq'] is not None else 0
        conn.close()

        return jsonify({
            'success': True,
            'data': {'max_seq': max_seq},
            'message': '获取最大序号成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取最大序号失败: {str(e)}'
        }), 500


@bp.route('/api/indicator_rule/benti', methods=['POST'])
def api_create_benti_indicator():
    """创建新的本体指标API"""
    try:
        data = request.get_json()

        # 验证数据
        if not data:
            return jsonify({
                'success': False,
                'message': '请提供指标数据'
            }), 400

        # 验证必填字段
        if not data.get('指标名称', '').strip():
            return jsonify({
                'success': False,
                'message': '指标名称不能为空'
            }), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # 插入新指标
        insert_sql = """
            INSERT INTO t_indicator_benti
            (序号, 指标名称, 单位, 分类, 概算表, 章节, 定额编号, 匹配字段, 指标提取业务逻辑, 指标提取技术逻辑, 单价, 创建时间, 更新时间)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        """

        # 处理单价字段 - 可以为数字或"/"
        price = data.get('单价')
        if price is not None and price != '':
            if price == '/':
                price = '/'  # 保持"/"字符串
            else:
                try:
                    price_num = float(price)
                    if price_num < 0:
                        price = '/'
                    else:
                        price = price_num  # 保存为数字
                except (ValueError, TypeError):
                    price = '/'
        else:
            price = '/'

        cursor.execute(insert_sql, (
            data.get('序号', ''),
            data.get('指标名称', ''),
            data.get('单位', ''),
            data.get('分类', ''),
            data.get('概算表', ''),
            data.get('章节', ''),
            data.get('定额编号', ''),
            data.get('匹配字段', ''),
            data.get('指标提取业务逻辑', ''),
            data.get('指标提取技术逻辑', ''),
            price
        ))

        conn.commit()
        new_id = cursor.lastrowid
        conn.close()

        return jsonify({
            'success': True,
            'data': {'id': new_id},
            'message': '本体指标创建成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'创建本体指标失败: {str(e)}'
        }), 500


@bp.route('/api/indicator_rule/qita/<int:indicator_id>', methods=['PUT'])
def api_update_qita_indicator(indicator_id):
    """更新其他指标API"""
    try:
        data = request.get_json()

        # 验证数据
        if not data:
            return jsonify({
                'success': False,
                'message': '请提供更新数据'
            }), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查指标是否存在
        cursor.execute("SELECT id FROM t_indicator_qita WHERE id = ?", (indicator_id,))
        if not cursor.fetchone():
            conn.close()
            return jsonify({
                'success': False,
                'message': '指标不存在'
            }), 404

        # 构建更新SQL
        update_fields = []
        params = []

        # 可更新的字段
        updatable_fields = ['序号', '指标名称', '单位', '分类', '概算表', '章节', '定额编号', '匹配字段', '指标提取业务逻辑', '指标提取技术逻辑']

        for field in updatable_fields:
            if field in data:
                update_fields.append(f"{field} = ?")
                params.append(data[field])

        if not update_fields:
            conn.close()
            return jsonify({
                'success': False,
                'message': '没有可更新的字段'
            }), 400

        # 添加更新时间
        update_fields.append("更新时间 = CURRENT_TIMESTAMP")
        params.append(indicator_id)

        update_sql = f"UPDATE t_indicator_qita SET {', '.join(update_fields)} WHERE id = ?"
        cursor.execute(update_sql, params)

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': '其他指标更新成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'更新其他指标失败: {str(e)}'
        }), 500


@bp.route('/api/indicator_rule/qita/max_seq')
def api_get_qita_max_seq():
    """获取其他指标最大序号API"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT MAX(CAST(序号 AS INTEGER)) as max_seq FROM t_indicator_qita")
        row = cursor.fetchone()

        max_seq = row['max_seq'] if row['max_seq'] is not None else 0
        conn.close()

        return jsonify({
            'success': True,
            'data': {'max_seq': max_seq},
            'message': '获取最大序号成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取最大序号失败: {str(e)}'
        }), 500


@bp.route('/api/indicator_rule/qita', methods=['POST'])
def api_create_qita_indicator():
    """创建新的其他指标API"""
    try:
        data = request.get_json()

        # 验证数据
        if not data:
            return jsonify({
                'success': False,
                'message': '请提供指标数据'
            }), 400

        # 验证必填字段
        if not data.get('指标名称', '').strip():
            return jsonify({
                'success': False,
                'message': '指标名称不能为空'
            }), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # 插入新指标
        insert_sql = """
            INSERT INTO t_indicator_qita
            (序号, 指标名称, 单位, 概算表, 匹配字段, 指标提取业务逻辑, 指标提取技术逻辑, 创建时间, 更新时间)
            VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        """

        cursor.execute(insert_sql, (
            data.get('序号', ''),
            data.get('指标名称', ''),
            data.get('单位', ''),
            data.get('概算表', ''),
            data.get('匹配字段', ''),
            data.get('指标提取业务逻辑', ''),
            data.get('指标提取技术逻辑', '')
        ))

        conn.commit()
        new_id = cursor.lastrowid
        conn.close()

        return jsonify({
            'success': True,
            'data': {'id': new_id},
            'message': '其他指标创建成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'创建其他指标失败: {str(e)}'
        }), 500

# ==================== 本体费用计算逻辑 API ====================

@bp.route('/api/indicator_rule/ontology/list', methods=['GET'])
def api_get_ontology_list():
    """获取本体费用列表API"""
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        name_filter = request.args.get('name', '').strip()

        conn = get_db_connection()
        cursor = conn.cursor()

        # 构建查询条件
        where_conditions = []
        params = []

        if name_filter:
            where_conditions.append("费用名称 LIKE ?")
            params.append(f'%{name_filter}%')

        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""

        # 查询总数
        count_sql = f"SELECT COUNT(*) FROM t_benti_ontology{where_clause}"
        cursor.execute(count_sql, params)
        total = cursor.fetchone()[0]

        # 查询数据
        offset = (page - 1) * page_size
        data_sql = f"""
            SELECT id, 费用序号, 费用名称, 计算逻辑, 费用关系, 创建时间, 更新时间
            FROM t_benti_ontology{where_clause}
            ORDER BY 费用序号
            LIMIT ? OFFSET ?
        """
        cursor.execute(data_sql, params + [page_size, offset])

        rows = cursor.fetchall()
        data = []
        for row in rows:
            data.append({
                'id': row[0],
                '费用序号': row[1],
                '费用名称': row[2],
                '计算逻辑': row[3],
                '费用关系': row[4],
                '创建时间': row[5],
                '更新时间': row[6]
            })

        conn.close()

        return jsonify({
            'success': True,
            'data': data,
            'total': total,
            'page': page,
            'pageSize': page_size,
            'totalPages': (total + page_size - 1) // page_size
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取本体费用列表失败: {str(e)}'
        }), 500

@bp.route('/api/indicator_rule/ontology/<int:ontology_id>', methods=['GET'])
def api_get_ontology_detail(ontology_id):
    """获取本体费用详情API"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT id, 费用序号, 费用名称, 计算逻辑, 费用关系, 创建时间, 更新时间
            FROM t_benti_ontology
            WHERE id = ?
        """, (ontology_id,))

        row = cursor.fetchone()
        conn.close()

        if not row:
            return jsonify({
                'success': False,
                'message': '本体费用不存在'
            }), 404

        data = {
            'id': row[0],
            '费用序号': row[1],
            '费用名称': row[2],
            '计算逻辑': row[3],
            '费用关系': row[4],
            '创建时间': row[5],
            '更新时间': row[6]
        }

        return jsonify({
            'success': True,
            'data': data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取本体费用详情失败: {str(e)}'
        }), 500

@bp.route('/api/indicator_rule/ontology', methods=['POST'])
def api_create_ontology():
    """创建新本体费用API"""
    try:
        data = request.get_json()

        # 验证数据
        if not data:
            return jsonify({
                'success': False,
                'message': '请提供费用数据'
            }), 400

        # 验证必填字段
        required_fields = ['费用序号', '费用名称', '计算逻辑', '费用关系']
        for field in required_fields:
            if field not in data or not str(data[field]).strip():
                return jsonify({
                    'success': False,
                    'message': f'{field}不能为空'
                }), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查费用序号是否已存在
        cursor.execute("SELECT COUNT(*) FROM t_benti_ontology WHERE 费用序号 = ?", (data['费用序号'],))
        if cursor.fetchone()[0] > 0:
            conn.close()
            return jsonify({
                'success': False,
                'message': '费用序号已存在，请使用其他序号'
            }), 400

        # 插入新记录
        cursor.execute("""
            INSERT INTO t_benti_ontology (费用序号, 费用名称, 计算逻辑, 费用关系, 创建时间, 更新时间)
            VALUES (?, ?, ?, ?, datetime('now', 'localtime'), datetime('now', 'localtime'))
        """, (data['费用序号'], data['费用名称'], data['计算逻辑'], data['费用关系']))

        conn.commit()
        new_id = cursor.lastrowid
        conn.close()

        return jsonify({
            'success': True,
            'data': {'id': new_id},
            'message': '创建本体费用成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'创建本体费用失败: {str(e)}'
        }), 500

@bp.route('/api/indicator_rule/ontology/<int:ontology_id>', methods=['PUT'])
def api_update_ontology(ontology_id):
    """更新本体费用API"""
    try:
        data = request.get_json()

        # 验证数据
        if not data:
            return jsonify({
                'success': False,
                'message': '请提供更新数据'
            }), 400

        # 验证必填字段
        required_fields = ['费用序号', '费用名称', '计算逻辑', '费用关系']
        for field in required_fields:
            if field not in data or not str(data[field]).strip():
                return jsonify({
                    'success': False,
                    'message': f'{field}不能为空'
                }), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查记录是否存在
        cursor.execute("SELECT COUNT(*) FROM t_benti_ontology WHERE id = ?", (ontology_id,))
        if cursor.fetchone()[0] == 0:
            conn.close()
            return jsonify({
                'success': False,
                'message': '本体费用不存在'
            }), 404

        # 检查费用序号是否被其他记录使用
        cursor.execute("SELECT COUNT(*) FROM t_benti_ontology WHERE 费用序号 = ? AND id != ?", (data['费用序号'], ontology_id))
        if cursor.fetchone()[0] > 0:
            conn.close()
            return jsonify({
                'success': False,
                'message': '费用序号已被其他记录使用，请使用其他序号'
            }), 400

        # 更新记录
        cursor.execute("""
            UPDATE t_benti_ontology
            SET 费用序号 = ?, 费用名称 = ?, 计算逻辑 = ?, 费用关系 = ?, 更新时间 = datetime('now', 'localtime')
            WHERE id = ?
        """, (data['费用序号'], data['费用名称'], data['计算逻辑'], data['费用关系'], ontology_id))

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': '更新本体费用成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'更新本体费用失败: {str(e)}'
        }), 500

@bp.route('/api/indicator_rule/ontology/<int:ontology_id>', methods=['DELETE'])
def api_delete_ontology(ontology_id):
    """删除本体费用API"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查记录是否存在
        cursor.execute("SELECT COUNT(*) FROM t_benti_ontology WHERE id = ?", (ontology_id,))
        if cursor.fetchone()[0] == 0:
            conn.close()
            return jsonify({
                'success': False,
                'message': '本体费用不存在'
            }), 404

        # 删除记录
        cursor.execute("DELETE FROM t_benti_ontology WHERE id = ?", (ontology_id,))
        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': '删除本体费用成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'删除本体费用失败: {str(e)}'
        }), 500
