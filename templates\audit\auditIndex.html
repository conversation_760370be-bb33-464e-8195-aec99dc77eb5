{% extends "base.html" %}

{% block title %}工程管理 - 电网线路工程造价分析平台{% endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/audit.css') }}">
<style>
    /* 覆盖全局容器样式 */
    main {
        padding: 0;
        min-height: calc(100vh - 120px);
    }

    main .container {
        max-width: none;
        padding: 0;
        height: 100%;
    }
</style>
{% endblock %}

{% block content %}
<!-- 工程列表 -->
<section class="project-section">
    <!-- 查询条件 -->
    <form class="query-form">
        <div class="form-group">
            <label>工程名称</label>
            <input type="text" placeholder="请输入工程名称">
        </div>
        <div class="form-group">
            <label>电压等级</label>
            <select>
                <option value="">全部</option>
                <option value="500kV">500kV</option>
                <option value="220kV">220kV</option>
                <option value="110kV">110kV</option>
            </select>
        </div>
        <div class="form-group">
            <label>线路总长度(km)</label>
            <input type="number" placeholder="请输入线路长度">
        </div>
        <div class="form-group">
            <label>回路数</label>
            <select>
                <option value="">全部</option>
                <option value="单回路">单回路</option>
                <option value="双回路">双回路</option>
                <option value="四回路">四回路</option>
            </select>
        </div>
        <div class="form-group">
            <label>风速(m/s)</label>
            <input type="number" placeholder="请输入风速">
        </div>
        <div class="form-group">
            <label>覆冰(mm)</label>
            <input type="number" placeholder="请输入覆冰">
        </div>
        <div class="form-group">
            <label>导线规格</label>
            <input type="text" placeholder="请输入导线规格">
        </div>
        <div class="button-group">
            <button type="button" class="btn btn-primary">查询</button>
            <button type="reset" class="btn btn-secondary">重置</button>
            <button type="button" class="btn btn-primary" onclick="showAddProjectModal()">添加线路工程</button>
        </div>
    </form>

    <div class="table-container">
        <table class="project-table">
            <thead>
                <tr>
                    <th style="display: none;">序号</th>
                    <th>工程名称</th>
                    <th>线路段名称</th>
                    <th>电压等级</th>
                    <th>线路总长度</th>
                    <th>回路数</th>
                    <th>风速</th>
                    <th>覆冰</th>
                    <th>导线规格</th>
                    <th>导入状态</th>
                    <th>提取状态</th>
                    <th>校审状态</th>
                    <th>创建时间</th>
                    <th>校审时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <!-- 项目数据将通过JavaScript动态填充 -->
            </tbody>
        </table>
    </div>

    <div class="pagination">
        <button class="active">1</button>
        <button>2</button>
        <button>3</button>
        <button>4</button>
        <button>5</button>
    </div>
</section>

<!-- 新增线路工程对话框 -->
<div id="addProjectModal" class="indicator-modal">
    <div class="indicator-content" style="max-width: 800px;">
        <span class="close" onclick="closeAddProjectModal()">&times;</span>
        <h2>新增线路工程</h2>
        <form id="addProjectForm" class="add-project-form">
            <div class="form-row">
                <div class="form-group">
                    <label>工程名称</label>
                    <input type="text" value="500kV东莞西南部受电通道工程" readonly>
                </div>
                <div class="form-group">
                    <label>线路段名称</label>
                    <input type="text" value="500kV博罗~莞城双回线路-惠州段（25m风区）" readonly>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>电压等级</label>
                    <select disabled>
                        <option value="500kV" selected>500kV</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>线路总长度</label>
                    <input type="number" value="22.3" readonly>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>回路数</label>
                    <select disabled>
                        <option value="双回路" selected>双回路</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>风速</label>
                    <input type="number" value="25" readonly>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>覆冰</label>
                    <input type="number" value="10" readonly>
                </div>
                <div class="form-group">
                    <label>导线规格</label>
                    <input type="text" value="JL/LB20A 630/45" readonly>
                </div>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-primary" onclick="saveNewProject()">保存</button>
                <button type="button" class="btn btn-secondary" onclick="closeAddProjectModal()">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 文件上传对话框 -->
<div id="uploadModal" class="indicator-modal">
    <div class="indicator-content">
        <span class="close" onclick="closeUploadModal()">&times;</span>
        <h2>概算文件导入</h2>
        <div class="upload-area" id="uploadArea">
            <input type="file" id="fileInput" accept=".xlsx,.xls" style="display: none;">
            <div class="upload-box" onclick="triggerFileInput()">
                <i class="fas fa-cloud-upload-alt"></i>
                <p>点击或拖拽工程概算Excel文件到此处，完成文件上传</p>
            </div>
        </div>
        <div id="uploadProgress" style="display: none; text-align: center;">
            <p class="success-message" style="color: #52c41a; font-size: 16px;"><i class="fas fa-check-circle"></i> 文件上传成功！</p>
        </div>
    </div>
</div>

{% include 'audit/indicatorExtract.html' %}
{% include 'audit/indicatorReview.html' %}

<script>
// 加载项目数据
async function loadProjectData() {
    try {
        const response = await fetch('/api/projects');
        const data = await response.json();
        renderProjectTable(data);
    } catch (error) {
        console.error('加载项目数据失败:', error);
    }
}

// 更新项目状态
async function updateProjectStatus(projectId, updates) {
    try {
        const response = await fetch(`/api/projects/${projectId}`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(updates)
        });
        
        if (response.ok) {
            // 重新加载数据
            await loadProjectData();
            return true;
        }
        return false;
    } catch (error) {
        console.error('更新项目状态失败:', error);
        return false;
    }
}

// 渲染项目表格
function renderProjectTable(projects) {
    const tbody = document.querySelector('.project-table tbody');
    tbody.innerHTML = '';
    
    projects.forEach(project => {
        const tr = document.createElement('tr');
        tr.setAttribute('data-project-id', project.序号);
        tr.innerHTML = `
            <td style="display: none;">${project.序号}</td>
            <td>${project.工程名称}</td>
            <td>${project.线路段名称}</td>
            <td>${project.电压等级}</td>
            <td>${project['线路总长度']}</td>
            <td>${project.回路数}</td>
            <td>${project.风速}</td>
            <td>${project.覆冰}</td>
            <td>${project.导线规格}</td>
            <td><span class="status-tag status-${project.导入状态 === '已导入' ? 'success' : 'default'}">${project.导入状态}</span></td>
            <td><span class="status-tag status-${project.提取状态 === '已提取' ? 'success' : project.提取状态 === '未提取' ? 'default' : 'warning'}">${project.提取状态}</span></td>
            <td><span class="status-tag status-${project.校审状态 === '已校审' ? 'success' : 'default'}">${project.校审状态}</span></td>
            <td>${project.创建时间 || ''}</td>
            <td>${project.校审时间 || ''}</td>
            <td class="action-buttons">
                <button class="btn ${project.导入状态 === '已导入' ? 'btn-success' : 'btn-primary'}" onclick="showUploadModal(${project.序号})">${project.导入状态 === '已导入' ? '重新导入' : '文件导入'}</button>
                ${project.提取状态 === '已提取' ? 
                    `<button class="btn btn-success" onclick="showIndicatorPreview(${project.序号})">指标预览</button>` :
                    `<button class="btn btn-primary ${project.导入状态 !== '已导入' ? 'btn-disabled' : ''}" 
                        onclick="${project.导入状态 === '已导入' ? 
                        `showExtractIndicatorModal(${project.序号})` : 
                        'showImportWarning()'}">指标提取</button>`
                }
                ${project.校审状态 === '已校审' ? 
                    `<button class="btn btn-success" onclick="showReviewPreview(${project.序号})">校审预览</button>` :
                    `<button class="btn btn-primary ${getAuditButtonDisabledClass(project)}" 
                        onclick="${getAuditButtonOnClick(project)}">指标校审</button>`
                }
                <button class="btn btn-danger" onclick="showDeleteConfirm(${project.序号})">删除</button>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

// 获取校审按钮的禁用类
function getAuditButtonDisabledClass(project) {
    if (project.导入状态 !== '已导入' || project.提取状态 !== '已提取') {
        return 'btn-disabled';
    }
    return '';
}

// 获取校审按钮的点击事件
function getAuditButtonOnClick(project) {
    if (project.导入状态 !== '已导入') {
        return 'showImportWarning()';
    }
    if (project.提取状态 !== '已提取') {
        return 'showExtractWarning()';
    }
    return `showReviewIndicatorModal(${project.序号})`;
}

// 添加导入提示函数
function showImportWarning() {
    alert('概算文件未导入，请先导入概算文件');
}

// 添加提取提示函数
function showExtractWarning() {
    alert('指标数据未提取，请先进行指标提取');
}

// 页面加载完成后加载数据
document.addEventListener('DOMContentLoaded', () => {
    loadProjectData();
    initializeFileUpload(); // 初始化文件上传功能
});

// 新增线路工程相关函数
function showAddProjectModal() {
    const modal = document.getElementById('addProjectModal');
    modal.classList.add('show');
}

function closeAddProjectModal() {
    const modal = document.getElementById('addProjectModal');
    modal.classList.remove('show');
}

async function saveNewProject() {
    try {
        const currentTime = new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        }).replace(/\//g, '-');

        const response = await fetch('/api/projects', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                工程名称: "500kV东莞西南部受电通道工程",
                线路段名称: "500kV博罗-莞城双回线路-惠州段（25m风区）",
                电压等级: "500kV",
                线路总长度: 22.3,
                回路数: "双回路",
                风速: 25,
                覆冰: 10,
                导线规格: "JL/LB20A 630/45",
                导入状态: "未导入",
                提取状态: "未提取",
                校审状态: "未校审",
                校审时间: "",
                创建时间: currentTime
            })
        });
        
        if (response.ok) {
            // 重新加载数据
            await loadProjectData();
            // 关闭对话框
            closeAddProjectModal();
            // 显示成功提示
            showToast('添加线路工程成功');
        }
    } catch (error) {
        console.error('添加新项目失败:', error);
        showToast('添加线路工程失败', 'error');
    }
}

// Toast 提示函数
function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;
    document.body.appendChild(toast);
    
    // 2秒后自动消失
    setTimeout(() => {
        toast.remove();
    }, 2000);
}

// 触发文件选择框
function triggerFileInput() {
    document.getElementById('fileInput').click();
}

// 文件上传相关函数
function showUploadModal(projectId) {
    const modal = document.getElementById('uploadModal');
    const fileInput = document.getElementById('fileInput');
    modal.dataset.projectId = projectId;
    
    // 新需求2：检查是否为重新导入，如果是则显示确认提示
    const isReimport = document.querySelector(`tr[data-project-id="${projectId}"] .btn-success[onclick*="showUploadModal"]`) !== null;
    if (isReimport) {
        if (!confirm('重新导入，历史提取和校审数据将会被覆盖，请确认是否继续导入？如需下载历史数据，可关闭页面进入相关页面下载。')) {
            return; // 用户取消导入
        }
    }
    
    modal.classList.add('show');
    document.getElementById('uploadProgress').style.display = 'none';
    fileInput.value = ''; // 清空文件输入
}

function closeUploadModal() {
    const modal = document.getElementById('uploadModal');
    modal.classList.remove('show');
}

// 初始化文件上传相关事件
function initializeFileUpload() {
    const fileInput = document.getElementById('fileInput');
    const uploadArea = document.getElementById('uploadArea');
    const uploadBox = uploadArea.querySelector('.upload-box');

    // 文件选择变化处理
    fileInput.addEventListener('change', async function(e) {
        if (e.target.files.length > 0) {
            const modal = document.getElementById('uploadModal');
            const projectId = modal.dataset.projectId;
            
            // 恢复原始代码：检测是否为重新导入
            const isReimport = document.querySelector(`tr[data-project-id="${projectId}"] .btn-success[onclick*="showUploadModal"]`) !== null;
            
            try {
                // 显示上传成功消息
                document.getElementById('uploadProgress').style.display = 'block';
                
                // 更新项目状态
                const updates = {
                    导入状态: '已导入',
                    提取状态: '未提取',
                    校审状态: '未校审',
                    校审时间: ''  // 清空校审时间
                };
                
                const success = await updateProjectStatus(projectId, updates);
                
                if (success) {
                    // 恢复原始代码：根据是否重新导入显示不同的提示
                    showToast(isReimport ? 
                       '概算文件已重新导入，请重新进行指标提取和校审' : 
                       '文件上传成功');
                    
                    // 3秒后自动关闭
                    setTimeout(() => {
                        closeUploadModal();
                    }, 3000);
                } else {
                    throw new Error('更新项目状态失败');
                }
            } catch (error) {
                console.error('文件上传或状态更新失败:', error);
                document.getElementById('uploadProgress').style.display = 'none';
                showToast('文件上传失败，请重试', 'error');
            }
        }
    });

    // 拖拽相关事件
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        e.stopPropagation();
        uploadBox.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', (e) => {
        e.preventDefault();
        e.stopPropagation();
        uploadBox.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        e.stopPropagation();
        uploadBox.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            fileInput.dispatchEvent(new Event('change'));
        }
    });
}

// 删除确认函数
function showDeleteConfirm(projectId) {
    if (confirm('确认要删除此工程吗？')) {
        // 演示效果：直接从DOM中移除该行
        const row = document.querySelector(`tr[data-project-id="${projectId}"]`);
        if (row) {
            row.remove();
            showToast('工程删除成功');
        }
    }
}
</script>
{% endblock %} 