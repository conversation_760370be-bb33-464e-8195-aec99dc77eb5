<!-- 校审预览对话框 -->
<div id="reviewPreviewModal" class="indicator-modal">
    <div class="indicator-content">
        <span class="close" onclick="closeModal('reviewPreviewModal')">&times;</span>
        <h2>校审结果预览</h2>
        <div class="preview-actions" style="margin-bottom: 15px;">
            <button id="exportReviewBtn" class="btn btn-success" onclick="exportReview()">导出</button>
        </div>
        <div class="review-tabs">
            <button class="review-tab active" onclick="switchTab('review', 'summary')">校审概述</button>
            <button class="review-tab" onclick="switchTab('review', 'benti')">本体费用指标</button>
            <button class="review-tab" onclick="switchTab('review', 'qita')">其他费用指标</button>
        </div>
        <div id="reviewSummaryPanel" class="review-panel active">
            <div style="color: #d32f2f; margin-bottom: 10px; font-size: 16px;">
                共校审了 <strong>44</strong> 个本体费用指标，<strong>26</strong> 个其他费用指标。校审情况如下：
            </div>
            <div class="indicator-table-wrapper">
                <table class="indicator-table review-table">
                    <thead>
                        <tr>
                            <th>指标类别</th>
                            <th>校审状态</th>
                            <th>指标数据状态</th>
                            <th>个数</th>
                            <th>占比</th>
                        </tr>
                    </thead>
                    <tbody id="reviewSummaryTableBody">
                        <!-- 由JS动态填充 -->
                    </tbody>
                </table>
            </div>
        </div>
        <div id="reviewBentiPanel" class="review-panel">
            <div class="indicator-table-wrapper">
                <table class="indicator-table review-table">
                    <thead>
                        <tr>
                            <th style="text-align: center;">序号</th>
                            <th style="text-align: center;">本体费用指标名称</th>
                            <th style="text-align: center;">单位</th>
                            <th style="text-align: center;">指标值</th>
                            <th style="text-align: center;">校审结果</th>
                            <th style="text-align: center;">校审明细</th>
                        </tr>
                    </thead>
                    <tbody id="reviewBentiTableBody">
                        <!-- 将通过JavaScript动态填充 -->
                    </tbody>
                </table>
            </div>
        </div>
        <div id="reviewQitaPanel" class="review-panel">
            <div class="indicator-table-wrapper">
                <table class="indicator-table review-table">
                    <thead>
                        <tr>
                            <th style="text-align: center;">序号</th>
                            <th style="text-align: center;">其他费用指标名称</th>
                            <th style="text-align: center;">单位</th>
                            <th style="text-align: center;">指标值</th>
                            <th style="text-align: center;">校审结果</th>
                            <th style="text-align: center;">校审明细</th>
                        </tr>
                    </thead>
                    <tbody id="reviewQitaTableBody">
                        <!-- 将通过JavaScript动态填充 -->
                    </tbody>
                </table>
            </div>
        </div>
        <div style="margin-top: 20px; text-align: right;">
            <button class="btn btn-secondary" onclick="closeModal('reviewPreviewModal')">关闭</button>
        </div>
    </div>
</div>

<!-- 校审进度对话框 -->
<div id="reviewProgressModal" class="indicator-modal">
    <div class="indicator-content">
        <span class="close" onclick="closeProgressModal('reviewProgressModal')">&times;</span>
        <h2 id="reviewProgressTitle">指标校审进度</h2>
        <div class="progress-content">
            <div id="reviewProgressMessages" class="progress-messages"></div>
        </div>
        <div class="modal-footer" style="margin-top: 20px; text-align: right;">
            <button id="reviewPreviewButton" class="btn btn-primary" style="display: none; margin-right: 10px;" onclick="handleReviewPreviewClick()">校审预览</button>
            <button class="btn btn-secondary" onclick="closeProgressModal('reviewProgressModal')">关闭</button>
        </div>
    </div>
</div>

<script>
// 校审相关函数
async function showReviewIndicatorModal(projectId) {
    const modal = document.getElementById('reviewProgressModal');
    const title = document.getElementById('reviewProgressTitle');
    const messagesContainer = document.getElementById('reviewProgressMessages');
    const previewButton = document.getElementById('reviewPreviewButton');
    
    // 清空之前的消息
    messagesContainer.innerHTML = '';
    
    // 隐藏预览按钮
    previewButton.style.display = 'none';
    
    // 设置标题
    title.textContent = '指标校审进度';
    
    // 显示对话框
    modal.classList.add('show');

    // 指标校审流程
    addProgressMessage(messagesContainer, '指标校审开始');
    
    // 本体费用指标校审
    addProgressMessage(messagesContainer, '本体费用指标开始校审');
    for (const indicator of bentiIndicators) {
        await new Promise(resolve => setTimeout(resolve, 100));
        addProgressMessage(messagesContainer, `校审本体费用指标：${indicator.name}，校审完成`);
    }
    addProgressMessage(messagesContainer, '本体费用指标校审完成');
    
    // 其他费用指标校审
    addProgressMessage(messagesContainer, '其他费用指标开始校审');
    for (const indicator of qitaIndicators) {
        await new Promise(resolve => setTimeout(resolve, 100));
        addProgressMessage(messagesContainer, `校审其他费用指标：${indicator.name}，校审完成`);
    }
    addProgressMessage(messagesContainer, '其他费用指标校审完成');
    
    try {
        const response = await fetch(`/api/projects/${projectId}/review`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            const result = await response.json();
            if (result.review_data) {
                // 保存校审数据用于预览
                modal.dataset.reviewData = JSON.stringify(result.review_data);
            }
            previewButton.textContent = '校审预览';
            previewButton.style.display = 'inline-block';
            await loadProjectData();
            modal.dataset.projectId = projectId;
            addProgressMessage(messagesContainer, '所有指标校审完成，请点击"校审预览"查看校审结果');
        } else {
            console.error('更新校审状态失败');
        }
    } catch (error) {
        console.error('调用API失败:', error);
    }
}

// 校审预览相关函数
async function showReviewPreview(projectId, reviewData = null) {
    const modal = document.getElementById('reviewPreviewModal');
    
    // 显示对话框
    modal.classList.add('show');
    
    try {
        // 如果没有传入reviewData，则从API获取
        if (!reviewData) {
            const [bentiReview, qitaReview] = await Promise.all([
                fetch(`/api/review/benti/${projectId}`).then(res => res.json()),
                fetch(`/api/review/qita/${projectId}`).then(res => res.json())
            ]);
            reviewData = {
                benti: bentiReview,
                qita: qitaReview
            };
        }
        
        const reviewBentiPanel = document.getElementById('reviewBentiPanel');
        const reviewQitaPanel = document.getElementById('reviewQitaPanel');
        
        // 生成本体费用指标校审预览表格
        reviewBentiPanel.innerHTML = generateReviewTable(bentiIndicators, '本体费用', reviewData.benti);
        
        // 生成其他费用指标校审预览表格
        reviewQitaPanel.innerHTML = generateReviewTable(qitaIndicators, '其他费用', reviewData.qita);
        
        // 默认显示校审概述面板
        switchTab('review', 'summary');
        
    } catch (error) {
        console.error('加载校审预览数据失败:', error);
    }
}

// 生成校审预览表格的辅助函数
function generateReviewTable(indicators, type, reviewData) {
    return `
        <div class="indicator-table-wrapper">
            <table class="indicator-table review-table">
                <thead>
                    <tr>
                        <th style="text-align: center;">序号</th>
                        <th>${type}指标名称</th>
                        <th style="text-align: center;">单位</th>
                        <th style="text-align: right;">指标值</th>
                        <th style="text-align: center;">校审结果</th>
                        <th>校审明细</th>
                    </tr>
                </thead>
                <tbody>
                    ${indicators.map((indicator, index) => {
                        // 构建JSON字段名
                        let fieldName;
                        if (type === '本体费用') {
                            fieldName = `${indicator.name}(${indicator.unit})`;
                            if (!reviewData[fieldName]) {
                                fieldName = `${indicator.name}（${indicator.unit}）`;
                            }
                        } else {
                            fieldName = `${indicator.name}（${indicator.unit}）`;
                        }
                        
                        // 获取对应的校审数据
                        const reviewInfo = reviewData[fieldName] || {
                            value: '--',
                            status: '--',
                            detail: '--'
                        };
                        
                        const statusClass = reviewInfo.status === '异常' ? 'review-abnormal' : 
                                        reviewInfo.status === '正常' ? 'review-normal' : '';
                        
                        return `
                            <tr>
                                <td style="text-align: center;">${index + 1}</td>
                                <td>${indicator.name}</td>
                                <td style="text-align: center;">${indicator.unit}</td>
                                <td style="text-align: right;">${reviewInfo.value}</td>
                                <td style="text-align: center;"><span class="${statusClass}">${reviewInfo.status}</span></td>
                                <td>${reviewInfo.detail}</td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        </div>
    `;
}

// 校审概述演示数据
const summaryDemoData = [
    ['本体费用指标', '校审成功', '指标数据正常', 40, '90.90%'],
    ['本体费用指标', '校审成功', '指标数据异常', 2, '4.55%'],
    ['本体费用指标', '校审失败', '/', 2, '4.55%'],
    ['其他费用指标', '校审成功', '指标数据正常', 23, '88.46%'],
    ['其他费用指标', '校审成功', '指标数据异常', 2, '7.69%'],
    ['其他费用指标', '校审失败', '/', 1, '3.85%'],
];

// 页面加载时渲染校审概述表格
document.addEventListener('DOMContentLoaded', function() {
    renderSummaryTable();
});

function renderSummaryTable() {
    const tbody = document.getElementById('reviewSummaryTableBody');
    if (!tbody) return;
    tbody.innerHTML = summaryDemoData.map(row =>
        `<tr>${row.map(cell => `<td>${cell}</td>`).join('')}</tr>`
    ).join('');
}

// 导出校审报告
function exportReview() {
    // 获取当前projectId
    const modal = document.getElementById('reviewProgressModal');
    let projectId = null;
    if (modal && modal.dataset.projectId) {
        projectId = modal.dataset.projectId;
    } else {
        // 尝试从预览modal获取
        const previewModal = document.getElementById('reviewPreviewModal');
        if (previewModal && previewModal.dataset.projectId) {
            projectId = previewModal.dataset.projectId;
        } else {
            // 默认1
            projectId = 1;
        }
    }
    fetch(`/api/review/export/${projectId}`)
        .then(response => response.blob())
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `xxxx线路工程指标自校单_${projectId}.docx`;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);
        });
}

// 处理校审预览按钮点击事件
function handleReviewPreviewClick() {
    const modal = document.getElementById('reviewProgressModal');
    const projectId = modal.dataset.projectId;
    const reviewData = modal.dataset.reviewData ? JSON.parse(modal.dataset.reviewData) : null;
    if (projectId) {
        closeProgressModal('reviewProgressModal'); // 先关闭进度框
        showReviewPreview(projectId, reviewData); // 再显示校审预览框
    }
}

// 关闭进度对话框
function closeProgressModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.remove('show');
}

// 切换tab页
function switchTab(type, tabName) {
    // 获取所有tab按钮和面板
    const tabs = document.querySelectorAll(`.${type}-tabs button`);
    const panels = document.querySelectorAll(`.${type}-panel`);
    
    // 移除所有active类
    tabs.forEach(tab => tab.classList.remove('active'));
    panels.forEach(panel => panel.classList.remove('active'));
    
    // 激活选中的tab
    const selectedTab = document.querySelector(`.${type}-tab[onclick*="${type}', '${tabName}"]`);
    const selectedPanel = document.getElementById(`${type}${tabName.charAt(0).toUpperCase() + tabName.slice(1)}Panel`);
    
    if (selectedTab) selectedTab.classList.add('active');
    if (selectedPanel) selectedPanel.classList.add('active');
}
</script>