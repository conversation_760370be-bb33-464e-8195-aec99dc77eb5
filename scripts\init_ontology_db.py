#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本体费用计算逻辑数据库初始化脚本
创建 t_benti_ontology 表并插入初始化数据
"""

import os
import sqlite3
from datetime import datetime

def create_ontology_table():
    """创建本体费用计算逻辑表"""
    db_path = os.path.join('data', 'indicator_management.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        print("请先运行 init_indicator_db.py 创建数据库")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建本体费用计算逻辑表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS t_benti_ontology (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            费用序号 INTEGER NOT NULL,
            费用名称 TEXT NOT NULL,
            计算逻辑 TEXT NOT NULL,
            费用关系 TEXT NOT NULL,
            创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
            更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 创建索引提高查询性能
    cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_benti_ontology_xuhao ON t_benti_ontology(费用序号)
    ''')
    
    cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_benti_ontology_name ON t_benti_ontology(费用名称)
    ''')
    
    conn.commit()
    conn.close()
    print(f"本体费用计算逻辑表创建成功: {db_path}")
    return True

def insert_initial_data():
    """插入初始化数据"""
    # 初始化数据
    initial_data = [
        (1, '本体工程', '/', '/'),
        (2, '价差', '本体*20%', '1*20%'),
        (3, '其他', '本体*25%', '1*25%'),
        (4, '其中：建设场地征用及清理费', '其他*35%', '3*35%'),
        (5, '基本预备费', '（本体+价差+其他）*1.5%', '(1+2+3)*1.5%'),
        (6, '工程静态投资', '本体+价差+其他+基本预备费', '1+2+3+5'),
        (7, '建设期贷款利息', '工程静态投资*3.55%*0.5*0.8', '6*3.55%*0.5*0.8'),
        (8, '工程动态投资', '工程静态投资+建设期贷款利息', '6+7')
    ]
    
    db_path = os.path.join('data', 'indicator_management.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 清空现有数据
    cursor.execute('DELETE FROM t_benti_ontology')
    
    # 插入数据
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    for 费用序号, 费用名称, 计算逻辑, 费用关系 in initial_data:
        cursor.execute('''
            INSERT INTO t_benti_ontology (
                费用序号, 费用名称, 计算逻辑, 费用关系, 创建时间, 更新时间
            ) VALUES (?, ?, ?, ?, ?, ?)
        ''', (费用序号, 费用名称, 计算逻辑, 费用关系, current_time, current_time))

        print(f"插入数据: {费用序号:2d} | {费用名称:25s} | {计算逻辑:30s} | {费用关系}")
    
    conn.commit()
    conn.close()
    print(f"\n本体费用计算逻辑数据插入成功，共 {len(initial_data)} 条记录")

def verify_data():
    """验证插入的数据"""
    db_path = os.path.join('data', 'indicator_management.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 查询数据统计
    cursor.execute('SELECT COUNT(*) FROM t_benti_ontology')
    count = cursor.fetchone()[0]
    
    print(f"\n数据验证结果:")
    print(f"总记录数: {count}")
    
    # 显示所有记录
    cursor.execute('''
        SELECT 费用序号, 费用名称, 计算逻辑, 费用关系, 创建时间
        FROM t_benti_ontology
        ORDER BY 费用序号
    ''')
    
    records = cursor.fetchall()
    print(f"\n所有记录:")
    print(f"{'序号':<4} {'费用名称':<25} {'计算逻辑':<30} {'费用关系':<20} {'创建时间'}")
    print("-" * 100)
    
    for record in records:
        费用序号, 费用名称, 计算逻辑, 费用关系, 创建时间 = record
        print(f"{费用序号:<4} {费用名称:<25} {计算逻辑:<30} {费用关系:<20} {创建时间}")
    
    conn.close()

def main():
    """主函数"""
    print("=" * 60)
    print("本体费用计算逻辑数据库初始化")
    print("=" * 60)
    
    try:
        # 1. 创建表
        print("\n1. 创建数据库表...")
        if not create_ontology_table():
            return
        
        # 2. 插入初始化数据
        print("\n2. 插入初始化数据...")
        insert_initial_data()
        
        # 3. 验证数据
        print("\n3. 验证数据...")
        verify_data()
        
        print("\n" + "=" * 60)
        print("本体费用计算逻辑数据库初始化完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n初始化过程中发生错误: {e}")
        return False
    
    return True

if __name__ == '__main__':
    main()
