#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看审计项目数据工具
用于查看 t_audit_projects 表中的数据
"""

import os
import sqlite3
from datetime import datetime

def view_audit_projects(limit=10):
    """查看审计项目数据"""
    db_path = os.path.join('data', 'te_manage.db')
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='t_audit_projects'")
        if not cursor.fetchone():
            print("❌ t_audit_projects 表不存在")
            return False
        
        # 获取总记录数
        cursor.execute('SELECT COUNT(*) FROM t_audit_projects')
        total_count = cursor.fetchone()[0]
        
        if total_count == 0:
            print("📋 t_audit_projects 表为空")
            return True
        
        print(f"📋 审计项目数据 (共 {total_count} 条记录)")
        print("=" * 120)
        
        # 查询数据
        cursor.execute(f'''
            SELECT 序号, project_id, 工程名称, 线路段名称, 电压等级, 线路总长度,
                   回路数, 风速, 覆冰, 导线规格, 导入状态, 提取状态, 校审状态,
                   气象区, 创建时间, 校审时间
            FROM t_audit_projects 
            ORDER BY 序号 
            LIMIT {limit}
        ''')
        
        rows = cursor.fetchall()
        
        # 显示表头
        headers = ['序号', 'ID', '工程名称', '线路段名称', '电压', '长度', '回路', '风速', '覆冰', 
                  '导线规格', '导入', '提取', '校审', '气象区', '创建时间', '校审时间']
        
        # 计算列宽
        col_widths = [4, 4, 20, 25, 6, 6, 6, 4, 4, 15, 6, 6, 6, 6, 16, 16]
        
        # 打印表头
        header_line = " | ".join(h.ljust(w) for h, w in zip(headers, col_widths))
        print(header_line)
        print("-" * len(header_line))
        
        # 打印数据行
        for row in rows:
            formatted_row = []
            for i, (value, width) in enumerate(zip(row, col_widths)):
                if value is None:
                    formatted_value = ""
                elif i in [5, 7, 8]:  # 数值列
                    formatted_value = str(value) if value is not None else ""
                else:
                    formatted_value = str(value)
                
                # 截断过长的文本
                if len(formatted_value) > width:
                    formatted_value = formatted_value[:width-3] + "..."
                
                formatted_row.append(formatted_value.ljust(width))
            
            print(" | ".join(formatted_row))
        
        if total_count > limit:
            print(f"\n... 还有 {total_count - limit} 条记录")
            print(f"使用 view_audit_projects({total_count}) 查看全部记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 查看数据失败: {e}")
        return False
        
    finally:
        if 'conn' in locals():
            conn.close()

def view_table_schema():
    """查看表结构"""
    db_path = os.path.join('data', 'te_manage.db')
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取表结构
        cursor.execute("PRAGMA table_info(t_audit_projects)")
        columns = cursor.fetchall()
        
        if not columns:
            print("❌ t_audit_projects 表不存在")
            return False
        
        print("📋 t_audit_projects 表结构:")
        print("=" * 60)
        print(f"{'列名':<20} {'类型':<15} {'非空':<6} {'默认值':<15}")
        print("-" * 60)
        
        for col in columns:
            cid, name, type_name, notnull, default_value, pk = col
            notnull_str = "是" if notnull else "否"
            default_str = str(default_value) if default_value is not None else ""
            print(f"{name:<20} {type_name:<15} {notnull_str:<6} {default_str:<15}")
        
        return True
        
    except Exception as e:
        print(f"❌ 查看表结构失败: {e}")
        return False
        
    finally:
        if 'conn' in locals():
            conn.close()

def view_statistics():
    """查看数据统计"""
    db_path = os.path.join('data', 'te_manage.db')
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("📊 数据统计:")
        print("=" * 40)
        
        # 总记录数
        cursor.execute('SELECT COUNT(*) FROM t_audit_projects')
        total = cursor.fetchone()[0]
        print(f"总记录数: {total}")
        
        if total == 0:
            return True
        
        # 按状态统计
        print("\n按状态统计:")
        for status_col in ['导入状态', '提取状态', '校审状态']:
            cursor.execute(f'SELECT {status_col}, COUNT(*) FROM t_audit_projects GROUP BY {status_col}')
            results = cursor.fetchall()
            print(f"  {status_col}:")
            for status, count in results:
                print(f"    {status}: {count}")
        
        # 按气象区统计
        cursor.execute('SELECT 气象区, COUNT(*) FROM t_audit_projects GROUP BY 气象区')
        results = cursor.fetchall()
        print(f"\n按气象区统计:")
        for weather_zone, count in results:
            print(f"  {weather_zone}: {count}")
        
        # 按电压等级统计
        cursor.execute('SELECT 电压等级, COUNT(*) FROM t_audit_projects GROUP BY 电压等级')
        results = cursor.fetchall()
        print(f"\n按电压等级统计:")
        for voltage, count in results:
            print(f"  {voltage}: {count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 查看统计失败: {e}")
        return False
        
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == '__main__':
    print("🔍 审计项目数据查看工具")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 查看数据 (前10条)")
        print("2. 查看所有数据")
        print("3. 查看表结构")
        print("4. 查看数据统计")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-4): ").strip()
        
        if choice == '1':
            view_audit_projects(10)
        elif choice == '2':
            view_audit_projects(1000)  # 显示更多记录
        elif choice == '3':
            view_table_schema()
        elif choice == '4':
            view_statistics()
        elif choice == '0':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")
