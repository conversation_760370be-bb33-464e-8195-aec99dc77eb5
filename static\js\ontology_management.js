/**
 * 本体费用计算逻辑管理模块 JavaScript
 * 负责本体费用数据的增删改查、分页、搜索等功能
 */

// ==================== 模块配置和状态管理 ====================

/**
 * 本体费用管理模块配置
 */
const OntologyManagement = {
    // 分页状态管理
    pagination: {
        currentPage: 1,
        pageSize: 10,
        totalRecords: 0,
        totalPages: 0
    },

    // API 端点配置
    api: {
        list: '/api/indicator_rule/ontology/list',
        detail: '/api/indicator_rule/ontology',
        create: '/api/indicator_rule/ontology',
        update: '/api/indicator_rule/ontology',
        delete: '/api/indicator_rule/ontology'
    },

    // DOM元素缓存
    elements: {}
};

// ==================== 初始化功能 ====================

/**
 * 初始化本体费用管理页面
 */
async function initOntologyManagement() {
    console.log('初始化本体费用管理模块...');
    
    try {
        // 缓存DOM元素
        cacheOntologyElements();
        
        // 延迟一小段时间确保DOM完全准备好
        await new Promise(resolve => setTimeout(resolve, 100));

        // 加载默认数据
        console.log('开始加载本体费用默认数据...');
        await loadOntologyDataWithRetry();

        console.log('本体费用管理模块初始化完成');
    } catch (error) {
        console.error('本体费用管理模块初始化失败:', error);
        showOntologyGlobalErrorMessage('页面初始化失败，请刷新页面重试');
    }
}

/**
 * 带重试机制的本体费用数据加载
 */
async function loadOntologyDataWithRetry(maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            console.log(`第${attempt}次尝试加载本体费用数据...`);
            await loadOntologyData();
            console.log('本体费用数据加载成功');
            return;
        } catch (error) {
            console.error(`第${attempt}次加载失败:`, error);
            
            if (attempt === maxRetries) {
                console.error('所有重试都失败，显示错误状态');
                showOntologyErrorState('数据加载失败，请点击查询按钮重试');
                throw error;
            }
            
            // 等待一段时间后重试
            await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
    }
}

/**
 * 显示全局错误消息
 * @param {string} message - 错误消息
 */
function showOntologyGlobalErrorMessage(message) {
    // 尝试使用现有的错误显示机制
    if (typeof showOntologyErrorState === 'function') {
        showOntologyErrorState(message);
    } else {
        // 如果没有现有机制，使用alert作为后备
        alert(message);
        console.error('全局错误:', message);
    }
}

/**
 * 缓存常用DOM元素
 */
function cacheOntologyElements() {
    console.log('开始缓存本体费用DOM元素...');
    
    // 本体费用相关元素
    OntologyManagement.elements = {
        nameInput: document.getElementById('ontology-name-input'),
        tableBody: document.getElementById('ontology-table-body'),
        totalInfo: document.getElementById('ontology-total-info'),
        prevBtn: document.getElementById('ontology-prev-btn'),
        nextBtn: document.getElementById('ontology-next-btn'),
        paginationPages: document.getElementById('ontology-pagination-pages'),
        editModal: document.getElementById('ontology-edit-modal')
    };

    // 验证关键元素是否存在
    const criticalElements = [
        { name: 'ontology-table-body', element: OntologyManagement.elements.tableBody },
        { name: 'ontology-pagination-pages', element: OntologyManagement.elements.paginationPages },
        { name: 'ontology-total-info', element: OntologyManagement.elements.totalInfo }
    ];

    let missingElements = [];
    criticalElements.forEach(({ name, element }) => {
        if (!element) {
            missingElements.push(name);
            console.error(`关键元素缺失: ${name}`);
        } else {
            console.log(`元素缓存成功: ${name}`);
        }
    });

    if (missingElements.length > 0) {
        throw new Error(`缺失关键DOM元素: ${missingElements.join(', ')}`);
    }

    console.log('本体费用DOM元素缓存完成');
}

// ==================== 数据加载和渲染功能 ====================

/**
 * 加载本体费用数据
 * @param {Object} searchParams - 搜索参数
 */
async function loadOntologyData(searchParams = {}) {
    const pagination = OntologyManagement.pagination;
    
    try {
        console.log('开始加载本体费用数据，参数:', searchParams);
        showOntologyLoadingState(true);
        
        // 构建请求参数
        const params = new URLSearchParams({
            ...searchParams,
            page: pagination.currentPage,
            pageSize: pagination.pageSize
        });

        console.log('请求URL:', `${OntologyManagement.api.list}?${params}`);
        
        // 真实API调用
        const data = await getOntologyList(searchParams);
        console.log('API响应数据:', data);

        if (data && data.success) {
            // 更新分页信息
            pagination.totalRecords = data.total || 0;
            pagination.totalPages = Math.ceil(pagination.totalRecords / pagination.pageSize);

            console.log('分页信息更新:', {
                totalRecords: pagination.totalRecords,
                totalPages: pagination.totalPages,
                currentPage: pagination.currentPage
            });

            // 渲染数据
            renderOntologyTable(data.data || []);
            renderOntologyPagination();
            
            console.log('本体费用数据渲染完成');
        } else {
            const errorMsg = data?.message || '加载数据失败';
            console.error('API返回错误:', errorMsg);
            showOntologyErrorState(errorMsg);
            throw new Error(errorMsg);
        }
    } catch (error) {
        console.error('加载本体费用失败:', error);
        const errorMsg = error.message || '网络请求失败';
        showOntologyErrorState('加载数据出错: ' + errorMsg);
        throw error; // 重新抛出错误以便重试机制处理
    } finally {
        showOntologyLoadingState(false);
    }
}



// ==================== 表格渲染功能 ====================

/**
 * 渲染本体费用表格
 * @param {Array} data - 费用数据
 */
function renderOntologyTable(data) {
    const tableBody = OntologyManagement.elements.tableBody;

    if (!data || data.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="5" style="text-align: center; padding: 40px; color: var(--secondary-color);">
                    暂无数据
                </td>
            </tr>
        `;
        return;
    }

    const rows = data.map(item => `
        <tr>
            <td>${item.费用序号}</td>
            <td>${item.费用名称}</td>
            <td>${item.计算逻辑}</td>
            <td>${item.费用关系}</td>
            <td>
                <button class="btn btn-sm btn-primary" onclick="editOntologyData(${item.id})">编辑</button>
                <button class="btn btn-sm btn-danger" onclick="deleteOntologyData(${item.id}, '${item.费用名称}')" style="margin-left: 5px;">删除</button>
            </td>
        </tr>
    `).join('');

    tableBody.innerHTML = rows;
}

/**
 * 渲染本体费用分页控件
 */
function renderOntologyPagination() {
    const pagination = OntologyManagement.pagination;
    const elements = OntologyManagement.elements;

    // 更新总记录数信息
    elements.totalInfo.textContent = `共 ${pagination.totalRecords} 条记录`;

    // 更新分页按钮状态
    elements.prevBtn.disabled = pagination.currentPage <= 1;
    elements.nextBtn.disabled = pagination.currentPage >= pagination.totalPages;

    // 渲染页码
    renderOntologyPaginationPages(pagination, elements.paginationPages);
}

/**
 * 渲染本体费用分页页码
 * @param {Object} pagination - 分页信息
 * @param {HTMLElement} container - 页码容器
 */
function renderOntologyPaginationPages(pagination, container) {
    container.innerHTML = '';

    if (pagination.totalPages > 0) {
        // 计算显示的页码范围
        let startPage = Math.max(1, pagination.currentPage - 2);
        let endPage = Math.min(pagination.totalPages, pagination.currentPage + 2);

        // 如果当前页靠近开始，显示更多后面的页码
        if (pagination.currentPage <= 3) {
            endPage = Math.min(pagination.totalPages, 5);
        }

        // 如果当前页靠近结束，显示更多前面的页码
        if (pagination.currentPage >= pagination.totalPages - 2) {
            startPage = Math.max(1, pagination.totalPages - 4);
        }

        // 渲染页码按钮
        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('span');
            pageBtn.className = `pagination-page ${i === pagination.currentPage ? 'active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.onclick = () => gotoOntologyPage(i);
            container.appendChild(pageBtn);
        }

        // 添加总页数显示
        const totalInfo = document.createElement('span');
        totalInfo.textContent = ` / ${pagination.totalPages}页`;
        totalInfo.style.marginLeft = '10px';
        totalInfo.style.color = 'var(--secondary-color)';
        container.appendChild(totalInfo);
    }
}

// ==================== 搜索和分页导航功能 ====================

/**
 * 搜索本体费用数据
 */
async function searchOntologyData() {
    const elements = OntologyManagement.elements;
    const name = elements.nameInput.value.trim();

    const searchParams = {};
    if (name) {
        searchParams.name = name;
    }

    // 重置到第一页
    OntologyManagement.pagination.currentPage = 1;

    try {
        await loadOntologyData(searchParams);
    } catch (error) {
        console.error('搜索本体费用失败:', error);
    }
}

/**
 * 重置本体费用查询表单
 */
function resetOntologyForm() {
    const elements = OntologyManagement.elements;
    elements.nameInput.value = '';

    // 重新加载数据
    OntologyManagement.pagination.currentPage = 1;
    loadOntologyData();
}

/**
 * 分页导航 - 上一页
 */
function prevOntologyPage() {
    const pagination = OntologyManagement.pagination;
    if (pagination.currentPage > 1) {
        pagination.currentPage--;
        loadOntologyDataWithCurrentSearch();
    }
}

/**
 * 分页导航 - 下一页
 */
function nextOntologyPage() {
    const pagination = OntologyManagement.pagination;
    if (pagination.currentPage < pagination.totalPages) {
        pagination.currentPage++;
        loadOntologyDataWithCurrentSearch();
    }
}

/**
 * 分页导航 - 跳转到指定页
 * @param {number} page - 目标页码
 */
function gotoOntologyPage(page) {
    const pagination = OntologyManagement.pagination;
    if (page >= 1 && page <= pagination.totalPages) {
        pagination.currentPage = page;
        loadOntologyDataWithCurrentSearch();
    }
}

/**
 * 分页加载本体费用（保持当前搜索条件）
 */
function loadOntologyDataWithCurrentSearch() {
    const elements = OntologyManagement.elements;
    const name = elements.nameInput.value.trim();

    const searchParams = {};
    if (name) {
        searchParams.name = name;
    }

    loadOntologyData(searchParams);
}

// ==================== 编辑和删除功能 ====================

/**
 * 编辑本体费用数据
 * @param {number} id - 费用ID
 */
async function editOntologyData(id) {
    console.log('显示编辑模态框，ID:', id);
    try {
        // 获取详细数据
        const data = await getOntologyDataById(id);

        if (data) {
            // 先重置字段状态
            resetFieldsReadonlyState();

            // 填充编辑表单
            document.getElementById('ontology-edit-id').value = data.id;
            document.getElementById('ontology-edit-sequence').value = data.费用序号;
            document.getElementById('ontology-edit-name').value = data.费用名称;
            document.getElementById('ontology-edit-logic').value = data.计算逻辑;
            document.getElementById('ontology-edit-relation').value = data.费用关系;

            // 修改模态框标题为编辑
            document.getElementById('ontology-modal-title').textContent = '编辑费用信息';

            // 设置编辑模式的字段状态
            setEditModeFieldStates();

            // 显示编辑模态框
            showOntologyEditModal();
        }
    } catch (error) {
        console.error('获取费用详情失败:', error);
        alert('获取费用详情失败，请重试');
    }
}

/**
 * 删除本体费用数据
 * @param {number} id - 费用ID
 * @param {string} name - 费用名称
 */
async function deleteOntologyData(id, name) {
    if (!confirm(`确定要删除费用"${name}"吗？`)) {
        return;
    }

    try {
        // 真实删除API调用
        const result = await deleteOntology(id);

        if (result.success) {
            alert('删除成功');
            // 重新加载数据
            loadOntologyDataWithCurrentSearch();
        } else {
            alert('删除失败: ' + result.message);
        }
    } catch (error) {
        console.error('删除费用失败:', error);
        alert('删除失败，请重试');
    }
}

/**
 * 保存本体费用数据（表单提交处理）
 */
async function saveOntologyData() {
    const form = document.getElementById('ontology-edit-form');

    // 表单验证
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const formData = {
        id: document.getElementById('ontology-edit-id').value,
        费用序号: parseInt(document.getElementById('ontology-edit-sequence').value),
        费用名称: document.getElementById('ontology-edit-name').value.trim(),
        计算逻辑: document.getElementById('ontology-edit-logic').value.trim(),
        费用关系: document.getElementById('ontology-edit-relation').value.trim()
    };

    try {
        // 调用API保存数据
        const result = await saveOntologyDataAPI(formData);

        if (result.success) {
            const isNew = !formData.id || formData.id === '';
            alert(isNew ? '新增成功' : '保存成功');
            closeOntologyEditModal();
            // 重新加载数据
            loadOntologyDataWithCurrentSearch();
        } else {
            alert('保存失败: ' + result.message);
        }
    } catch (error) {
        console.error('保存费用失败:', error);
        alert('保存失败，请重试');
    }
}

// ==================== 新增功能 ====================

/**
 * 获取下一个费用序号
 * @returns {Promise<number>} 下一个序号
 */
async function getNextOntologySequenceNumber() {
    try {
        const result = await getOntologyList();
        if (result.success && result.data && result.data.length > 0) {
            const maxSeqNo = Math.max(...result.data.map(item => item.费用序号 || 0));
            return maxSeqNo + 1;
        }
        return 1;
    } catch (error) {
        console.error('获取序号失败:', error);
        return 1;
    }
}

/**
 * 新增本体费用
 */
async function addOntologyData() {
    console.log('调用了addOntologyData');
    try {
        const nextSeqNo = await getNextOntologySequenceNumber();
        showOntologyAddModal(nextSeqNo);
    } catch (error) {
        console.error('获取序号失败:', error);
        alert('获取序号失败：' + error.message);
    }
}

/**
 * 显示本体费用新增模态框
 * @param {number} nextSeqNo - 下一个序号
 */
function showOntologyAddModal(nextSeqNo) {
    console.log('显示新增模态框');

    // 先重置字段状态
    resetFieldsReadonlyState();

    // 清空表单并设置默认值
    document.getElementById('ontology-edit-id').value = '';
    document.getElementById('ontology-edit-sequence').value = nextSeqNo;
    document.getElementById('ontology-edit-name').value = '';
    document.getElementById('ontology-edit-logic').value = '';
    document.getElementById('ontology-edit-relation').value = '';

    // 修改模态框标题
    document.getElementById('ontology-modal-title').textContent = '新增费用信息';

    // 设置新增模式的字段状态
    setAddModeFieldStates();

    // 显示模态框
    showOntologyEditModal();
}

// ==================== 模态框管理功能 ====================

/**
 * 设置表单字段的只读状态
 * @param {string} fieldId - 字段ID
 * @param {boolean} readonly - 是否只读
 */
function setFieldReadonly(fieldId, readonly) {
    const field = document.getElementById(fieldId);
    if (field) {
        if (readonly) {
            field.setAttribute('readonly', 'readonly');
            field.classList.add('readonly-input');
            console.log(`设置字段 ${fieldId} 为只读`);
        } else {
            field.removeAttribute('readonly');
            field.classList.remove('readonly-input');
            console.log(`设置字段 ${fieldId} 为可编辑`);
        }
    } else {
        console.warn(`未找到字段: ${fieldId}`);
    }
}

/**
 * 重置所有表单字段的只读状态
 */
function resetFieldsReadonlyState() {
    setFieldReadonly('ontology-edit-sequence', false);
    setFieldReadonly('ontology-edit-name', false);
    setFieldReadonly('ontology-edit-logic', false);
    setFieldReadonly('ontology-edit-relation', false);
}

/**
 * 设置新增模式的字段状态
 */
function setAddModeFieldStates() {
    console.log('设置新增模式字段状态');
    // 新增时：费用序号只读，其他字段可编辑
    setFieldReadonly('ontology-edit-sequence', true);
    setFieldReadonly('ontology-edit-name', false);
    setFieldReadonly('ontology-edit-logic', false);
    setFieldReadonly('ontology-edit-relation', false);
}

/**
 * 设置编辑模式的字段状态
 */
function setEditModeFieldStates() {
    console.log('设置编辑模式字段状态');
    // 编辑时：费用序号和费用名称只读，其他字段可编辑
    setFieldReadonly('ontology-edit-sequence', true);
    setFieldReadonly('ontology-edit-name', true);
    setFieldReadonly('ontology-edit-logic', false);
    setFieldReadonly('ontology-edit-relation', false);
}

/**
 * 显示编辑模态框
 */
function showOntologyEditModal() {
    const modal = OntologyManagement.elements.editModal;
    if (modal) {
        modal.classList.add('show');
    }
}

/**
 * 关闭编辑模态框
 */
function closeOntologyEditModal() {
    const modal = OntologyManagement.elements.editModal;
    if (modal) {
        modal.classList.remove('show');
    }

    // 重置字段只读状态
    resetFieldsReadonlyState();

    // 清空表单
    document.getElementById('ontology-edit-form').reset();
}

// ==================== 状态显示功能 ====================

/**
 * 显示加载状态
 * @param {boolean} isLoading - 是否正在加载
 */
function showOntologyLoadingState(isLoading) {
    const tableBody = OntologyManagement.elements.tableBody;

    if (isLoading) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="5" style="text-align: center; padding: 40px;">
                    <div class="loading-spinner"></div>
                    <div style="margin-top: 10px;">正在加载数据...</div>
                </td>
            </tr>
        `;
    }
}

/**
 * 显示错误状态
 * @param {string} message - 错误消息
 */
function showOntologyErrorState(message) {
    const tableBody = OntologyManagement.elements.tableBody;

    tableBody.innerHTML = `
        <tr>
            <td colspan="5" style="text-align: center; padding: 40px; color: var(--danger-color);">
                <div style="margin-bottom: 10px;">⚠️ ${message}</div>
                <button class="btn btn-primary btn-sm" onclick="loadOntologyDataWithCurrentSearch()">重新加载</button>
            </td>
        </tr>
    `;
}



// ==================== 真实API函数 ====================

/**
 * 获取本体费用列表API
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} API响应
 */
async function getOntologyList(params = {}) {
    try {
        const queryParams = new URLSearchParams();

        // 添加查询参数
        if (params.name) queryParams.append('name', params.name);
        if (params.page) queryParams.append('page', params.page);
        if (params.pageSize) queryParams.append('pageSize', params.pageSize);

        const url = `/api/indicator_rule/ontology/list?${queryParams.toString()}`;
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error('获取本体费用列表失败:', error);
        return {
            success: false,
            message: `获取本体费用列表失败: ${error.message}`,
            data: [],
            total: 0
        };
    }
}

/**
 * 删除本体费用API
 * @param {number} id - 费用ID
 * @returns {Promise<Object>} API响应
 */
async function deleteOntology(id) {
    try {
        const response = await fetch(`/api/indicator_rule/ontology/${id}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error('删除本体费用失败:', error);
        return {
            success: false,
            message: `删除本体费用失败: ${error.message}`
        };
    }
}

/**
 * 获取单个本体费用数据API
 * @param {number} id - 费用ID
 * @returns {Promise<Object>} API响应
 */
async function getOntologyDataById(id) {
    try {
        const response = await fetch(`/api/indicator_rule/ontology/${id}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        if (result.success) {
            return result.data;
        } else {
            throw new Error(result.message || '获取数据失败');
        }
    } catch (error) {
        console.error('获取本体费用详情失败:', error);
        throw error;
    }
}

/**
 * 保存本体费用数据API（创建或更新）
 * @param {Object} data - 费用数据
 * @returns {Promise<Object>} API响应
 */
async function saveOntologyDataAPI(data) {
    try {
        const isUpdate = data.id && data.id !== '';
        const url = isUpdate ? `/api/indicator_rule/ontology/${data.id}` : '/api/indicator_rule/ontology';
        const method = isUpdate ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error('保存本体费用失败:', error);
        return {
            success: false,
            message: `保存本体费用失败: ${error.message}`
        };
    }
}

// ==================== 页面初始化事件 ====================

/**
 * 页面加载完成后的初始化
 */
document.addEventListener('DOMContentLoaded', async function() {
    // 检查是否在本体费用管理页面
    if (document.querySelector('.ontology-management-container')) {
        console.log('检测到本体费用管理页面，开始初始化...');

        try {
            // 初始化本体费用管理模块
            await initOntologyManagement();
        } catch (error) {
            console.error('本体费用页面初始化过程中发生错误:', error);
        }
    }
});

// 备用初始化机制 - 如果DOMContentLoaded已经触发
if (document.readyState === 'loading') {
    // 文档仍在加载中，DOMContentLoaded事件会处理初始化
    console.log('文档正在加载中，等待DOMContentLoaded事件...');
} else {
    // 文档已经加载完成，立即初始化
    console.log('文档已加载完成，立即初始化...');
    setTimeout(async () => {
        if (document.querySelector('.ontology-management-container')) {
            console.log('备用初始化：检测到本体费用管理页面');
            try {
                await initOntologyManagement();
            } catch (error) {
                console.error('备用初始化失败:', error);
            }
        }
    }, 100);
}

// 模态框点击外部关闭功能
window.onclick = function(event) {
    const modal = document.getElementById('ontology-edit-modal');
    if (event.target === modal) {
        closeOntologyEditModal();
    }
};

// 导出全局函数供HTML调用
window.addOntologyData = addOntologyData;
window.editOntologyData = editOntologyData;
window.deleteOntologyData = deleteOntologyData;
window.saveOntologyData = saveOntologyData;
window.closeOntologyEditModal = closeOntologyEditModal;
window.searchOntologyData = searchOntologyData;
window.resetOntologyForm = resetOntologyForm;
window.prevOntologyPage = prevOntologyPage;
window.nextOntologyPage = nextOntologyPage;
