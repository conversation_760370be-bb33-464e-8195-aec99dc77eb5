

"""
电力线路匹配算法模块 - 简化版本

本模块实现了多种相似度匹配算法的简化版本，不依赖外部库，可以直接运行。
支持的算法包括：欧几里得距离、余弦相似度、曼哈顿距离、加权距离等。

主要应用场景：
- 根据边界条件（风速、覆冰、回路数、导线规格）匹配历史线路
- 为新建线路提供参考设计方案
- 基于历史数据进行线路设计优化

作者: AI Assistant
创建时间: 2025-07-30
"""

import math
from typing import List, Tuple, Optional


class SimpleLineMatching:
    """
    简化版电力线路匹配算法类
    
    该类实现了多种相似度计算算法的简化版本，不依赖外部库。
    支持的算法包括：
    1. 欧几里得距离 (euclidean) - 最常用的距离度量
    2. 余弦相似度 (cosine) - 关注特征向量的方向
    3. 曼哈顿距离 (manhattan) - 对异常值不敏感
    4. 加权距离 (weighted) - 可设置特征重要性权重
    """
    
    def __init__(self, method: str = 'euclidean', weights: Optional[List[float]] = None):
        """
        初始化匹配算法
        
        Args:
            method (str): 匹配算法类型，可选值：
                - 'euclidean': 欧几里得距离
                - 'cosine': 余弦相似度  
                - 'manhattan': 曼哈顿距离
                - 'weighted': 加权欧几里得距离
            weights (List[float], optional): 特征权重列表，仅在method='weighted'时使用
                权重顺序：[风速权重, 覆冰权重, 回路数权重, 导线规格权重]
        """
        self.method = method
        self.weights = weights
        self.historical_data = None
        self.normalized_data = None
        
        # 验证算法类型
        valid_methods = ['euclidean', 'cosine', 'manhattan', 'weighted']
        if method not in valid_methods:
            raise ValueError(f"不支持的算法类型: {method}. 支持的类型: {valid_methods}")
    
    def _normalize_data(self, data: List[List[float]]) -> List[List[float]]:
        """
        数据标准化处理（Z-score标准化）
        
        Args:
            data: 原始数据
            
        Returns:
            标准化后的数据
        """
        if not data:
            return data
        
        n_features = len(data[0])
        n_samples = len(data)
        
        # 计算每个特征的均值和标准差
        means = []
        stds = []
        
        for j in range(n_features):
            feature_values = [data[i][j] for i in range(n_samples)]
            mean = sum(feature_values) / n_samples
            variance = sum((x - mean) ** 2 for x in feature_values) / n_samples
            std = math.sqrt(variance) if variance > 0 else 1.0
            
            means.append(mean)
            stds.append(std)
        
        # 标准化数据
        normalized = []
        for i in range(n_samples):
            normalized_row = []
            for j in range(n_features):
                normalized_value = (data[i][j] - means[j]) / stds[j]
                normalized_row.append(normalized_value)
            normalized.append(normalized_row)
        
        # 保存标准化参数用于新数据
        self.means = means
        self.stds = stds
        
        return normalized
    
    def _normalize_new_data(self, new_data: List[float]) -> List[float]:
        """
        使用已有参数标准化新数据
        
        Args:
            new_data: 新数据点
            
        Returns:
            标准化后的新数据点
        """
        if not hasattr(self, 'means') or not hasattr(self, 'stds'):
            raise ValueError("请先调用fit()方法训练数据")
        
        normalized = []
        for i, value in enumerate(new_data):
            normalized_value = (value - self.means[i]) / self.stds[i]
            normalized.append(normalized_value)
        
        return normalized
    
    def fit(self, historical_data: List[List[float]]) -> None:
        """
        训练/拟合历史数据
        
        Args:
            historical_data: 历史线路数据，每行代表一条历史线路
                格式：[[风速, 覆冰, 回路数, 导线规格], ...]
        """
        if len(historical_data) < 2:
            raise ValueError("历史数据至少需要2条记录")
        
        self.historical_data = [row[:] for row in historical_data]  # 深拷贝
        
        # 对于余弦相似度，不需要标准化
        if self.method != 'cosine':
            self.normalized_data = self._normalize_data(historical_data)
    
    def find_similar_lines(self, new_line_features: List[float], top_k: int = 5) -> Tuple[List[int], List[float]]:
        """
        找到最相似的K条历史线路
        
        Args:
            new_line_features: 新线路特征 [风速, 覆冰, 回路数, 导线规格]
            top_k: 返回最相似的前K条线路，默认为5
            
        Returns:
            Tuple[List[int], List[float]]: 
                - 最相似线路的索引列表（按相似度降序排列）
                - 对应的相似度分数列表
        """
        if self.historical_data is None:
            raise ValueError("请先调用fit()方法训练历史数据")
        
        if len(new_line_features) != len(self.historical_data[0]):
            raise ValueError(f"新线路特征维度({len(new_line_features)})与历史数据维度({len(self.historical_data[0])})不匹配")
        
        # 根据选择的算法计算相似度
        if self.method == 'euclidean':
            similarities = self._euclidean_similarity(new_line_features)
        elif self.method == 'cosine':
            similarities = self._cosine_similarity(new_line_features)
        elif self.method == 'manhattan':
            similarities = self._manhattan_similarity(new_line_features)
        elif self.method == 'weighted':
            similarities = self._weighted_similarity(new_line_features)
        
        # 获取top-k最相似的索引（按相似度降序排列）
        top_k = min(top_k, len(similarities))
        indexed_similarities = [(i, sim) for i, sim in enumerate(similarities)]
        indexed_similarities.sort(key=lambda x: x[1], reverse=True)
        
        top_indices = [item[0] for item in indexed_similarities[:top_k]]
        top_similarities = [item[1] for item in indexed_similarities[:top_k]]
        
        return top_indices, top_similarities
    
    def _euclidean_similarity(self, new_features: List[float]) -> List[float]:
        """
        欧几里得距离相似度计算
        
        欧几里得距离是最常用的距离度量方法，计算多维空间中两点间的直线距离。
        距离越小表示越相似，这里转换为相似度分数（0-1之间，越大越相似）。
        
        公式: distance = sqrt(sum((x_i - y_i)^2))
        相似度 = 1 / (1 + distance)
        
        Args:
            new_features: 新线路特征向量
            
        Returns:
            相似度分数列表
        """
        new_normalized = self._normalize_new_data(new_features)
        similarities = []
        
        for hist_data in self.normalized_data:
            distance = 0
            for i in range(len(new_normalized)):
                distance += (hist_data[i] - new_normalized[i]) ** 2
            distance = math.sqrt(distance)
            similarity = 1 / (1 + distance)
            similarities.append(similarity)
        
        return similarities
    
    def _cosine_similarity(self, new_features: List[float]) -> List[float]:
        """
        余弦相似度计算
        
        余弦相似度通过计算两个向量夹角的余弦值来度量相似性。
        它关注的是向量的方向而不是大小，适合比例关系重要的场景。
        
        公式: cosine_sim = (A·B) / (||A|| * ||B||)
        取值范围: [-1, 1]，1表示完全相同，-1表示完全相反，0表示正交
        
        Args:
            new_features: 新线路特征向量
            
        Returns:
            余弦相似度分数列表
        """
        similarities = []
        
        # 计算新特征向量的模长
        new_norm = math.sqrt(sum(x ** 2 for x in new_features))
        if new_norm == 0:
            return [0] * len(self.historical_data)
        
        for hist_data in self.historical_data:
            # 计算点积
            dot_product = sum(new_features[i] * hist_data[i] for i in range(len(new_features)))
            
            # 计算历史数据向量的模长
            hist_norm = math.sqrt(sum(x ** 2 for x in hist_data))
            
            if hist_norm == 0:
                similarity = 0
            else:
                cosine_sim = dot_product / (new_norm * hist_norm)
                # 将[-1,1]范围映射到[0,1]范围
                similarity = (cosine_sim + 1) / 2
            
            similarities.append(similarity)
        
        return similarities
    
    def _manhattan_similarity(self, new_features: List[float]) -> List[float]:
        """
        曼哈顿距离相似度计算
        
        曼哈顿距离（也称为L1距离或城市街区距离）计算各维度差值的绝对值之和。
        相比欧几里得距离，曼哈顿距离对异常值不敏感，在某些场景下更稳健。
        
        公式: distance = sum(|x_i - y_i|)
        相似度 = 1 / (1 + distance)
        
        Args:
            new_features: 新线路特征向量
            
        Returns:
            相似度分数列表
        """
        new_normalized = self._normalize_new_data(new_features)
        similarities = []
        
        for hist_data in self.normalized_data:
            distance = sum(abs(hist_data[i] - new_normalized[i]) for i in range(len(new_normalized)))
            similarity = 1 / (1 + distance)
            similarities.append(similarity)
        
        return similarities
    
    def _weighted_similarity(self, new_features: List[float]) -> List[float]:
        """
        加权欧几里得距离相似度计算
        
        在标准欧几里得距离基础上，为不同特征分配不同的权重。
        这允许根据工程经验或业务需求强调某些特征的重要性。
        例如：风速和覆冰可能比回路数更重要。
        
        公式: weighted_distance = sqrt(sum(w_i * (x_i - y_i)^2))
        其中 w_i 是第i个特征的权重
        
        Args:
            new_features: 新线路特征向量
            
        Returns:
            加权相似度分数列表
        """
        if self.weights is None:
            raise ValueError("使用加权算法时必须设置权重参数")
        
        if len(self.weights) != len(new_features):
            raise ValueError(f"权重维度({len(self.weights)})与特征维度({len(new_features)})不匹配")
        
        new_normalized = self._normalize_new_data(new_features)
        similarities = []
        
        for hist_data in self.normalized_data:
            weighted_distance = 0
            for i in range(len(new_normalized)):
                diff = hist_data[i] - new_normalized[i]
                weighted_distance += self.weights[i] * (diff ** 2)
            
            distance = math.sqrt(weighted_distance)
            similarity = 1 / (1 + distance)
            similarities.append(similarity)
        
        return similarities


def demonstrate_simple_algorithms():
    """
    演示简化版匹配算法的使用方法
    """
    print("=" * 60)
    print("电力线路匹配算法演示（简化版）")
    print("=" * 60)
    
    # 模拟历史线路数据
    # 特征顺序：[风速(m/s), 覆冰厚度(mm), 回路数, 导线规格编号]
    historical_data = [
        [30.0, 10.0, 2, 1],  # 线路1
        [25.0, 15.0, 1, 2],  # 线路2
        [35.0, 5.0, 3, 1],   # 线路3
        [28.0, 12.0, 2, 2],  # 线路4
        [32.0, 8.0, 1, 3],   # 线路5
        [26.0, 18.0, 2, 1],  # 线路6
        [29.0, 11.0, 3, 2],  # 线路7
        [33.0, 7.0, 2, 3],   # 线路8
    ]
    
    # 新建线路的特征
    new_line = [28.5, 12.5, 2, 1]
    print(f"新建线路特征: 风速={new_line[0]}m/s, 覆冰={new_line[1]}mm, 回路数={new_line[2]}, 导线规格={new_line[3]}")
    print()
    
    # 测试不同的匹配算法
    algorithms = [
        ('euclidean', '标准化欧几里得距离', {}),
        ('cosine', '余弦相似度', {}),
        ('manhattan', '曼哈顿距离', {}),
        ('weighted', '加权欧几里得距离', {'weights': [0.4, 0.4, 0.1, 0.1]}),  # 风速和覆冰权重更高
    ]
    
    # 对每种算法进行测试
    for method, name, kwargs in algorithms:
        print(f"【{name}算法】")
        print("-" * 40)
        
        try:
            # 创建匹配器实例
            matcher = SimpleLineMatching(method=method, **kwargs)
            matcher.fit(historical_data)
            
            # 找到最相似的5条线路
            similar_indices, similarities = matcher.find_similar_lines(new_line, top_k=5)
            
            print("最相似的5条历史线路:")
            for i, (idx, sim) in enumerate(zip(similar_indices, similarities)):
                hist_line = historical_data[idx]
                print(f"  {i+1}. 线路{idx+1}: 风速={hist_line[0]}m/s, 覆冰={hist_line[1]}mm, "
                      f"回路数={hist_line[2]}, 导线规格={hist_line[3]} (相似度: {sim:.4f})")
            
        except Exception as e:
            print(f"  算法执行出错: {e}")
        
        print()


if __name__ == "__main__":
    """
    主程序入口
    """
    print("电力线路匹配算法演示程序（简化版）")

    demonstrate_simple_algorithms()

