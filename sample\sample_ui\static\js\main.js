/**
 * 技术经济成本平台 - 主JavaScript文件
 */

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    console.log('技术经济成本平台初始化');
    
    // 初始化所有组件
    initModals();
    initFileUploads();
    initStepNavigation();
    initFormValidation();
    
    // 添加平滑滚动效果
    initSmoothScroll();
    
    // 添加动画效果
    initAnimations();
    
    // 检查是否在方案展示页面
    const schemesContainer = document.querySelector('.schemes-container');
    if (schemesContainer) {
        // 方案选择逻辑
        const schemeRadios = document.querySelectorAll('input[name="combination"]');
        schemeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                // 移除所有方案卡片的选中样式
                document.querySelectorAll('.scheme-card').forEach(card => {
                    card.classList.remove('selected');
                });
                
                // 为选中的方案卡片添加选中样式
                if (this.checked) {
                    this.closest('.scheme-card').classList.add('selected');
                }
            });
        });
        
        // 默认选中第一个方案
        const defaultScheme = document.getElementById('scheme1');
        if (defaultScheme) {
            defaultScheme.checked = true;
            defaultScheme.closest('.scheme-card').classList.add('selected');
        }
        
        // 详情按钮点击事件
        const detailButtons = document.querySelectorAll('[data-modal-target]');
        detailButtons.forEach(button => {
            button.addEventListener('click', function() {
                const modalId = this.getAttribute('data-modal-target');
                const modal = document.getElementById(modalId);
                
                if (modal) {
                    // 显示模态框
                    modal.style.display = 'block';
                    
                    // 点击关闭按钮关闭模态框
                    const closeBtn = modal.querySelector('.close-modal');
                    if (closeBtn) {
                        closeBtn.onclick = function() {
                            modal.style.display = 'none';
                        }
                    }
                    
                    // 点击模态框外部关闭模态框
                    window.onclick = function(event) {
                        if (event.target === modal) {
                            modal.style.display = 'none';
                        }
                    }
                    
                    // 点击取消按钮关闭模态框
                    const cancelBtn = modal.querySelector('[data-modal-close]');
                    if (cancelBtn) {
                        cancelBtn.onclick = function() {
                            modal.style.display = 'none';
                        }
                    }
                }
            });
        });
    }
});

/**
 * 初始化通用组件
 */
function initializeComponents() {
    // 初始化提示框自动关闭
    initializeAlerts();
    
    // 初始化表单验证
    initializeFormValidation();
    
    // 初始化导航栏交互
    initializeNavigation();
}

/**
 * 初始化提示框自动关闭
 */
function initializeAlerts() {
    const alerts = document.querySelectorAll('.alert');
    
    alerts.forEach(alert => {
        // 如果提示框有自动关闭属性
        if (alert.dataset.autoDismiss) {
            const delay = parseInt(alert.dataset.autoDismiss) || 5000;
            
            setTimeout(() => {
                fadeOut(alert);
            }, delay);
        }
        
        // 添加关闭按钮功能
        const closeBtn = alert.querySelector('.close-alert');
        if (closeBtn) {
            closeBtn.addEventListener('click', function() {
                fadeOut(alert);
            });
        }
    });
}

/**
 * 初始化表单验证
 */
function initializeFormValidation() {
    const forms = document.querySelectorAll('form[data-validate="true"]');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            const isValid = validateForm(form);
            
            if (!isValid) {
                event.preventDefault();
            }
        });
        
        // 实时验证
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateInput(input);
            });
        });
    });
}

/**
 * 验证表单
 * @param {HTMLFormElement} form - 表单元素
 * @returns {boolean} - 表单是否有效
 */
function validateForm(form) {
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!validateInput(input)) {
            isValid = false;
        }
    });
    
    return isValid;
}

/**
 * 验证单个输入字段
 * @param {HTMLInputElement} input - 输入元素
 * @returns {boolean} - 输入是否有效
 */
function validateInput(input) {
    const value = input.value.trim();
    const errorElement = input.parentElement.querySelector('.error-message');
    
    // 移除现有错误消息
    if (errorElement) {
        errorElement.remove();
    }
    
    // 检查是否为空
    if (input.hasAttribute('required') && value === '') {
        showInputError(input, '此字段不能为空');
        return false;
    }
    
    // 检查电子邮件格式
    if (input.type === 'email' && value !== '') {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            showInputError(input, '请输入有效的电子邮件地址');
            return false;
        }
    }
    
    // 检查数字范围
    if (input.type === 'number' && value !== '') {
        const min = parseFloat(input.getAttribute('min'));
        const max = parseFloat(input.getAttribute('max'));
        
        if (!isNaN(min) && parseFloat(value) < min) {
            showInputError(input, `值必须大于或等于 ${min}`);
            return false;
        }
        
        if (!isNaN(max) && parseFloat(value) > max) {
            showInputError(input, `值必须小于或等于 ${max}`);
            return false;
        }
    }
    
    // 检查密码长度
    if (input.type === 'password' && value !== '') {
        const minLength = parseInt(input.getAttribute('minlength')) || 6;
        
        if (value.length < minLength) {
            showInputError(input, `密码长度必须至少为 ${minLength} 个字符`);
            return false;
        }
    }
    
    // 添加有效类
    input.classList.remove('is-invalid');
    input.classList.add('is-valid');
    
    return true;
}

/**
 * 显示输入错误消息
 * @param {HTMLInputElement} input - 输入元素
 * @param {string} message - 错误消息
 */
function showInputError(input, message) {
    // 添加错误类
    input.classList.remove('is-valid');
    input.classList.add('is-invalid');
    
    // 创建错误消息元素
    const errorElement = document.createElement('div');
    errorElement.className = 'error-message';
    errorElement.textContent = message;
    errorElement.style.color = '#f44336';
    errorElement.style.fontSize = '0.875rem';
    errorElement.style.marginTop = '4px';
    
    // 添加到DOM
    input.parentElement.appendChild(errorElement);
}

/**
 * 初始化导航交互
 */
function initializeNavigation() {
    // 高亮当前页面对应的导航项
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('nav a');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        
        if (href === currentPath || (href !== '/' && currentPath.startsWith(href))) {
            link.classList.add('active');
        }
    });
    
    // 移动端菜单切换
    const menuToggle = document.querySelector('.menu-toggle');
    const mobileMenu = document.querySelector('.mobile-menu');
    
    if (menuToggle && mobileMenu) {
        menuToggle.addEventListener('click', function() {
            mobileMenu.classList.toggle('show');
        });
    }
}

/**
 * 初始化页面特定功能
 */
function initializePageSpecific() {
    // 快速组价页面
    if (document.querySelector('.pricing-page')) {
        initializePricingPage();
    }
    
    // 智能校审页面
    if (document.querySelector('.audit-page')) {
        initializeAuditPage();
    }
    
    // 前期总包孵化页面
    if (document.querySelector('.incubation-page')) {
        initializeIncubationPage();
    }
}

/**
 * 初始化快速组价页面
 */
function initializePricingPage() {
    console.log('初始化快速组价页面');
    
    // 步骤导航
    const stepButtons = document.querySelectorAll('.step-button');
    if (stepButtons.length > 0) {
        stepButtons.forEach(button => {
            button.addEventListener('click', function() {
                const targetStep = this.dataset.step;
                navigateToStep(targetStep);
            });
        });
    }
    
    // 价格计算
    const calculationInputs = document.querySelectorAll('.calculation-input');
    if (calculationInputs.length > 0) {
        calculationInputs.forEach(input => {
            input.addEventListener('change', updatePriceCalculation);
        });
    }
}

/**
 * 初始化智能校审页面
 */
function initializeAuditPage() {
    console.log('初始化智能校审页面');
    
    // 文件上传预览
    const fileInputs = document.querySelectorAll('.file-input');
    if (fileInputs.length > 0) {
        fileInputs.forEach(input => {
            input.addEventListener('change', function() {
                const fileName = this.files[0]?.name || '未选择文件';
                const fileLabel = this.nextElementSibling;
                if (fileLabel) {
                    fileLabel.textContent = fileName;
                }
            });
        });
    }
    
    // 审核结果展示
    const auditResults = document.querySelectorAll('.audit-result');
    if (auditResults.length > 0) {
        auditResults.forEach(result => {
            const toggleBtn = result.querySelector('.toggle-details');
            const details = result.querySelector('.audit-details');
            
            if (toggleBtn && details) {
                toggleBtn.addEventListener('click', function() {
                    details.classList.toggle('show');
                    this.textContent = details.classList.contains('show') ? '隐藏详情' : '显示详情';
                });
            }
        });
    }
}

/**
 * 初始化前期总包孵化页面
 */
function initializeIncubationPage() {
    console.log('初始化前期总包孵化页面');
    
    // 项目过滤
    const filterInputs = document.querySelectorAll('.project-filter');
    if (filterInputs.length > 0) {
        filterInputs.forEach(input => {
            input.addEventListener('change', filterProjects);
        });
    }
    
    // 项目排序
    const sortOptions = document.querySelector('.project-sort');
    if (sortOptions) {
        sortOptions.addEventListener('change', sortProjects);
    }
}

/**
 * 导航到特定步骤
 * @param {string} stepId - 步骤ID
 */
function navigateToStep(stepId) {
    const steps = document.querySelectorAll('.step-content');
    const indicators = document.querySelectorAll('.step-indicator');
    
    // 隐藏所有步骤
    steps.forEach(step => {
        step.classList.remove('active');
    });
    
    // 取消高亮所有指示器
    indicators.forEach(indicator => {
        indicator.classList.remove('active');
    });
    
    // 显示目标步骤
    const targetStep = document.getElementById(stepId);
    const targetIndicator = document.querySelector(`.step-indicator[data-step="${stepId}"]`);
    
    if (targetStep) {
        targetStep.classList.add('active');
    }
    
    if (targetIndicator) {
        targetIndicator.classList.add('active');
    }
}

/**
 * 更新价格计算
 */
function updatePriceCalculation() {
    console.log('更新价格计算');
    // 实际计算逻辑将根据具体业务需求实现
}

/**
 * 过滤项目
 */
function filterProjects() {
    console.log('过滤项目');
    // 实际过滤逻辑将根据具体业务需求实现
}

/**
 * 排序项目
 */
function sortProjects() {
    console.log('排序项目');
    // 实际排序逻辑将根据具体业务需求实现
}

/**
 * 淡出元素
 * @param {HTMLElement} element - 要淡出的元素
 */
function fadeOut(element) {
    element.style.opacity = '1';
    
    (function fade() {
        if ((element.style.opacity -= 0.1) < 0) {
            element.style.display = 'none';
        } else {
            requestAnimationFrame(fade);
        }
    })();
}

// 模态框功能
function initModals() {
    // 打开模态框按钮
    const modalOpenButtons = document.querySelectorAll('[data-modal-target]');
    modalOpenButtons.forEach(button => {
        button.addEventListener('click', () => {
            const modalId = button.getAttribute('data-modal-target');
            const modal = document.getElementById(modalId);
            if (modal) {
                openModal(modal);
            }
        });
    });
    
    // 关闭模态框按钮
    const modalCloseButtons = document.querySelectorAll('.close-modal, [data-modal-close]');
    modalCloseButtons.forEach(button => {
        button.addEventListener('click', () => {
            const modal = button.closest('.modal-backdrop');
            if (modal) {
                closeModal(modal);
            }
        });
    });
    
    // 点击模态框外部关闭
    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('modal-backdrop')) {
            closeModal(e.target);
        }
    });
}

function openModal(modal) {
    modal.classList.add('show');
    document.body.style.overflow = 'hidden';
}

function closeModal(modal) {
    modal.classList.remove('show');
    document.body.style.overflow = '';
}

// 文件上传功能
function initFileUploads() {
    const fileUploads = document.querySelectorAll('.file-upload');
    
    fileUploads.forEach(upload => {
        const input = upload.querySelector('input[type="file"]');
        const label = upload.querySelector('.file-upload-text');
        
        if (input && label) {
            input.addEventListener('change', () => {
                if (input.files.length > 0) {
                    const fileNames = Array.from(input.files).map(file => file.name).join(', ');
                    label.textContent = fileNames;
                    upload.classList.add('has-files');
                } else {
                    label.textContent = '点击或拖拽文件到此处上传';
                    upload.classList.remove('has-files');
                }
            });
        }
        
        // 拖放功能
        upload.addEventListener('dragover', (e) => {
            e.preventDefault();
            upload.classList.add('dragover');
        });
        
        upload.addEventListener('dragleave', () => {
            upload.classList.remove('dragover');
        });
        
        upload.addEventListener('drop', (e) => {
            e.preventDefault();
            upload.classList.remove('dragover');
            
            if (input && e.dataTransfer.files.length > 0) {
                input.files = e.dataTransfer.files;
                
                // 触发change事件
                const event = new Event('change', { bubbles: true });
                input.dispatchEvent(event);
            }
        });
    });
}

// 步骤导航功能
function initStepNavigation() {
    const nextButtons = document.querySelectorAll('[data-next-step]');
    const prevButtons = document.querySelectorAll('[data-prev-step]');
    const stepIndicators = document.querySelectorAll('.step');
    
    // 设置当前步骤高亮
    const currentStep = document.querySelector('.step-content.active');
    if (currentStep) {
        const stepId = currentStep.id;
        const indicator = document.querySelector(`.step[data-step="${stepId}"]`);
        if (indicator) {
            indicator.classList.add('active');
        }
    }
    
    // 步骤指示器点击事件
    stepIndicators.forEach(indicator => {
        indicator.addEventListener('click', () => {
            const stepId = indicator.getAttribute('data-step');
            const stepContent = document.getElementById(stepId);
            
            if (stepContent && !indicator.classList.contains('disabled')) {
                // 隐藏所有步骤
                document.querySelectorAll('.step-content').forEach(content => {
                    content.classList.remove('active');
                });
                
                // 取消高亮所有指示器
                stepIndicators.forEach(ind => {
                    ind.classList.remove('active');
                });
                
                // 显示选中步骤
                stepContent.classList.add('active');
                indicator.classList.add('active');
                
                // 保存当前步骤到会话存储
                sessionStorage.setItem('currentStep', stepId);
            }
        });
    });
    
    // 下一步按钮
    nextButtons.forEach(button => {
        button.addEventListener('click', (e) => {
            e.preventDefault();
            const form = button.closest('form');
            
            // 如果有表单且需要验证
            if (form && form.getAttribute('data-validate') === 'true') {
                if (!validateForm(form)) {
                    return;
                }
            }
            
            const nextStepId = button.getAttribute('data-next-step');
            if (nextStepId) {
                navigateToStep(nextStepId);
            }
        });
    });
    
    // 上一步按钮
    prevButtons.forEach(button => {
        button.addEventListener('click', (e) => {
            e.preventDefault();
            const prevStepId = button.getAttribute('data-prev-step');
            if (prevStepId) {
                navigateToStep(prevStepId);
            }
        });
    });
    
    // 从会话存储中恢复当前步骤
    const savedStep = sessionStorage.getItem('currentStep');
    if (savedStep) {
        navigateToStep(savedStep);
    }
}

// 平滑滚动功能
function initSmoothScroll() {
    const scrollLinks = document.querySelectorAll('a[href^="#"]:not([href="#"])');
    
    scrollLinks.forEach(link => {
        link.addEventListener('click', (e) => {
                e.preventDefault();
            
            const targetId = link.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                const offsetTop = targetElement.getBoundingClientRect().top + window.pageYOffset;
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// 动画效果
function initAnimations() {
    // 检测元素是否在视口中
    const observerOptions = {
        root: null,
        rootMargin: '0px',
        threshold: 0.1
    };
    
    const observer = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // 添加动画类
    const animateElements = document.querySelectorAll('.feature-card, .info-card, .hero-section h1, .hero-section .subtitle');
    
    animateElements.forEach(element => {
        element.classList.add('animate-element');
        observer.observe(element);
    });
}

// 显示提示信息
function showAlert(message, type = 'success', duration = 3000) {
    const alertContainer = document.getElementById('alert-container') || createAlertContainer();
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.textContent = message;
    
    alertContainer.appendChild(alert);
    
    // 淡入效果
    setTimeout(() => {
        alert.style.opacity = '1';
    }, 10);
    
    // 自动关闭
    setTimeout(() => {
        alert.style.opacity = '0';
        setTimeout(() => {
            alert.remove();
        }, 300);
    }, duration);
}

function createAlertContainer() {
    const container = document.createElement('div');
    container.id = 'alert-container';
    container.style.position = 'fixed';
    container.style.top = '20px';
    container.style.right = '20px';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
    return container;
}

// 导出功能
function exportData(data, filename, type) {
    const blob = new Blob([data], { type });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
}

// 在详情模态框中选择方案
const selectScheme1 = document.getElementById('select-scheme1');
const selectScheme2 = document.getElementById('select-scheme2');
const selectScheme3 = document.getElementById('select-scheme3');

if (selectScheme1) {
    selectScheme1.addEventListener('click', function() {
        document.getElementById('scheme1').checked = true;
        document.getElementById('detail-modal-1').style.display = 'none';
        // 触发change事件以更新UI
        const event = new Event('change');
        document.getElementById('scheme1').dispatchEvent(event);
    });
}

if (selectScheme2) {
    selectScheme2.addEventListener('click', function() {
        document.getElementById('scheme2').checked = true;
        document.getElementById('detail-modal-2').style.display = 'none';
        // 触发change事件以更新UI
        const event = new Event('change');
        document.getElementById('scheme2').dispatchEvent(event);
    });
}

if (selectScheme3) {
    selectScheme3.addEventListener('click', function() {
        document.getElementById('scheme3').checked = true;
        document.getElementById('detail-modal-3').style.display = 'none';
        // 触发change事件以更新UI
        const event = new Event('change');
        document.getElementById('scheme3').dispatchEvent(event);
    });
} 