#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速组价模块业务模型
提供业务逻辑层的数据操作接口
"""

from typing import List, Dict, Optional
from .database import ProjectDAO, FeatureDAO, PricingResultDAO

class ProjectService:
    """工程业务服务类"""
    
    def __init__(self):
        self.project_dao = ProjectDAO()
        self.feature_dao = FeatureDAO()
    
    def get_all_projects(self) -> List[Dict]:
        """获取所有工程列表"""
        return self.project_dao.get_all_projects()
    
    def get_project_detail(self, project_id: int) -> Optional[Dict]:
        """获取工程详情（包含特征段信息）"""
        project = self.project_dao.get_project_by_id(project_id)
        if project:
            features = self.feature_dao.get_features_by_project(project_id)
            project['features'] = features
        return project
    
    def create_project(self, project_data: Dict) -> Dict:
        """创建新工程"""
        # 自动分配序号和project_id
        project_data['序号'] = self.project_dao.get_next_xuhao()
        project_data['project_id'] = self.project_dao.get_next_project_id()
        
        # 创建工程
        result_id = self.project_dao.create_project(project_data)
        
        # 返回创建的工程信息
        return self.project_dao.get_project_by_id(project_data['project_id'])
    
    def update_project(self, project_id: int, project_data: Dict) -> bool:
        """更新工程信息"""
        affected_rows = self.project_dao.update_project(project_id, project_data)
        return affected_rows > 0
    
    def delete_project(self, project_id: int) -> bool:
        """删除工程（同时删除相关特征段）"""
        # 先删除相关特征段
        features = self.feature_dao.get_features_by_project(project_id)
        for feature in features:
            self.feature_dao.delete_feature(feature['序号'])
        
        # 删除工程
        affected_rows = self.project_dao.delete_project(project_id)
        return affected_rows > 0
    
    def update_feature_count(self, project_id: int):
        """更新工程的特征段数量"""
        features = self.feature_dao.get_features_by_project(project_id)
        feature_count = len(features)
        self.project_dao.update_project(project_id, {'特征段数量': feature_count})

class FeatureService:
    """特征段业务服务类"""
    
    def __init__(self):
        self.feature_dao = FeatureDAO()
        self.project_dao = ProjectDAO()
    
    def get_features_by_project(self, project_id: int) -> List[Dict]:
        """获取指定工程的所有特征段"""
        return self.feature_dao.get_features_by_project(project_id)
    
    def get_feature_detail(self, feature_id: int) -> Optional[Dict]:
        """获取特征段详情"""
        return self.feature_dao.get_feature_by_id(feature_id)
    
    def create_feature(self, feature_data: Dict) -> Dict:
        """创建新特征段"""
        # 自动分配序号
        feature_data['序号'] = self.feature_dao.get_next_xuhao()
        
        # 创建特征段
        result_id = self.feature_dao.create_feature(feature_data)
        
        # 更新工程的特征段数量
        project_service = ProjectService()
        project_service.update_feature_count(feature_data['project_id'])
        
        # 返回创建的特征段信息
        return self.feature_dao.get_feature_by_id(feature_data['序号'])
    
    def update_feature(self, feature_id: int, feature_data: Dict) -> bool:
        """更新特征段信息"""
        affected_rows = self.feature_dao.update_feature(feature_id, feature_data)
        return affected_rows > 0
    
    def delete_feature(self, feature_id: int) -> bool:
        """删除特征段"""
        # 获取特征段信息以便更新工程统计
        feature = self.feature_dao.get_feature_by_id(feature_id)
        if not feature:
            return False
        
        project_id = feature['project_id']
        
        # 删除特征段
        affected_rows = self.feature_dao.delete_feature(feature_id)
        
        if affected_rows > 0:
            # 更新工程的特征段数量
            project_service = ProjectService()
            project_service.update_feature_count(project_id)
            return True
        
        return False
    
    def validate_terrain_data(self, feature_data: Dict) -> bool:
        """验证地形数据的合理性"""
        terrain_fields = ['平地', '丘陵', '山地', '高山', '峻岭', '泥沼', '河网']
        total_terrain = sum(feature_data.get(field, 0) for field in terrain_fields)
        line_length = feature_data.get('线路长度', 0)
        
        # 地形总长度应该等于线路长度（允许小的误差）
        return abs(total_terrain - line_length) <= 0.1

class PricingResultService:
    """组价结果业务服务类"""
    
    def __init__(self):
        self.result_dao = PricingResultDAO()
        self.feature_dao = FeatureDAO()
    
    def save_fen_result(self, feature_id: int, project_id: int, calculation_data: Dict) -> bool:
        """保存分气象区组价结果"""
        # 获取特征段名称
        feature = self.feature_dao.get_feature_by_id(feature_id)
        if not feature:
            return False
        
        result_data = {
            'feature_id': feature_id,
            'project_id': project_id,
            '特征段名称': feature['特征段名称'],
            '计算数据': str(calculation_data)  # 转换为JSON字符串
        }
        
        result_id = self.result_dao.save_fen_result(result_data)
        return result_id > 0
    
    def save_he_result(self, feature_id: int, project_id: int, calculation_data: Dict) -> bool:
        """保存合并气象区组价结果"""
        # 获取特征段名称
        feature = self.feature_dao.get_feature_by_id(feature_id)
        if not feature:
            return False
        
        result_data = {
            'feature_id': feature_id,
            'project_id': project_id,
            '特征段名称': feature['特征段名称'],
            '计算数据': str(calculation_data)  # 转换为JSON字符串
        }
        
        result_id = self.result_dao.save_he_result(result_data)
        return result_id > 0

# 便捷的服务实例
project_service = ProjectService()
feature_service = FeatureService()
pricing_result_service = PricingResultService()
