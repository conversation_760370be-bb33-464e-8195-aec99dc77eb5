"""
电力线路匹配算法模块

本模块实现了多种相似度匹配算法，用于在历史线路数据库中找到与新建线路最相似的历史线路。
支持的算法包括：欧几里得距离、余弦相似度、曼哈顿距离、加权距离、马哈拉诺比斯距离等。

主要应用场景：
- 根据边界条件（风速、覆冰、回路数、导线规格）匹配历史线路
- 为新建线路提供参考设计方案
- 基于历史数据进行线路设计优化

作者: AI Assistant
创建时间: 2025-07-30
"""

import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.metrics.pairwise import cosine_similarity
from scipy.spatial.distance import mahalanobis
from typing import Tuple, List, Optional
import warnings


class LineMatchingAlgorithm:
    """
    电力线路匹配算法类
    
    该类实现了多种相似度计算算法，用于在历史线路数据中找到与新线路最相似的记录。
    支持的算法包括：
    1. 欧几里得距离 (euclidean) - 最常用的距离度量
    2. 余弦相似度 (cosine) - 关注特征向量的方向
    3. 曼哈顿距离 (manhattan) - 对异常值不敏感
    4. 加权距离 (weighted) - 可设置特征重要性权重
    5. 马哈拉诺比斯距离 (mahalanobis) - 考虑特征间协方差
    """
    
    def __init__(self, method: str = 'euclidean', weights: Optional[List[float]] = None):
        """
        初始化匹配算法
        
        Args:
            method (str): 匹配算法类型，可选值：
                - 'euclidean': 欧几里得距离
                - 'cosine': 余弦相似度  
                - 'manhattan': 曼哈顿距离
                - 'weighted': 加权欧几里得距离
                - 'mahalanobis': 马哈拉诺比斯距离
            weights (List[float], optional): 特征权重列表，仅在method='weighted'时使用
                权重顺序：[风速权重, 覆冰权重, 回路数权重, 导线规格权重]
        """
        self.method = method
        self.weights = weights
        self.scaler = StandardScaler()
        self.historical_data = None
        self.scaled_historical = None
        self.cov_inv = None
        
        # 验证算法类型
        valid_methods = ['euclidean', 'cosine', 'manhattan', 'weighted', 'mahalanobis']
        if method not in valid_methods:
            raise ValueError(f"不支持的算法类型: {method}. 支持的类型: {valid_methods}")
    
    def fit(self, historical_data: np.ndarray) -> None:
        """
        训练/拟合历史数据
        
        对历史数据进行预处理，包括标准化、协方差矩阵计算等。
        
        Args:
            historical_data (np.ndarray): 历史线路数据矩阵，形状为 (n_samples, n_features)
                每行代表一条历史线路，列顺序为：[风速, 覆冰, 回路数, 导线规格]
        """
        if historical_data.shape[0] < 2:
            raise ValueError("历史数据至少需要2条记录")
        
        self.historical_data = historical_data.copy()
        
        # 数据标准化处理（除了余弦相似度，其他算法都需要标准化）
        if self.method != 'cosine':
            self.scaled_historical = self.scaler.fit_transform(historical_data)
        
        # 马哈拉诺比斯距离需要计算协方差矩阵的逆矩阵
        if self.method == 'mahalanobis':
            try:
                cov_matrix = np.cov(historical_data.T)
                self.cov_inv = np.linalg.inv(cov_matrix)
            except np.linalg.LinAlgError:
                warnings.warn("协方差矩阵不可逆，将使用伪逆矩阵")
                self.cov_inv = np.linalg.pinv(np.cov(historical_data.T))
    
    def find_similar_lines(self, new_line_features: np.ndarray, top_k: int = 5) -> Tuple[np.ndarray, np.ndarray]:
        """
        找到最相似的K条历史线路
        
        Args:
            new_line_features (np.ndarray): 新线路特征向量 [风速, 覆冰, 回路数, 导线规格]
            top_k (int): 返回最相似的前K条线路，默认为5
            
        Returns:
            Tuple[np.ndarray, np.ndarray]: 
                - 最相似线路的索引数组（按相似度降序排列）
                - 对应的相似度分数数组
        """
        if self.historical_data is None:
            raise ValueError("请先调用fit()方法训练历史数据")
        
        if len(new_line_features) != self.historical_data.shape[1]:
            raise ValueError(f"新线路特征维度({len(new_line_features)})与历史数据维度({self.historical_data.shape[1]})不匹配")
        
        # 根据选择的算法计算相似度
        if self.method == 'euclidean':
            similarities = self._euclidean_similarity(new_line_features)
        elif self.method == 'cosine':
            similarities = self._cosine_similarity(new_line_features)
        elif self.method == 'manhattan':
            similarities = self._manhattan_similarity(new_line_features)
        elif self.method == 'weighted':
            similarities = self._weighted_similarity(new_line_features)
        elif self.method == 'mahalanobis':
            similarities = self._mahalanobis_similarity(new_line_features)
        
        # 获取top-k最相似的索引（按相似度降序排列）
        top_k = min(top_k, len(similarities))
        top_indices = np.argsort(similarities)[-top_k:][::-1]
        top_similarities = similarities[top_indices]
        
        return top_indices, top_similarities
    
    def _euclidean_similarity(self, new_features: np.ndarray) -> np.ndarray:
        """
        欧几里得距离相似度计算
        
        欧几里得距离是最常用的距离度量方法，计算多维空间中两点间的直线距离。
        距离越小表示越相似，这里转换为相似度分数（0-1之间，越大越相似）。
        
        公式: distance = sqrt(sum((x_i - y_i)^2))
        相似度 = 1 / (1 + distance)
        
        Args:
            new_features (np.ndarray): 新线路特征向量
            
        Returns:
            np.ndarray: 相似度分数数组
        """
        new_scaled = self.scaler.transform(new_features.reshape(1, -1))[0]
        distances = np.sqrt(np.sum((self.scaled_historical - new_scaled) ** 2, axis=1))
        return 1 / (1 + distances)
    
    def _cosine_similarity(self, new_features: np.ndarray) -> np.ndarray:
        """
        余弦相似度计算
        
        余弦相似度通过计算两个向量夹角的余弦值来度量相似性。
        它关注的是向量的方向而不是大小，适合比例关系重要的场景。
        
        公式: cosine_sim = (A·B) / (||A|| * ||B||)
        取值范围: [-1, 1]，1表示完全相同，-1表示完全相反，0表示正交
        
        Args:
            new_features (np.ndarray): 新线路特征向量
            
        Returns:
            np.ndarray: 余弦相似度分数数组
        """
        new_features_2d = new_features.reshape(1, -1)
        similarities = cosine_similarity(new_features_2d, self.historical_data)[0]
        # 将[-1,1]范围映射到[0,1]范围
        return (similarities + 1) / 2

    def _manhattan_similarity(self, new_features: np.ndarray) -> np.ndarray:
        """
        曼哈顿距离相似度计算

        曼哈顿距离（也称为L1距离或城市街区距离）计算各维度差值的绝对值之和。
        相比欧几里得距离，曼哈顿距离对异常值不敏感，在某些场景下更稳健。

        公式: distance = sum(|x_i - y_i|)
        相似度 = 1 / (1 + distance)

        Args:
            new_features (np.ndarray): 新线路特征向量

        Returns:
            np.ndarray: 相似度分数数组
        """
        new_scaled = self.scaler.transform(new_features.reshape(1, -1))[0]
        distances = np.sum(np.abs(self.scaled_historical - new_scaled), axis=1)
        return 1 / (1 + distances)

    def _weighted_similarity(self, new_features: np.ndarray) -> np.ndarray:
        """
        加权欧几里得距离相似度计算

        在标准欧几里得距离基础上，为不同特征分配不同的权重。
        这允许根据工程经验或业务需求强调某些特征的重要性。
        例如：风速和覆冰可能比回路数更重要。

        公式: weighted_distance = sqrt(sum(w_i * (x_i - y_i)^2))
        其中 w_i 是第i个特征的权重

        Args:
            new_features (np.ndarray): 新线路特征向量

        Returns:
            np.ndarray: 加权相似度分数数组

        Raises:
            ValueError: 如果未设置权重
        """
        if self.weights is None:
            raise ValueError("使用加权算法时必须设置权重参数")

        if len(self.weights) != len(new_features):
            raise ValueError(f"权重维度({len(self.weights)})与特征维度({len(new_features)})不匹配")

        new_scaled = self.scaler.transform(new_features.reshape(1, -1))[0]
        # 应用权重到差值的平方
        weighted_diff_squared = (self.scaled_historical - new_scaled) ** 2 * self.weights
        distances = np.sqrt(np.sum(weighted_diff_squared, axis=1))
        return 1 / (1 + distances)

    def _mahalanobis_similarity(self, new_features: np.ndarray) -> np.ndarray:
        """
        马哈拉诺比斯距离相似度计算

        马哈拉诺比斯距离考虑了特征间的协方差关系，能够处理特征间相关性的影响。
        当特征间存在相关性时，该距离比欧几里得距离更准确。
        它实际上是在协方差标准化空间中的欧几里得距离。

        公式: distance = sqrt((x-y)^T * Σ^(-1) * (x-y))
        其中 Σ^(-1) 是协方差矩阵的逆矩阵

        Args:
            new_features (np.ndarray): 新线路特征向量

        Returns:
            np.ndarray: 马哈拉诺比斯相似度分数数组
        """
        distances = []
        for hist_point in self.historical_data:
            try:
                dist = mahalanobis(new_features, hist_point, self.cov_inv)
                distances.append(dist)
            except Exception as e:
                # 如果计算失败，使用一个较大的距离值
                warnings.warn(f"马哈拉诺比斯距离计算失败: {e}")
                distances.append(float('inf'))

        distances = np.array(distances)
        # 处理无穷大值
        distances = np.where(np.isinf(distances), np.max(distances[~np.isinf(distances)]) * 2, distances)
        return 1 / (1 + distances)


def demonstrate_algorithms():
    """
    演示各种匹配算法的使用方法

    该函数展示了如何使用不同的匹配算法来找到相似的历史线路。
    包含了模拟的历史数据和新线路数据，以及各算法的比较结果。
    """
    print("=" * 60)
    print("电力线路匹配算法演示")
    print("=" * 60)

    # 模拟历史线路数据
    # 特征顺序：[风速(m/s), 覆冰厚度(mm), 回路数, 导线规格编号]
    historical_data = np.array([
        [30.0, 10.0, 2, 1],  # 线路1: 风速30m/s, 覆冰10mm, 2回路, 导线规格1
        [25.0, 15.0, 1, 2],  # 线路2: 风速25m/s, 覆冰15mm, 1回路, 导线规格2
        [35.0, 5.0, 3, 1],   # 线路3: 风速35m/s, 覆冰5mm, 3回路, 导线规格1
        [28.0, 12.0, 2, 2],  # 线路4: 风速28m/s, 覆冰12mm, 2回路, 导线规格2
        [32.0, 8.0, 1, 3],   # 线路5: 风速32m/s, 覆冰8mm, 1回路, 导线规格3
        [26.0, 18.0, 2, 1],  # 线路6: 风速26m/s, 覆冰18mm, 2回路, 导线规格1
        [29.0, 11.0, 3, 2],  # 线路7: 风速29m/s, 覆冰11mm, 3回路, 导线规格2
        [33.0, 7.0, 2, 3],   # 线路8: 风速33m/s, 覆冰7mm, 2回路, 导线规格3
    ])

    # 新建线路的特征
    new_line = np.array([28.5, 12.5, 2, 1])
    print(f"新建线路特征: 风速={new_line[0]}m/s, 覆冰={new_line[1]}mm, 回路数={new_line[2]}, 导线规格={new_line[3]}")
    print()

    # 测试不同的匹配算法
    algorithms = [
        ('euclidean', '欧几里得距离', {}),
        ('cosine', '余弦相似度', {}),
        ('manhattan', '曼哈顿距离', {}),
        ('weighted', '加权距离', {'weights': [0.4, 0.4, 0.1, 0.1]}),  # 风速和覆冰权重更高
        ('mahalanobis', '马哈拉诺比斯距离', {}),
    ]

    # 对每种算法进行测试
    for method, name, kwargs in algorithms:
        print(f"【{name}算法】")
        print("-" * 40)

        try:
            # 创建匹配器实例
            matcher = LineMatchingAlgorithm(method=method, **kwargs)
            matcher.fit(historical_data)

            # 找到最相似的5条线路
            similar_indices, similarities = matcher.find_similar_lines(new_line, top_k=5)

            print("最相似的5条历史线路:")
            for i, (idx, sim) in enumerate(zip(similar_indices, similarities)):
                hist_line = historical_data[idx]
                print(f"  {i+1}. 线路{idx+1}: 风速={hist_line[0]}m/s, 覆冰={hist_line[1]}mm, "
                      f"回路数={hist_line[2]}, 导线规格={hist_line[3]} (相似度: {sim:.4f})")

        except Exception as e:
            print(f"  算法执行出错: {e}")

        print()


class LineMatchingSystem:
    """
    电力线路匹配系统

    这是一个完整的线路匹配系统，提供了便捷的接口来管理历史数据和执行匹配查询。
    支持多种匹配算法，并提供详细的匹配结果分析。
    """

    def __init__(self):
        """初始化匹配系统"""
        self.historical_data = None
        self.feature_names = ['风速(m/s)', '覆冰厚度(mm)', '回路数', '导线规格']
        self.line_ids = None

    def load_historical_data(self, data: np.ndarray, line_ids: Optional[List[str]] = None):
        """
        加载历史线路数据

        Args:
            data (np.ndarray): 历史线路数据矩阵
            line_ids (List[str], optional): 线路ID列表，如果不提供则自动生成
        """
        self.historical_data = data.copy()

        if line_ids is None:
            self.line_ids = [f"线路{i+1}" for i in range(len(data))]
        else:
            if len(line_ids) != len(data):
                raise ValueError("线路ID数量与数据行数不匹配")
            self.line_ids = line_ids.copy()

    def find_similar_lines(self, new_line_features: np.ndarray,
                          algorithm: str = 'euclidean',
                          weights: Optional[List[float]] = None,
                          top_k: int = 5) -> dict:
        """
        查找相似线路

        Args:
            new_line_features (np.ndarray): 新线路特征
            algorithm (str): 匹配算法
            weights (List[float], optional): 特征权重（仅用于加权算法）
            top_k (int): 返回最相似的前K条线路

        Returns:
            dict: 包含匹配结果的详细信息
        """
        if self.historical_data is None:
            raise ValueError("请先加载历史数据")

        # 创建匹配器
        matcher = LineMatchingAlgorithm(method=algorithm, weights=weights)
        matcher.fit(self.historical_data)

        # 执行匹配
        similar_indices, similarities = matcher.find_similar_lines(new_line_features, top_k)

        # 构建结果
        results = {
            'algorithm': algorithm,
            'new_line_features': new_line_features,
            'matches': []
        }

        for idx, sim in zip(similar_indices, similarities):
            match_info = {
                'line_id': self.line_ids[idx],
                'index': idx,
                'features': self.historical_data[idx],
                'similarity': sim,
                'feature_details': {}
            }

            # 添加特征详情
            for name, value in zip(self.feature_names, self.historical_data[idx]):
                match_info['feature_details'][name] = value

            results['matches'].append(match_info)

        return results

    def print_matching_results(self, results: dict):
        """
        打印匹配结果

        Args:
            results (dict): find_similar_lines返回的结果字典
        """
        print(f"使用算法: {results['algorithm']}")
        print(f"新线路特征: {dict(zip(self.feature_names, results['new_line_features']))}")
        print("\n匹配结果:")
        print("-" * 80)

        for i, match in enumerate(results['matches']):
            print(f"{i+1}. {match['line_id']} (相似度: {match['similarity']:.4f})")
            for name, value in match['feature_details'].items():
                print(f"   {name}: {value}")
            print()


if __name__ == "__main__":
    """
    主程序入口

    演示电力线路匹配算法的使用方法，包括：
    1. 基本算法演示
    2. 匹配系统使用示例
    """
    print("电力线路匹配算法演示程序")
    print("=" * 60)

    # 演示基本算法
    demonstrate_algorithms()

    print("\n" + "=" * 60)
    print("匹配系统使用示例")
    print("=" * 60)

    # 创建匹配系统实例
    system = LineMatchingSystem()

    # 加载示例数据
    sample_data = np.array([
        [30.0, 10.0, 2, 1],
        [25.0, 15.0, 1, 2],
        [35.0, 5.0, 3, 1],
        [28.0, 12.0, 2, 2],
        [32.0, 8.0, 1, 3],
    ])

    line_names = ["华北线路A", "华东线路B", "西北线路C", "华南线路D", "东北线路E"]
    system.load_historical_data(sample_data, line_names)

    # 新线路特征
    new_line = np.array([29.0, 11.0, 2, 1])

    # 使用加权算法进行匹配
    results = system.find_similar_lines(
        new_line,
        algorithm='weighted',
        weights=[0.4, 0.4, 0.15, 0.05],  # 风速和覆冰更重要
        top_k=3
    )

    # 打印结果
    system.print_matching_results(results)
