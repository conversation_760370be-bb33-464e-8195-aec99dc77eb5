# 功能模块说明

1. 总述：现在历史线路工程已经形成了本体费用指标（44个）、其他费用指标（26个）的单公里量，基于这些历史工程的每个指标的单公里量做为基准，进行对应数据操作。
2. 对历史工程每个指标项单公里量进行查询，可做为工程造价参考。
3. 将历史工程每个指标项单公里做为基准，根据不同边界条件（风速、覆冰、回路数、导线截面（导线规格），汇总出校审规则库，支撑对现有线路工程的各项指标数据校审。
4. 将历史工程每个指标项单公里做为基准，根据不同边界条件（风速、覆冰、回路数、导线截面（导线规格），组合出工程区域内的多个边界段，形成快速组（量）价方案。

## 历史指标库模块

1. 模块概述：该模块有本体费用指标、其他费用指标的查询页面，用户在对应页面可以进行历史工程指标的查询、导出、维护操作。
2. 业务操作流程
   1. 根据用户输入的查询条件（如线路工程名称、回路数、风速、覆冰、指标名称）进行历史工程本体费用指标、其他费用指标查询。
   2. 系统管理员在历史工程的本体费用指标页面，点击规则提炼，可对所有本体费用指标数据进行汇总，按边界条件（风速、覆冰、回路数、导线截面（导线规格）汇总出每类边界条件的上限、下限值，形成校审规则库，推送到预置的规则库。
3. 预置规则或数据
   1. 本体费用指标数据：项目各气象区-本体费用指标表-指标单公里量_fake.xlsx
   2. 其他费用指标数据：项目合并气象区-其他费用指标表-指标单公里量_fake.xlsx”的数据；
4. 参考原型图：1.历史指标库原型界面.png



## 智能辅助校审模块

1. 模块概述

   1. 用户上传每个工程的概算文件（excel文件），首先基于预置好的excel指标提取算法（规则），提取出每个工程的指标数据；
   2. 然后基于预置好的指标校审算法（规则）和历史指标数据，对每个工程提取的指标数据进行校审，校审不通过的指标项及数据进行展示；
   3. 最后可以导出校审报告。
2. 业务操作流程

   1. 用户进入“我的线路工程”页面可以查看到自己的工程工作区。工作区中对工程清单展示三个状态：导入状态（工程概算文件是否已导入）、指标提取状态（工程概算文件的指标项及数据是否已提取）、指标校审状态（工程概算文件指标项及数据是否已校审）

   1. 用户在工程工作区创建待校审工程，导入状态显示为未导入。
   2. 双击待校审工程，点击导入按钮，进入上传页面，进行工程概算文件上传操作，上传成功后，导入状态显示为已导入。可支持再次导入，再次导入后，该工程已提取指标和已校审数据会被清空。
   3. 校审工程导入成功后，指标提取状态显示为未提取，点击提取按钮，进入提取页面，进行工程概算文件本体费用指标、其他费用指标提取操作，提取成功后，指标提取状态显示为已提取。同时点击导出按钮，可导出已提取指标数据。
   4. 校审工程指标提取成功后，指标校审状态显示为未校审，点击校审按钮，进入校审页面，进行工程概算文件已提取指标数据校审操作，校审成功后，指标校审状态显示为已校审。同时点击导出按钮，可导出已校审数据及校审情况。
3. 预置规则或数据

   1. 指标提取算法（规则），展示“输电指标提取规则.xlsx”文件规则
   2. 指标校审算法（规则），根据历史指标数据，按边界条件（风速、覆冰、回路数、导线截面（导线规格）汇总出每类边界条件的上限、下限值，形成校审规则库；然后待校审工程根据边界条件匹配规则库对应校审规则，根据校审规则的各个指标的数据区间，对校审工程的指标数据进行校审，校审工程相应指标数据不在校审规则的数据区间内，则进行标红提示。
4. 参考原型图：2.智能辅助校审原型界面.png



## 快速组（量）价模块

1. 模块概述：

   1. 用户在快速组（量）价页面，按边界条件（即每一个特征段）进行历史工程指标数据查询和组合；基于历史工程每个指标的单公里数据基准，勾选对应的历史工程指标数据放到待组合区域，待组合完所有边界条件后，对待组合区域的数据进行指标数据量的汇总，再输入每个指标的价格，形成最后组价方案。

2. 业务操作流程

   1. 用户在快速组（量）价页面，按边界条件（即每一个特征段）进行历史工程指标数据查询（不限制边界条件组合的次数）
   2. 首先按边界条件1（风速：27，覆冰：0，回路数：双回路，导线界面：JL/LB20A 720/50）进行查询，获取到匹配的历史工程；然后勾选某个工程放到数据待汇总区域，作为该边界条件的单公里指标基准1；
   3. 然后按边界条件2（风速：27，覆冰：5，回路数：双回路，导线界面：JL/LB20A 720/50）进行查询，获取到匹配的历史工程；然后勾选某个工程放到数据待汇总区域，作为该边界条件的单公里指标基准2；
   4. 然后再按边界条件3（风速：30，覆冰：10，回路数：双回路，导线界面：JL/LB20A 720/50）进行查询，获取到匹配的历史工程；然后勾选某个工程放到数据待汇总区域，作为该边界条件的单公里指标基准3；
   5. 对数据待汇总区的历史工程，对选出来的每个边界条件的单公里指标基准，分别输入每个边界条件的线路长度，然后算出每个边界条件的指标总量（指标总量=线路长度*单公里指标量）
   6. 对数据待汇总区的历史工程，进行所有边界条件的指标量汇总，得出一个线路段的每个指标的总量。
   7. 在每个指标总量中输入指标的价格，得到最终的总价方案，然后进行导出。

3. 预置规则或数据

   1. 项目各气象区-本体费用指标表-指标单公里量_fake.xlsx

4. 参考原型图：3.快速组（量）价原型界面.png








工程名称关键词:

工程编号:

时间段:

所属省份:

跨越城市:

电压等级:

线路总长度:

回路数:

导线截面:

风速范围:

覆冰范围:

涵盖地形:

特殊跨区:

​	地形跨越

​	地物跨越

​	铁路跨越

​	高压跨越

特殊设备: