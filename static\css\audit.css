/* 项目列表样式 */
.project-section {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.query-form {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
    align-items: flex-end;
    padding: 1rem;
    background: white;
}

.form-group {
    display: flex;
    flex-direction: column;
    flex: 0 0 200px;
}

.form-group label {
    margin-bottom: 0.5rem;
    color: var(--text-color);
    white-space: nowrap;
}

.form-group input,
.form-group select {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.9rem;
    width: 100%;
}

.button-group {
    display: flex;
    gap: 0.5rem;
    margin-left: auto;
    white-space: nowrap;
}

.table-container {
    flex: 1;
    overflow: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin: 0 1rem 1rem;
    background: white;
}

.project-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.project-table th,
.project-table td {
    padding: 0.75rem 1rem;
    text-align: left;
    border: 1px solid #ddd;
    white-space: nowrap;
}

.project-table th {
    background-color: #f5f5f5;
    font-weight: 500;
    position: sticky;
    top: 0;
    z-index: 1;
    text-align: center;
}

.project-table td {
    text-align: left;
}

.project-table tbody tr:hover {
    background-color: #f5f5f5;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-buttons .btn {
    padding: 0.35rem 0.75rem;
    font-size: 0.9rem;
}

.pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: white;
    border-top: 1px solid #ddd;
    margin: 0 1rem;
}

.pagination button {
    padding: 0.35rem 0.75rem;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 0.9rem;
}

.pagination button:hover {
    background: #f5f5f5;
}

.pagination button.active {
    background: #1890ff;
    color: white;
    border-color: #1890ff;
}

/* 状态标签样式 */
.status-tag {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.85rem;
    display: inline-block;
    min-width: 64px;
    text-align: center;
}

.status-success {
    background-color: #e6f7e6;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.status-warning {
    background-color: #fff7e6;
    color: #fa8c16;
    border: 1px solid #ffd591;
}

.status-default {
    background-color: #f5f5f5;
    color: #8c8c8c;
    border: 1px solid #d9d9d9;
}

/* 对话框样式 */
.indicator-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.indicator-modal.show {
    display: block;
}

.indicator-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
}

.close {
    position: absolute;
    right: 20px;
    top: 10px;
    font-size: 24px;
    cursor: pointer;
    color: #999;
    transition: color 0.3s;
    line-height: 1;
    padding: 5px;
}

.close:hover {
    color: #666;
}

/* 指标表格样式 */
.indicator-tabs {
    display: flex;
    border-bottom: none;
    margin-bottom: 20px;
    gap: 10px;
}

.indicator-tab {
    padding: 8px 20px;
    border: 1px solid #1890ff;
    background: #fff;
    cursor: pointer;
    font-size: 14px;
    color: #333;
    border-radius: 4px;
    transition: all 0.3s;
}

.indicator-tab:hover {
    background-color: #e6f7ff;
}

.indicator-tab.active {
    color: #fff;
    background-color: #1890ff;
    border-color: #1890ff;
}

.indicator-panel {
    display: none;
    background-color: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 0 0 4px 4px;
    padding: 20px;
}

.indicator-panel.active {
    display: block;
}

.indicator-table-wrapper {
    max-height: 60vh;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.indicator-table {
    width: 100%;
    border-collapse: collapse;
}

.indicator-table th,
.indicator-table td {
    padding: 12px;
    text-align: left;
    border: 1px solid #ddd;
}

.indicator-table th {
    background-color: #f5f5f5;
    font-weight: 500;
    position: sticky;
    top: 0;
    z-index: 1;
}

.indicator-table tbody tr:hover {
    background-color: #f5f5f5;
}

/* 预览表格样式 */
.preview-table td:last-child {
    text-align: right;
}

.preview-table th:nth-child(1),
.preview-table td:nth-child(1) {
    width: 60px;
    min-width: 60px;
}

.preview-table th:nth-child(2),
.preview-table td:nth-child(2) {
    width: 200px;
    min-width: 200px;
    text-align: left;
}

.preview-table th:nth-child(3),
.preview-table td:nth-child(3) {
    width: 60px;
    min-width: 60px;
    text-align: center;
}

.preview-table th:nth-child(4),
.preview-table td:nth-child(4) {
    width: 80px;
    min-width: 80px;
    text-align: right;
}

/* 校审表格样式 */
.review-table td:nth-child(4) {
    text-align: right;
}

.review-table td:nth-child(5) {
    text-align: center;
}

/* 校审明细列样式 */
.review-table td:nth-child(6) {
    max-width: 400px;
    white-space: normal;
    word-wrap: break-word;
    line-height: 1.5;
}

/* 表格列宽控制 */
.review-table th:nth-child(1),
.review-table td:nth-child(1) {
    width: 60px;
    min-width: 60px;
    max-width: 60px;
}

.review-table th:nth-child(2),
.review-table td:nth-child(2) {
    width: 180px;
    min-width: 180px;
    max-width: 180px;
    white-space: normal;
    word-wrap: break-word;
}

.review-table th:nth-child(3),
.review-table td:nth-child(3) {
    width: 80px;
    min-width: 80px;
    max-width: 80px;
    text-align: center;
}

.review-table th:nth-child(4),
.review-table td:nth-child(4) {
    width: 100px;
    min-width: 100px;
    max-width: 100px;
    text-align: right;
}

.review-table th:nth-child(5),
.review-table td:nth-child(5) {
    width: 100px;
    min-width: 100px;
    max-width: 100px;
    text-align: center;
}

.review-normal {
    color: #52c41a;
    padding: 4px 8px;
    border-radius: 4px;
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
}

.review-abnormal {
    color: #ff4d4f;
    padding: 4px 8px;
    border-radius: 4px;
    background-color: #fff2f0;
    border: 1px solid #ffccc7;
}

/* 按钮样式 */
.btn-success {
    background-color: #52c41a;
    color: white;
    border-color: #52c41a;
}

.btn-success:hover {
    background-color: #389e0d;
    border-color: #389e0d;
}

.btn-disabled {
    opacity: 0.6;
    cursor: not-allowed !important;
    background-color: #d9d9d9 !important;
    border-color: #d9d9d9 !important;
    color: rgba(0, 0, 0, 0.25) !important;
}

.btn-disabled:hover {
    opacity: 0.6;
    background-color: #d9d9d9 !important;
    border-color: #d9d9d9 !important;
    color: rgba(0, 0, 0, 0.25) !important;
}

/* Toast 样式 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 4px;
    color: white;
    font-size: 14px;
    z-index: 9999;
    animation: slideIn 0.3s ease-in-out;
}

.toast.success {
    background-color: #4CAF50;
}

.toast.error {
    background-color: #f44336;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 文件上传样式 */
.upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 60px;
    text-align: center;
    margin: 40px 0;
    cursor: pointer;
    transition: all 0.3s;
    background-color: #fafafa;
}

.upload-area:hover {
    border-color: #1890ff;
    background-color: #f0f8ff;
}

.upload-area.dragover {
    border-color: #1890ff;
    background-color: #f0f8ff;
}

.upload-area p {
    margin: 0;
    font-size: 16px;
    color: #666;
}

/* 进度消息样式 */
.progress-messages {
    max-height: 300px;
    overflow-y: auto;
    padding: 20px;
    background: #f5f5f5;
    border-radius: 4px;
}

.progress-message {
    margin-bottom: 10px;
    padding: 8px;
    border-radius: 4px;
    background: white;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.progress-message.success {
    color: #52c41a;
    border-left: 4px solid #52c41a;
}

.progress-message i {
    margin-right: 8px;
}

/* 新增线路工程表单样式 */
.add-project-form {
    padding: 20px 0;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.add-project-form .form-group {
    flex: 1;
}

.add-project-form label {
    display: block;
    margin-bottom: 8px;
    color: #333;
}

.add-project-form input,
.add-project-form select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f5f5f5;
}

.form-actions {
    margin-top: 20px;
    text-align: right;
    gap: 10px;
    display: flex;
    justify-content: flex-end;
}

/* 指标预览界面样式 */
.preview-tabs {
    display: flex;
    border-bottom: none;
    margin-bottom: 20px;
    gap: 10px;
}

.preview-tab {
    padding: 8px 20px;
    border: 1px solid #1890ff;
    background: #fff;
    cursor: pointer;
    font-size: 14px;
    color: #333;
    border-radius: 4px;
    transition: all 0.3s;
}

.preview-tab:hover {
    background-color: #e6f7ff;
}

.preview-tab.active {
    color: #fff;
    background-color: #1890ff;
    border-color: #1890ff;
}

.preview-panel {
    display: none;
    background-color: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 0 0 4px 4px;
    padding: 20px;
}

.preview-panel.active {
    display: block;
}

/* 校审预览界面样式 */
.review-tabs {
    display: flex;
    border-bottom: none;
    margin-bottom: 20px;
    gap: 10px;
}

.review-tab {
    padding: 8px 20px;
    border: 1px solid #1890ff;
    background: #fff;
    cursor: pointer;
    font-size: 14px;
    color: #333;
    border-radius: 4px;
    transition: all 0.3s;
}

.review-tab:hover {
    background-color: #e6f7ff;
}

.review-tab.active {
    color: #fff;
    background-color: #1890ff;
    border-color: #1890ff;
}

.review-panel {
    display: none;
    background-color: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 0 0 4px 4px;
    padding: 20px;
}

.review-panel.active {
    display: block;
} 