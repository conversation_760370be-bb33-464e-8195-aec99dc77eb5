#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
审计项目数据迁移脚本
从 project_data.json 迁移数据到 t_audit_projects 表
"""

import os
import sqlite3
import json
from datetime import datetime

def migrate_project_data():
    """从project_data.json迁移数据到t_audit_projects表"""
    db_path = os.path.join('data', 'te_manage.db')
    json_path = os.path.join('data', 'project_data.json')
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        print("请先运行 init_te_manage_db.py 创建数据库")
        return False
        
    if not os.path.exists(json_path):
        print(f"❌ JSON数据文件不存在: {json_path}")
        return False
    
    try:
        # 读取JSON数据
        with open(json_path, 'r', encoding='utf-8') as f:
            projects_data = json.load(f)
        
        print(f"📖 读取到 {len(projects_data)} 条项目数据")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='t_audit_projects'")
        if not cursor.fetchone():
            print("❌ t_audit_projects 表不存在，请先运行 init_te_manage_db.py")
            return False
        
        # 询问是否清空现有数据
        cursor.execute('SELECT COUNT(*) FROM t_audit_projects')
        existing_count = cursor.fetchone()[0]
        
        if existing_count > 0:
            print(f"⚠️ 表中已存在 {existing_count} 条记录")
            response = input("是否清空现有数据？(y/N): ").strip().lower()
            if response in ['y', 'yes']:
                cursor.execute('DELETE FROM t_audit_projects')
                print("🗑️ 已清空现有数据")
        
        # 插入数据
        inserted_count = 0
        updated_count = 0
        
        for i, project in enumerate(projects_data):
            # 生成project_id（如果没有序号，使用索引+1）
            project_id = project.get('序号', i + 1)
            
            # 准备插入数据
            insert_data = (
                project.get('序号'),
                project_id,
                project.get('工程名称', ''),
                project.get('线路段名称', ''),
                project.get('电压等级', ''),
                project.get('线路总长度'),
                project.get('回路数', ''),
                project.get('风速'),
                project.get('覆冰'),
                project.get('导线规格', ''),
                project.get('导入状态', '未导入'),
                project.get('提取状态', '未提取'),
                project.get('校审状态', '未校审'),
                '分',  # 气象区默认为'分'
                project.get('创建时间'),
                project.get('校审时间') if project.get('校审时间') else None
            )
            
            # 检查记录是否已存在
            cursor.execute('SELECT COUNT(*) FROM t_audit_projects WHERE project_id = ?', (project_id,))
            exists = cursor.fetchone()[0] > 0
            
            cursor.execute('''
                INSERT OR REPLACE INTO t_audit_projects (
                    序号, project_id, 工程名称, 线路段名称, 电压等级, 线路总长度,
                    回路数, 风速, 覆冰, 导线规格, 导入状态, 提取状态, 校审状态,
                    气象区, 创建时间, 校审时间
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', insert_data)
            
            if exists:
                updated_count += 1
            else:
                inserted_count += 1
        
        conn.commit()
        
        print(f"✅ 数据迁移成功:")
        print(f"   - 新增记录: {inserted_count} 条")
        print(f"   - 更新记录: {updated_count} 条")
        print(f"   - 总计处理: {inserted_count + updated_count} 条")
        
        # 验证迁移结果
        cursor.execute('SELECT COUNT(*) FROM t_audit_projects')
        total_count = cursor.fetchone()[0]
        print(f"   - 表中总记录数: {total_count} 条")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据迁移失败: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
        
    finally:
        if 'conn' in locals():
            conn.close()

def show_migration_preview():
    """显示迁移预览"""
    json_path = os.path.join('data', 'project_data.json')
    
    if not os.path.exists(json_path):
        print(f"❌ JSON数据文件不存在: {json_path}")
        return
    
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            projects_data = json.load(f)
        
        print(f"\n📋 迁移预览 (共 {len(projects_data)} 条记录):")
        print("-" * 80)
        
        for i, project in enumerate(projects_data[:5]):  # 只显示前5条
            project_id = project.get('序号', i + 1)
            print(f"记录 {i+1}:")
            print(f"  project_id: {project_id}")
            print(f"  工程名称: {project.get('工程名称', '')}")
            print(f"  线路段名称: {project.get('线路段名称', '')}")
            print(f"  电压等级: {project.get('电压等级', '')}")
            print(f"  校审状态: {project.get('校审状态', '未校审')}")
            print(f"  气象区: 分 (默认)")
            print()
        
        if len(projects_data) > 5:
            print(f"... 还有 {len(projects_data) - 5} 条记录")
            
    except Exception as e:
        print(f"❌ 读取数据失败: {e}")

if __name__ == '__main__':
    print("📊 审计项目数据迁移工具")
    print("=" * 50)
    
    # 显示迁移预览
    show_migration_preview()
    
    # 确认是否执行迁移
    response = input("\n是否执行数据迁移？(y/N): ").strip().lower()
    if response in ['y', 'yes']:
        print("\n🚀 开始数据迁移...")
        if migrate_project_data():
            print("\n🎉 数据迁移完成！")
        else:
            print("\n💥 数据迁移失败！")
    else:
        print("\n❌ 取消数据迁移")
