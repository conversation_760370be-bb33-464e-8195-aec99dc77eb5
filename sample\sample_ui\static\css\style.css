/* 技术经济成本平台样式表 - 苹果设计风格 */

/* 基础样式 */
:root {
    --primary-color: #0071e3;
    --primary-hover: #0077ed;
    --secondary-color: #86868b;
    --text-color: #1d1d1f;
    --light-gray: #f5f5f7;
    --border-color: #d2d2d7;
    --success-color: #34c759;
    --warning-color: #ff9500;
    --error-color: #ff3b30;
    --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
    color: var(--text-color);
    line-height: 1.5;
    background-color: #fff;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 排版 */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 0.5em;
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.5rem;
}

p {
    margin-bottom: 1rem;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-hover);
}

/* 头部 */
header {
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: saturate(180%) blur(20px);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--box-shadow);
    padding: 15px 0;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    font-size: 1.5rem;
    margin-bottom: 0;
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 30px;
}

nav ul li a {
    color: var(--text-color);
    font-weight: 500;
    padding-bottom: 5px;
    border-bottom: 2px solid transparent;
}

nav ul li a:hover,
nav ul li a.active {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
}

/* 主要内容 */
main {
    padding: 40px 0;
    min-height: calc(100vh - 180px);
}

/* 页脚 */
footer {
    background-color: var(--light-gray);
    padding: 20px 0;
    text-align: center;
}

/* 按钮 */
.btn {
    display: inline-block;
    padding: 10px 20px;
    border-radius: 980px;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    outline: none;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    color: white;
}

.btn-secondary {
    background-color: var(--light-gray);
    color: var(--text-color);
}

.btn-secondary:hover {
    background-color: #e5e5e5;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-color);
}

.btn-outline:hover {
    background-color: var(--light-gray);
}

/* 表单 */
.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

input, select, textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 16px;
    transition: var(--transition);
}

input:focus, select:focus, textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 113, 227, 0.2);
}

/* 卡片 */
.card {
    background-color: white;
    border-radius: 12px;
    box-shadow: var(--box-shadow);
    padding: 20px;
    margin-bottom: 20px;
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

/* 步骤导航 */
.step-nav {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    position: relative;
}

.step-nav::before {
    content: '';
    position: absolute;
    top: 25px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--border-color);
    z-index: -1;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 1;
}

.step-number {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--light-gray);
    border: 2px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-bottom: 10px;
    transition: var(--transition);
}

.step.active .step-number {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.step.completed .step-number {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

/* 表格 */
.table-container {
    overflow-x: auto;
    margin-bottom: 20px;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    background-color: var(--light-gray);
    font-weight: 600;
}

tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* 文件上传 */
.file-upload {
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    margin-bottom: 20px;
    cursor: pointer;
    transition: var(--transition);
}

.file-upload:hover {
    border-color: var(--primary-color);
}

.file-upload-icon {
    font-size: 48px;
    color: var(--secondary-color);
    margin-bottom: 15px;
}

/* 提示框 */
.alert {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.alert-success {
    background-color: rgba(52, 199, 89, 0.1);
    border-left: 4px solid var(--success-color);
    color: #2a9d4a;
}

.alert-warning {
    background-color: rgba(255, 149, 0, 0.1);
    border-left: 4px solid var(--warning-color);
    color: #cc7800;
}

.alert-error {
    background-color: rgba(255, 59, 48, 0.1);
    border-left: 4px solid var(--error-color);
    color: #cc2f26;
}

/* 模态框 */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.modal-backdrop.show {
    opacity: 1;
    visibility: visible;
}

.modal {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    transform: translateY(-20px);
    transition: var(--transition);
}

.modal-backdrop.show .modal {
    transform: translateY(0);
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
}

.modal-footer button {
    margin-left: 10px;
}

.close-modal {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--secondary-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    header .container {
        flex-direction: column;
    }
    
    nav ul {
        margin-top: 15px;
    }
    
    nav ul li {
        margin-left: 15px;
        margin-right: 15px;
    }
    
    .step-nav {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .step-nav::before {
        display: none;
    }
    
    .step {
        flex-direction: row;
        margin-bottom: 15px;
    }
    
    .step-number {
        margin-bottom: 0;
        margin-right: 15px;
    }
}

/* 首页特定样式 */
.hero-section {
    text-align: center;
    padding: 60px 0;
}

.hero-section h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.subtitle {
    font-size: 1.2rem;
    color: var(--secondary-color);
    margin-bottom: 30px;
}

.features-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.feature-card {
    background-color: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: var(--box-shadow);
    text-align: center;
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    margin-bottom: 20px;
    color: var(--primary-color);
}

.feature-card h2 {
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.feature-card p {
    color: var(--secondary-color);
    margin-bottom: 25px;
}

.info-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.info-card {
    background-color: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: var(--box-shadow);
}

.info-card h3 {
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.info-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.info-list li {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.info-icon {
    margin-right: 15px;
    color: var(--primary-color);
}

.info-text {
    font-weight: 500;
}

/* 方案展示样式 */
.schemes-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.scheme-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    background-color: #fff;
    overflow: hidden;
    transition: all 0.3s ease;
}

.scheme-card.selected {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.scheme-header {
    padding: 15px 20px;
    background-color: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e0e0e0;
}

.scheme-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

.scheme-title h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.scheme-badge {
    background-color: #28a745;
    color: white;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.scheme-summary {
    display: flex;
    gap: 20px;
}

.scheme-price, .scheme-unit-price {
    font-size: 14px;
}

.scheme-price strong, .scheme-unit-price strong {
    font-size: 16px;
    color: #d9534f;
}

.scheme-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.scheme-body {
    padding: 20px;
}

.scheme-body h5 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 500;
    color: #555;
}

.module-cost-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.module-cost-item {
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #6c757d;
}

.module-cost-item.highlight {
    border-left-color: #007bff;
    background-color: #e7f1ff;
}

.module-cost-name {
    font-size: 14px;
    color: #555;
    margin-bottom: 5px;
}

.module-cost-value {
    font-size: 15px;
    font-weight: 500;
    color: #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.module-option {
    font-size: 12px;
    color: #007bff;
    font-weight: normal;
    background-color: rgba(0, 123, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
}

@media (max-width: 992px) {
    .scheme-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .scheme-summary {
        width: 100%;
        justify-content: space-between;
    }
    
    .scheme-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .module-cost-grid {
        grid-template-columns: 1fr;
    }
}

/* 详情模态框样式 */
.detail-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
}

.detail-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.detail-section h4 {
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 500;
    color: #333;
}

.detail-section p {
    margin: 0 0 10px 0;
    line-height: 1.5;
    color: #555;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 15px;
}

.detail-item {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 12px 15px;
}

.detail-label {
    font-size: 13px;
    color: #666;
    margin-bottom: 5px;
}

.detail-value {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.detail-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.detail-table th {
    background-color: #f1f3f5;
    padding: 10px;
    text-align: left;
    font-weight: 500;
    border-bottom: 2px solid #dee2e6;
}

.detail-table td {
    padding: 10px;
    border-bottom: 1px solid #e9ecef;
}

.detail-table .highlight-row {
    background-color: #e7f1ff;
}

.detail-table .total-row {
    font-weight: 600;
    background-color: #f8f9fa;
}

.detail-table .total-row td {
    border-top: 2px solid #dee2e6;
} 