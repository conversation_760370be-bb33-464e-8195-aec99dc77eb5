-- 创建本体费用计算逻辑表 (SQLite格式)
CREATE TABLE IF NOT EXISTS t_benti_ontology (
    id INTEGER PRIMARY KEY ,
    费用序号 INTEGER NOT NULL,
    费用名称 TEXT NOT NULL,
    计算逻辑 TEXT NOT NULL,
    费用关系 TEXT NOT NULL,
    创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
    更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引提高查询性能
CREATE INDEX IF NOT EXISTS idx_benti_ontology_xuhao ON t_benti_ontology(费用序号);
CREATE INDEX IF NOT EXISTS idx_benti_ontology_name ON t_benti_ontology(费用名称);

-- 插入初始化数据
INSERT INTO t_benti_ontology (费用序号, 费用名称, 计算逻辑, 费用关系) VALUES
(1, '本体工程', '/', '/'),
(2, '价差', '本体*20%', '1*20%'),
(3, '其他', '本体*25%', '1*25%'),
(4, '其中：建设场地征用及清理费', '其他*35%', '3*35%'),
(5, '基本预备费', '（本体+价差+其他）*1.5%', '(1+2+3)*1.5%'),
(6, '工程静态投资', '本体+价差+其他+基本预备费', '1+2+3+5'),
(7, '建设期贷款利息', '工程静态投资*3.55%*0.5*0.8', '6*3.55%*0.5*0.8'),
(8, '工程动态投资', '工程静态投资+建设期贷款利息', '6+7');

-- 查询验证数据
SELECT * FROM t_benti_ontology ORDER BY 费用序号;
