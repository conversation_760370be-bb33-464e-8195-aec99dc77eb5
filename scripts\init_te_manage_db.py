#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速组价模块数据库初始化脚本
创建 te_manage.db 数据库和相关表结构
"""

import os
import sqlite3
import json
from datetime import datetime

def create_te_manage_database():
    """创建快速组价管理数据库和表结构"""
    db_path = os.path.join('data', 'te_manage.db')
    
    # 确保data目录存在
    os.makedirs('data', exist_ok=True)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 1. 创建工程管理表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS t_pricing_projects (
                序号 INTEGER PRIMARY KEY,
                project_id INTEGER UNIQUE NOT NULL,
                工程名称 TEXT NOT NULL,
                电压等级 TEXT,
                线路总长度 REAL,
                特征段数量 INTEGER DEFAULT 0,
                组价状态 TEXT NOT NULL DEFAULT '未组价',
                创建时间 DATETIME,
                组价时间 DATETIME
            )
        ''')
        
        # 2. 创建特征段管理表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS t_pricing_fetures (
                序号 INTEGER PRIMARY KEY,
                project_id INTEGER NOT NULL,
                feture_id INTEGER UNIQUE NOT NULL,
                特征段名称 TEXT NOT NULL,
                线路长度 REAL,
                风速 REAL,
                覆冰 REAL,
                回路数 TEXT,
                导线规格 TEXT,
                平地 REAL DEFAULT 0,
                丘陵 REAL DEFAULT 0,
                山地 REAL DEFAULT 0,
                高山 REAL DEFAULT 0,
                峻岭 REAL DEFAULT 0,
                泥沼 REAL DEFAULT 0,
                河网 REAL DEFAULT 0,
                气象区 TEXT DEFAULT '分',
                组价状态 TEXT NOT NULL DEFAULT '未组价',
                创建时间 DATETIME,
                更新时间 DATETIME,
                FOREIGN KEY (project_id) REFERENCES t_pricing_projects(project_id)
            )
        ''')
        
        # 3. 创建分气象区组价结果表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS t_pricing_feture_result_fen (
                id INTEGER PRIMARY KEY,
                feature_id INTEGER NOT NULL,
                project_id INTEGER NOT NULL,
                特征段名称 TEXT NOT NULL,
                计算数据 TEXT NOT NULL,
                创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (feature_id) REFERENCES t_pricing_fetures(feture_id),
                FOREIGN KEY (project_id) REFERENCES t_pricing_projects(project_id)
            )
        ''')
        
        # 4. 创建合并气象区组价结果表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS t_pricing_feture_result_he (
                id INTEGER PRIMARY KEY,
                feature_id INTEGER NOT NULL,
                project_id INTEGER NOT NULL,
                特征段名称 TEXT NOT NULL,
                计算数据 TEXT NOT NULL,
                创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (feature_id) REFERENCES t_pricing_fetures(feature_id),
                FOREIGN KEY (project_id) REFERENCES t_pricing_projects(project_id)
            )
        ''')

        # 5. 创建审计项目表 (基于project_data.json结构)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS t_audit_projects (
                序号 INTEGER PRIMARY KEY,
                project_id INTEGER UNIQUE NOT NULL,
                工程名称 TEXT NOT NULL,
                线路段名称 TEXT,
                电压等级 TEXT,
                线路总长度 REAL,
                回路数 TEXT,
                风速 REAL,
                覆冰 REAL,
                导线规格 TEXT,
                导入状态 TEXT DEFAULT '未导入',
                提取状态 TEXT DEFAULT '未提取',
                校审状态 TEXT DEFAULT '未校审',
                气象区 TEXT DEFAULT '分',
                创建时间 DATETIME,
                校审时间 DATETIME
            )
        ''')
        
        # 创建索引提高查询性能
        indexes = [
            'CREATE INDEX IF NOT EXISTS idx_projects_project_id ON t_pricing_projects(project_id)',
            'CREATE INDEX IF NOT EXISTS idx_fetures_project_id ON t_pricing_fetures(project_id)',
            'CREATE INDEX IF NOT EXISTS idx_result_fen_feature_id ON t_pricing_feture_result_fen(feature_id)',
            'CREATE INDEX IF NOT EXISTS idx_result_he_feature_id ON t_pricing_feture_result_he(feature_id)',
            'CREATE INDEX IF NOT EXISTS idx_audit_projects_project_id ON t_audit_projects(project_id)',
            'CREATE INDEX IF NOT EXISTS idx_audit_projects_status ON t_audit_projects(校审状态)'
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        conn.commit()
        print(f"✅ 数据库创建成功: {db_path}")
        print("✅ 表结构创建完成:")
        print("   - t_pricing_projects (工程管理表)")
        print("   - t_pricing_fetures (特征段管理表)")
        print("   - t_pricing_feture_result_fen (分气象区组价结果表)")
        print("   - t_pricing_feture_result_he (合并气象区组价结果表)")
        print("   - t_audit_projects (审计项目表)")
        print("✅ 索引创建完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库创建失败: {e}")
        conn.rollback()
        return False
        
    finally:
        conn.close()

def verify_database():
    """验证数据库表结构"""
    db_path = os.path.join('data', 'te_manage.db')
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        expected_tables = [
            't_pricing_projects',
            't_pricing_fetures',
            't_pricing_feture_result_fen',
            't_pricing_feture_result_he',
            't_audit_projects'
        ]
        
        print("\n📋 数据库验证结果:")
        for table in expected_tables:
            if table in tables:
                print(f"   ✅ {table}")
            else:
                print(f"   ❌ {table} (缺失)")
        
        return all(table in tables for table in expected_tables)
        
    except Exception as e:
        print(f"❌ 数据库验证失败: {e}")
        return False
        
    finally:
        conn.close()

def migrate_project_data():
    """从project_data.json迁移数据到t_audit_projects表"""
    db_path = os.path.join('data', 'te_manage.db')
    json_path = os.path.join('data', 'project_data.json')

    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False

    if not os.path.exists(json_path):
        print(f"❌ JSON数据文件不存在: {json_path}")
        return False

    try:
        # 读取JSON数据
        with open(json_path, 'r', encoding='utf-8') as f:
            projects_data = json.load(f)

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 清空现有数据（可选）
        cursor.execute('DELETE FROM t_audit_projects')

        # 插入数据
        inserted_count = 0
        for project in projects_data:
            # 生成project_id（如果没有序号，使用索引+1）
            project_id = project.get('序号', inserted_count + 1)

            # 准备插入数据
            insert_data = (
                project.get('序号'),
                project_id,
                project.get('工程名称', ''),
                project.get('线路段名称', ''),
                project.get('电压等级', ''),
                project.get('线路总长度'),
                project.get('回路数', ''),
                project.get('风速'),
                project.get('覆冰'),
                project.get('导线规格', ''),
                project.get('导入状态', '未导入'),
                project.get('提取状态', '未提取'),
                project.get('校审状态', '未校审'),
                '分',  # 气象区默认为'分'
                project.get('创建时间'),
                project.get('校审时间') if project.get('校审时间') else None
            )

            cursor.execute('''
                INSERT OR REPLACE INTO t_audit_projects (
                    序号, project_id, 工程名称, 线路段名称, 电压等级, 线路总长度,
                    回路数, 风速, 覆冰, 导线规格, 导入状态, 提取状态, 校审状态,
                    气象区, 创建时间, 校审时间
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', insert_data)

            inserted_count += 1

        conn.commit()
        print(f"✅ 数据迁移成功: 共导入 {inserted_count} 条记录")
        return True

    except Exception as e:
        print(f"❌ 数据迁移失败: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False

    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == '__main__':
    print("🚀 开始创建快速组价管理数据库...")

    if create_te_manage_database():
        if verify_database():
            print("\n🎉 数据库初始化完成！")

            # 询问是否进行数据迁移
            print("\n📊 开始数据迁移...")
            if migrate_project_data():
                print("\n🎉 数据迁移完成！")
            else:
                print("\n⚠️ 数据迁移失败，但数据库结构已创建")
        else:
            print("\n⚠️ 数据库验证失败，请检查表结构")
    else:
        print("\n💥 数据库初始化失败！")
