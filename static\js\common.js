/**
 * 快速组价模块公共函数库
 * 包含各页面通用的工具函数
 */

// Toast提示函数
window.showToast = function(message, type = 'info') {
    console.log(`显示Toast: [${type}] ${message}`);
    // 创建一个toast元素
    const toast = document.createElement('div');
    toast.className = `pricing-shared-toast ${type || 'info'}`;
    toast.textContent = message;
    
    // 设置样式
    toast.style.position = 'fixed';
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '9999';
    toast.style.padding = '12px 24px';
    toast.style.background = type === 'success' ? '#f6ffed' : (type === 'error' ? '#fff2f0' : '#e6f7ff');
    toast.style.border = `1px solid ${type === 'success' ? '#b7eb8f' : (type === 'error' ? '#ffccc7' : '#91d5ff')}`;
    toast.style.color = type === 'success' ? '#52c41a' : (type === 'error' ? '#ff4d4f' : '#1890ff');
    toast.style.borderRadius = '4px';
    toast.style.boxShadow = '0 3px 6px rgba(0, 0, 0, 0.16)';
    toast.style.display = 'block';
    toast.style.opacity = '1';
    toast.style.visibility = 'visible';
    toast.style.pointerEvents = 'auto';
    
    // 添加到body
    document.body.appendChild(toast);
    console.log('Toast元素已添加到DOM');
    
    // 3秒后自动移除
    setTimeout(() => {
        console.log('准备移除Toast元素');
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
            console.log('Toast元素已移除');
        }
    }, 3000);
};

// 工作区面板展开函数
window.expandWorkspacePanels = function() {
    const workspaceContent = document.querySelector('.pricing-ws-content, .workspace-content');
    if (workspaceContent) {
        workspaceContent.classList.remove('panels-collapsed');
        workspaceContent.classList.add('panels-expanded');
        console.log('工作区面板已展开');
    } else {
        console.warn('找不到工作区内容元素');
    }
};

// 工作区面板折叠函数
window.collapseWorkspacePanels = function() {
    const workspaceContent = document.querySelector('.pricing-ws-content, .workspace-content');
    if (workspaceContent) {
        workspaceContent.classList.add('panels-collapsed');
        workspaceContent.classList.remove('panels-expanded');
        console.log('工作区面板已折叠');
    } else {
        console.warn('找不到工作区内容元素');
    }
};

// 显示/隐藏加载状态
window.toggleLoading = function(show) {
    // 防止重复创建
    if (show) {
        let existingOverlay = document.querySelector('.pricing-shared-loading-overlay');
        if (existingOverlay) {
            return; // 如果已存在，则不重复创建
        }
        
        const loadingOverlay = document.createElement('div');
        loadingOverlay.className = 'pricing-shared-loading-overlay';
        loadingOverlay.innerHTML = '<div class="pricing-shared-loading-spinner"></div>';
        loadingOverlay.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(255,255,255,0.7); display: flex; justify-content: center; align-items: center; z-index: 9999;';
        
        const spinner = loadingOverlay.querySelector('.pricing-shared-loading-spinner');
        if (spinner) {
            spinner.style.cssText = 'width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #0071e3; border-radius: 50%; animation: spin 1s linear infinite;';
        }
        
        document.body.appendChild(loadingOverlay);
        
        // 添加动画样式
        const styleElement = document.createElement('style');
        styleElement.textContent = '@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }';
        document.head.appendChild(styleElement);
    } else {
        const existingOverlay = document.querySelector('.pricing-shared-loading-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }
    }
};

console.log('快速组价共用JS文件已加载'); 