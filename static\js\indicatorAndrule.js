/**
 * 指标及规则管理模块 JavaScript
 * 负责页面切换、菜单交互等功能
 */

// 页面切换功能
document.addEventListener('DOMContentLoaded', function() {
    const menuItems = document.querySelectorAll('.indicator-rule-menu-item');
    const pages = document.querySelectorAll('.indicator-rule-page');
    const contentTitle = document.getElementById('content-title');
    const contentDescription = document.getElementById('content-description');

    // 页面配置
    const pageConfig = {
        'indicator': {
            title: '指标管理',
            description: '管理和维护系统中的各类指标数据，支持指标的增删改查操作。'
        },
        'price': {
            title: '单价管理',
            description: '管理和维护工程单价数据，支持单价信息的录入和更新。'
        },
        'ontology': {
            title: '本体计算逻辑',
            description: '配置和管理本体费用的计算逻辑和规则。'
        },
        'audit': {
            title: '校审数据库',
            description: '管理校审相关的数据库配置和数据源。'
        }
    };

    /**
     * 切换到指定的页面标签
     * @param {string} tabName - 标签名称
     */
    function switchToTab(tabName) {
        // 更新菜单状态
        menuItems.forEach(menu => menu.classList.remove('active'));
        const targetMenuItem = document.querySelector(`[data-tab="${tabName}"]`);
        if (targetMenuItem) {
            targetMenuItem.classList.add('active');
        }
        
        // 更新页面显示
        pages.forEach(page => page.classList.remove('active'));
        const targetPage = document.getElementById(tabName + '-page');
        if (targetPage) {
            targetPage.classList.add('active');
        }
        
        // 更新标题和描述
        const config = pageConfig[tabName];
        if (config && contentTitle && contentDescription) {
            contentTitle.textContent = config.title;
            contentDescription.textContent = config.description;
        }
    }

    /**
     * 初始化菜单点击事件
     */
    function initMenuEvents() {
        menuItems.forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const tabName = this.getAttribute('data-tab');
                if (tabName) {
                    switchToTab(tabName);
                    // 更新URL参数（可选）
                    updateUrlParams(tabName);
                }
            });
        });
    }

    /**
     * 更新URL参数
     * @param {string} tabName - 标签名称
     */
    function updateUrlParams(tabName) {
        if (history.pushState) {
            const url = new URL(window.location);
            url.searchParams.set('tab', tabName);
            history.pushState(null, '', url);
        }
    }

    /**
     * 根据URL参数设置默认页面
     */
    function setDefaultTab() {
        const urlParams = new URLSearchParams(window.location.search);
        const activeTab = urlParams.get('tab') || 'indicator';
        
        if (activeTab && activeTab !== 'indicator') {
            switchToTab(activeTab);
        }
    }

    /**
     * 初始化模块
     */
    function init() {
        initMenuEvents();
        setDefaultTab();
    }

    // 启动初始化
    init();
});

/**
 * 全局方法：切换到指定标签页
 * 可供外部调用
 * @param {string} tabName - 标签名称
 */
window.switchIndicatorRuleTab = function(tabName) {
    const event = new CustomEvent('switchTab', { detail: { tabName: tabName } });
    document.dispatchEvent(event);
};

// ==================== 指标管理页面功能已迁移到 indicator_management.js ====================
// 该部分功能已迁移到独立的 indicator_management.js 文件中
// 如需使用指标管理功能，请确保在HTML中引入 indicator_management.js 文件






