# 电网线路工程造价分析平台（原型）

## 项目简介
本项目为电网线路工程造价分析平台的Web原型，基于Flask框架，采用Jinja2模板，前端使用Tailwind CSS，页面全部为简体中文。

## 目录结构

```
TEPrototype/
├── app.py                # Flask主程序入口
├── requirements.txt      # Python依赖包列表
├── static/               # 静态资源目录（如logo、css、js等）
├── templates/            # Jinja2模板目录（所有页面HTML）
├── sample_data/          # 示例数据（Excel等）
├── README.md             # 项目说明
└── ...
```

## 启动方式

1. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```
2. 启动服务：
   ```bash
   python app.py
   ```
3. 浏览器访问：
   - 首页：http://127.0.0.1:5000/
   - 其他页面可通过导航栏跳转

## 说明
- 所有页面均为原型，数据为静态或示例数据。
- 如需二次开发，请在`app.py`中补充后端逻辑。
- 静态资源请放置于`static/`目录。
- 页面模板请放置于`templates/`目录。

#### 介绍
技经原型

#### 软件架构
软件架构说明


#### 安装教程

1.  xxxx
2.  xxxx
3.  xxxx

#### 使用说明

1.  xxxx
2.  xxxx
3.  xxxx

#### 参与贡献

1.  Fork 本仓库
2.  新建 Feat_xxx 分支
3.  提交代码
4.  新建 Pull Request


#### 特技

1.  使用 Readme\_XXX.md 来支持不同的语言，例如 Readme\_en.md, Readme\_zh.md
2.  Gitee 官方博客 [blog.gitee.com](https://blog.gitee.com)
3.  你可以 [https://gitee.com/explore](https://gitee.com/explore) 这个地址来了解 Gitee 上的优秀开源项目
4.  [GVP](https://gitee.com/gvp) 全称是 Gitee 最有价值开源项目，是综合评定出的优秀开源项目
5.  Gitee 官方提供的使用手册 [https://gitee.com/help](https://gitee.com/help)
6.  Gitee 封面人物是一档用来展示 Gitee 会员风采的栏目 [https://gitee.com/gitee-stars/](https://gitee.com/gitee-stars/)
