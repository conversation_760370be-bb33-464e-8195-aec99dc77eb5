# 电力线路匹配算法说明

## 概述

本项目实现了多种相似度匹配算法，用于在历史线路数据库中找到与新建线路最相似的历史线路。主要应用于根据边界条件（风速、覆冰、回路数、导线规格）进行线路匹配。

## 文件说明

### 1. `line_matching_algorithms.py` - 完整版本
- 依赖：numpy, scikit-learn, scipy
- 功能最完整，包含所有算法实现
- 提供了完整的匹配系统类

### 2. `simple_line_matching.py` - 简化版本
- 无外部依赖，仅使用Python标准库
- 实现了主要的匹配算法
- 可以直接运行测试

### 3. `matching_requirements.txt` - 依赖包列表
- 运行完整版本所需的Python包

## 支持的匹配算法

### 1. 欧几里得距离 (Euclidean Distance)
**原理**: 计算多维空间中两点之间的直线距离
**公式**: `distance = sqrt(sum((x_i - y_i)^2))`
**优点**: 简单直观，计算快速
**缺点**: 对特征量纲敏感，需要标准化
**适用场景**: 通用场景，特征重要性相等时

### 2. 余弦相似度 (Cosine Similarity)
**原理**: 计算两个向量夹角的余弦值，关注方向而非大小
**公式**: `cosine_sim = (A·B) / (||A|| * ||B||)`
**优点**: 不受特征量纲影响，适合比例关系重要的场景
**缺点**: 忽略了向量的大小信息
**适用场景**: 关注特征比例关系时

### 3. 曼哈顿距离 (Manhattan Distance)
**原理**: 计算各维度差值的绝对值之和
**公式**: `distance = sum(|x_i - y_i|)`
**优点**: 对异常值不敏感
**缺点**: 可能不如欧几里得距离精确
**适用场景**: 数据中存在异常值时

### 4. 加权距离 (Weighted Distance)
**原理**: 根据特征重要性分配不同权重
**公式**: `weighted_distance = sqrt(sum(w_i * (x_i - y_i)^2))`
**优点**: 可以突出重要特征
**缺点**: 需要人工设定权重
**适用场景**: 已知特征重要性差异时

### 5. 马哈拉诺比斯距离 (Mahalanobis Distance) - 仅完整版
**原理**: 考虑特征间协方差的距离度量
**公式**: `distance = sqrt((x-y)^T * Σ^(-1) * (x-y))`
**优点**: 考虑特征间相关性
**缺点**: 计算复杂，需要足够的历史数据
**适用场景**: 特征间存在相关性时

## 使用方法

### 快速开始（简化版本）

```python
from simple_line_matching import SimpleLineMatching

# 历史线路数据：[风速, 覆冰, 回路数, 导线规格]
historical_data = [
    [30.0, 10.0, 2, 1],
    [25.0, 15.0, 1, 2],
    [35.0, 5.0, 3, 1],
    # ... 更多数据
]

# 新线路特征
new_line = [28.5, 12.5, 2, 1]

# 创建匹配器（使用欧几里得距离）
matcher = SimpleLineMatching(method='euclidean')
matcher.fit(historical_data)

# 找到最相似的5条线路
indices, similarities = matcher.find_similar_lines(new_line, top_k=5)

# 输出结果
for i, (idx, sim) in enumerate(zip(indices, similarities)):
    print(f"第{i+1}相似: 线路{idx+1}, 相似度: {sim:.4f}")
```

### 使用加权算法

```python
# 设置权重：风速和覆冰更重要
weights = [0.4, 0.4, 0.1, 0.1]  # [风速, 覆冰, 回路数, 导线规格]

matcher = SimpleLineMatching(method='weighted', weights=weights)
matcher.fit(historical_data)
indices, similarities = matcher.find_similar_lines(new_line, top_k=5)
```

## 算法选择建议

### 1. 通用推荐
- **首选**: 加权欧几里得距离 - 可以根据工程经验设置特征权重
- **备选**: 标准化后的欧几里得距离 - 简单有效

### 2. 特殊场景
- **数据有异常值**: 使用曼哈顿距离
- **关注比例关系**: 使用余弦相似度
- **特征间有相关性**: 使用马哈拉诺比斯距离（完整版）

### 3. 权重设置建议
根据电力工程经验，建议权重分配：
- 风速：0.35-0.45（高重要性）
- 覆冰：0.35-0.45（高重要性）
- 回路数：0.05-0.15（中等重要性）
- 导线规格：0.05-0.15（中等重要性）

## 运行示例

### 运行简化版本
```bash
python simple_line_matching.py
```

### 运行完整版本
```bash
# 首先安装依赖
pip install -r matching_requirements.txt

# 然后运行
python line_matching_algorithms.py
```

## 输出示例

```
【加权距离算法】
----------------------------------------
最相似的5条历史线路:
  1. 线路4: 风速=28.0m/s, 覆冰=12.0mm, 回路数=2, 导线规格=2 (相似度: 0.8234)
  2. 线路1: 风速=30.0m/s, 覆冰=10.0mm, 回路数=2, 导线规格=1 (相似度: 0.7891)
  3. 线路7: 风速=29.0m/s, 覆冰=11.0mm, 回路数=3, 导线规格=2 (相似度: 0.7456)
  4. 线路6: 风速=26.0m/s, 覆冰=18.0mm, 回路数=2, 导线规格=1 (相似度: 0.6789)
  5. 线路2: 风速=25.0m/s, 覆冰=15.0mm, 回路数=1, 导线规格=2 (相似度: 0.6234)
```

## 注意事项

1. **数据预处理**: 除余弦相似度外，其他算法都需要对数据进行标准化处理
2. **权重设置**: 使用加权算法时，权重之和建议为1.0
3. **数据质量**: 历史数据应该具有代表性，避免极端值过多
4. **算法验证**: 建议使用已知相似的线路对算法进行验证和调优

## 扩展建议

1. **特征工程**: 可以考虑添加更多特征，如地理位置、海拔高度等
2. **组合算法**: 可以将多种算法的结果进行加权组合
3. **动态权重**: 根据不同的应用场景动态调整特征权重
4. **相似度阈值**: 设置最低相似度阈值，过滤不相关的结果
