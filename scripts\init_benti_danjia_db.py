#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本体指标单价数据库初始化脚本
创建t_benti_danjia表并生成随机单价数据
"""

import sqlite3
import os
import sys
import random
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def create_danjia_table():
    """创建本体指标单价表"""
    db_path = os.path.join('data', 'indicator_management.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        print("请先运行 init_indicator_db.py 创建数据库")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建本体指标单价表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS t_benti_danjia (
            id INTEGER PRIMARY KEY ,
            序号 INTEGER NOT NULL,
            指标名称 TEXT NOT NULL,
            指标单位 TEXT NOT NULL,
            指标分类 TEXT NOT NULL,
            指标单价 REAL NOT NULL,
            更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 创建索引提高查询性能
    cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_benti_danjia_xuhao ON t_benti_danjia(序号)
    ''')
    
    cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_benti_danjia_name ON t_benti_danjia(指标名称)
    ''')

    cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_benti_danjia_category ON t_benti_danjia(指标分类)
    ''')
    
    conn.commit()
    conn.close()
    print("本体指标单价表创建成功")
    return True

def get_price_range(unit):
    """根据单位确定单价范围"""
    price_ranges = {
        # 基础设施类 - 较高单价
        '基': (10000, 50000),
        
        # 材料类 - 中等单价（按吨计算）
        't': (5000, 25000),
        
        # 小件类 - 较低单价
        '套': (50, 500),
        '个': (20, 300),
        '支': (100, 800),
        
        # 工程量类 - 按立方米计价
        'm³': (100, 800),
        
        # 长度类 - 按米计价
        'm': (50, 200),
        
        # 百分比类 - 特殊计价
        '%': (1, 10)
    }
    
    return price_ranges.get(unit, (100, 1000))  # 默认范围

def generate_random_price(min_price, max_price):
    """生成随机单价，保留2位小数"""
    price = random.uniform(min_price, max_price)
    return round(price, 2)

def insert_danjia_data():
    """插入本体指标单价数据"""
    # 本体指标数据 - 包含序号、指标名称、单位、分类
    indicators = [
        (1, "直线塔", "基", "基础工程"),
        (2, "耐张塔", "基", "基础工程"),
        (3, "导线", "t", "架线工程"),
        (4, "塔材", "t", "杆塔工程"),
        (5, "基础钢材", "t", "基础工程"),
        (6, "地脚螺栓和插入式角钢", "t", "基础工程"),
        (7, "挂线金具", "t", "附件工程"),
        (8, "导线间隔棒", "套", "附件工程"),
        (9, "导线防振锤", "个", "附件工程"),
        (10, "地线防振锤", "个", "附件工程"),
        (11, "合成复合绝缘子", "支", "附件工程"),
        (12, "玻璃绝缘子/盘式绝缘子", "支", "附件工程"),
        (13, "硬跳", "套", "附件工程"),
        (14, "现浇混凝土", "m³", "基础工程"),
        (15, "灌柱桩基础混凝土", "m³", "基础工程"),
        (16, "基础护壁", "m³", "基础工程"),
        (17, "基础垫层", "m³", "基础工程"),
        (18, "钻孔灌注桩深度", "m", "基础工程"),
        (19, "护坡、挡土墙、排水沟", "m³", "辅助工程"),
        (20, "基坑土方（非机械）", "m³", "基础工程"),
        (21, "基坑土方（机械）", "m³", "基础工程"),
        (22, "接地槽", "m³", "接地工程"),
        (23, "排水沟", "m³", "辅助工程"),
        (24, "尖峰、基面", "m³", "辅助工程")
    ]
    
    db_path = os.path.join('data', 'indicator_management.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 清空现有数据
    cursor.execute('DELETE FROM t_benti_danjia')
    
    # 设置随机种子以确保可重现的结果
    random.seed(42)
    
    # 插入数据
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    for xuhao, name, unit, category in indicators:
        # 根据单位确定价格范围
        min_price, max_price = get_price_range(unit)

        # 生成随机单价
        price = generate_random_price(min_price, max_price)

        cursor.execute('''
            INSERT INTO t_benti_danjia (
                序号, 指标名称, 指标单位, 指标分类, 指标单价, 更新时间
            ) VALUES (?, ?, ?, ?, ?, ?)
        ''', (xuhao, name, unit, category, price, current_time))

        print(f"插入数据: {xuhao:2d} | {name:25s} | {unit:4s} | {category:8s} | {price:8.2f}元")
    
    conn.commit()
    conn.close()
    print(f"\n本体指标单价数据插入成功，共 {len(indicators)} 条记录")

def verify_data():
    """验证插入的数据"""
    db_path = os.path.join('data', 'indicator_management.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 查询数据统计
    cursor.execute('SELECT COUNT(*) FROM t_benti_danjia')
    count = cursor.fetchone()[0]
    
    cursor.execute('SELECT MIN(指标单价), MAX(指标单价), AVG(指标单价) FROM t_benti_danjia')
    min_price, max_price, avg_price = cursor.fetchone()
    
    print(f"\n数据验证结果:")
    print(f"总记录数: {count}")
    print(f"单价范围: {min_price:.2f} - {max_price:.2f} 元")
    print(f"平均单价: {avg_price:.2f} 元")
    
    # 显示前5条记录作为示例
    cursor.execute('''
        SELECT 序号, 指标名称, 指标单位, 指标分类, 指标单价, 更新时间
        FROM t_benti_danjia
        ORDER BY 序号
        LIMIT 5
    ''')

    print(f"\n前5条记录示例:")
    print(f"{'序号':<4} {'指标名称':<25} {'单位':<6} {'分类':<10} {'单价(元)':<10} {'更新时间'}")
    print("-" * 85)

    for row in cursor.fetchall():
        xuhao, name, unit, category, price, update_time = row
        print(f"{xuhao:<4} {name:<25} {unit:<6} {category:<10} {price:<10.2f} {update_time}")

    # 按分类统计数据
    cursor.execute('''
        SELECT 指标分类, COUNT(*) as 数量, AVG(指标单价) as 平均单价
        FROM t_benti_danjia
        GROUP BY 指标分类
        ORDER BY 指标分类
    ''')

    print(f"\n按分类统计:")
    print(f"{'分类':<12} {'数量':<6} {'平均单价(元)'}")
    print("-" * 35)

    for row in cursor.fetchall():
        category, count, avg_price = row
        print(f"{category:<12} {count:<6} {avg_price:<10.2f}")
    
    conn.close()

def main():
    """主函数"""
    print("开始初始化本体指标单价数据...")
    print("=" * 60)
    
    # 创建单价表
    if not create_danjia_table():
        return
    
    # 插入单价数据
    insert_danjia_data()
    
    # 验证数据
    verify_data()
    
    print("\n" + "=" * 60)
    print("本体指标单价数据初始化完成！")

if __name__ == '__main__':
    main()
