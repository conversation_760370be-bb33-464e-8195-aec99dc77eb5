<!-- 本体费用计算逻辑页面内容 -->
<div class="ontology-management-container">
    <!-- 查询条件区域 -->
    <div class="query-section">
        <div class="query-row">
            <div class="query-form">
                <div class="query-item">
                    <label>费用名称：</label>
                    <input type="text" id="ontology-name-input" placeholder="请输入费用名称" class="query-input">
                </div>
            </div>
            <div class="query-buttons">
                <button class="btn btn-primary" onclick="searchOntologyData()">查询</button>
                <button class="btn btn-secondary" onclick="resetOntologyForm()">重置</button>
                <button class="btn btn-success" onclick="addOntologyData()">新增</button>
            </div>
        </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
        <div class="table-container">
            <table class="data-table" id="ontology-table">
                <thead>
                    <tr>
                        <th style="width: 80px;">费用序号</th>
                        <th style="width: 200px;">费用名称</th>
                        <th style="width: 250px;">计算逻辑</th>
                        <th style="width: 200px;">费用关系</th>
                        <th style="width: 120px;">操作</th>
                    </tr>
                </thead>
                <tbody id="ontology-table-body">
                    <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- 分页控件 -->
    <div class="pagination-section">
        <div class="pagination-info">
            <span id="ontology-total-info">共 0 条记录</span>
        </div>
        <div class="pagination-controls">
            <button class="btn btn-secondary btn-sm" id="ontology-prev-btn" onclick="prevOntologyPage()">上一页</button>
            <span class="pagination-pages" id="ontology-pagination-pages"></span>
            <button class="btn btn-secondary btn-sm" id="ontology-next-btn" onclick="nextOntologyPage()">下一页</button>
        </div>
    </div>
</div>

<!-- 编辑模态框 -->
<div id="ontology-edit-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="ontology-modal-title">编辑费用信息</h3>
            <span class="close" onclick="closeOntologyEditModal()">&times;</span>
        </div>
        <div class="modal-body">
            <form id="ontology-edit-form">
                <input type="hidden" id="ontology-edit-id">

                <div class="form-group">
                    <label for="ontology-edit-sequence">费用序号：</label>
                    <input type="number" id="ontology-edit-sequence" class="form-input" required min="1">
                </div>

                <div class="form-group">
                    <label for="ontology-edit-name">费用名称：</label>
                    <input type="text" id="ontology-edit-name" class="form-input" required maxlength="100">
                </div>

                <div class="form-group">
                    <label for="ontology-edit-logic">计算逻辑：</label>
                    <textarea id="ontology-edit-logic" class="form-textarea" rows="4" required maxlength="200"></textarea>
                </div>

                <div class="form-group">
                    <label for="ontology-edit-relation">费用关系：</label>
                    <input type="text" id="ontology-edit-relation" class="form-input" required maxlength="200">
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeOntologyEditModal()">取消</button>
            <button type="button" class="btn btn-primary" onclick="saveOntologyData()">保存</button>
        </div>
    </div>
</div>
